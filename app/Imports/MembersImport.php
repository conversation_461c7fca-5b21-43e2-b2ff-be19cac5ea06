<?php

namespace App\Imports;

use App\Models\Member;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithChunkReading;

class MembersImport implements ToModel, WithHeadingRow, WithBatchInserts, WithChunkReading
{
    /**
    * @param array $row
    *
    * @return \Illuminate\Database\Eloquent\Model|null
    */
    public function model(array $row)
    {
        // Skip empty rows
        if (empty($row['name']) || trim($row['name']) === '') {
            return null;
        }

        // Convert categories string to array if present
        $categories = null;
        if (isset($row['categories']) && !empty($row['categories'])) {
            $categories = explode(',', $row['categories']);
            $categories = array_map('trim', $categories);
            $categories = array_filter($categories); // Remove empty values
        }

        // Convert emails string to array if present
        $emails = null;
        if (isset($row['emails']) && !empty($row['emails'])) {
            $emails = explode(',', $row['emails']);
            $emails = array_map('trim', $emails);
            $emails = array_filter($emails, function($email) {
                return !empty($email) && filter_var($email, FILTER_VALIDATE_EMAIL);
            });
        } elseif (isset($row['email']) && !empty(trim($row['email']))) {
            // Fallback to single email field for backward compatibility
            $email = trim($row['email']);
            if (filter_var($email, FILTER_VALIDATE_EMAIL)) {
                $emails = [$email];
            }
        }

        // Convert phones string to array if present
        $phones = null;
        if (isset($row['phones']) && !empty($row['phones'])) {
            $phones = explode(',', $row['phones']);
            $phones = array_map('trim', $phones);
            $phones = array_filter($phones); // Remove empty values
        } elseif (isset($row['phone']) && !empty(trim($row['phone']))) {
            // Fallback to single phone field for backward compatibility
            $phones = [trim($row['phone'])];
        }

        // Convert contact persons string to array if present
        $contactPersons = null;
        if (isset($row['contact_persons']) && !empty($row['contact_persons'])) {
            $contactPersons = [];
            $contacts = explode(';', $row['contact_persons']);
            foreach ($contacts as $contact) {
                $contact = trim($contact);
                if (!empty($contact)) {
                    $parts = explode(',', $contact);
                    if (count($parts) >= 1 && !empty(trim($parts[0]))) {
                        $contactPersons[] = [
                            'name' => trim($parts[0]),
                            'phone' => isset($parts[1]) ? trim($parts[1]) : null,
                        ];
                    }
                }
            }
        }

        // Validate required fields
        if (empty(trim($row['name'])) || empty(trim($row['address']))) {
            return null;
        }

        return new Member([
            'name' => trim($row['name']),
            'address' => trim($row['address']),
            'full_address' => isset($row['full_address']) ? trim($row['full_address']) : null,
            'description' => isset($row['description']) ? trim($row['description']) : null,
            'categories' => $categories,
            'emails' => $emails,
            'phones' => $phones,
            'website' => isset($row['website']) && !empty(trim($row['website'])) ? trim($row['website']) : null,
            'contact_persons' => $contactPersons,
            'is_active' => isset($row['is_active']) ? (bool)$row['is_active'] : true,
            'sort_order' => isset($row['sort_order']) && is_numeric($row['sort_order']) ? (int)$row['sort_order'] : 0,
        ]);
    }

    /**
     * @return int
     */
    public function batchSize(): int
    {
        return 100;
    }

    /**
     * @return int
     */
    public function chunkSize(): int
    {
        return 100;
    }
}
