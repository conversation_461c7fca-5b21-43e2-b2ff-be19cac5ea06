<?php

namespace App\Exports;

use App\Models\Member;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class MembersExport implements FromCollection, WithHeadings, WithMapping, WithStyles
{
    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        return Member::ordered()->get();
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'ID',
            'Name',
            'Address',
            'Full Address',
            'Description',
            'Categories',
            'Emails',
            'Phones',
            'Website',
            'Contact Persons',
            'Is Active',
            'Sort Order',
            'Created At',
            'Updated At',
        ];
    }

    /**
     * @param Member $member
     * @return array
     */
    public function map($member): array
    {
        return [
            $member->id,
            $member->name,
            $member->address,
            $member->full_address,
            $member->description,
            is_array($member->categories) ? implode(', ', $member->categories) : $member->categories,
            is_array($member->emails) ? implode(', ', $member->emails) : $member->emails,
            is_array($member->phones) ? implode(', ', $member->phones) : $member->phones,
            $member->website,
            $this->formatContactPersons($member->contact_persons),
            $member->is_active ? 'Yes' : 'No',
            $member->sort_order,
            $member->created_at ? $member->created_at->format('Y-m-d H:i:s') : '',
            $member->updated_at ? $member->updated_at->format('Y-m-d H:i:s') : '',
        ];
    }

    /**
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as bold text.
            1 => ['font' => ['bold' => true]],
        ];
    }

    /**
     * Format contact persons for export
     */
    private function formatContactPersons($contactPersons)
    {
        if (!is_array($contactPersons) || empty($contactPersons)) {
            return '';
        }

        $formatted = [];
        foreach ($contactPersons as $contact) {
            $name = $contact['name'] ?? '';
            $phone = $contact['phone'] ?? '';
            $formatted[] = $phone ? "{$name},{$phone}" : $name;
        }

        return implode(';', $formatted);
    }
}
