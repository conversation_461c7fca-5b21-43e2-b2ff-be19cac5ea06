<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class PresidentMessage extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'position',
        'message',
        'profile_image_path',
        'objectives',
        'is_active',
        'sort_order'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'sort_order' => 'integer',
        'objectives' => 'array' // Cast objectives to array for JSON storage
    ];

    /**
     * Scope to get only active messages
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to order by sort order
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order', 'asc');
    }

    /**
     * Get the current active president message
     */
    public function scopeCurrent($query)
    {
        return $query->active()->ordered()->first();
    }

    /**
     * Get the full profile image URL
     */
    public function getProfileImageUrlAttribute()
    {
        if ($this->profile_image_path) {
            return asset('storage/' . $this->profile_image_path);
        }
        return asset('image/default-president.jpg'); // fallback image
    }

    /**
     * Get formatted objectives as HTML list
     */
    public function getFormattedObjectivesAttribute()
    {
        if (!$this->objectives || !is_array($this->objectives)) {
            return '';
        }

        $html = '<ol>';
        foreach ($this->objectives as $objective) {
            $html .= '<li>' . e($objective) . '</li>';
        }
        $html .= '</ol>';

        return $html;
    }

    /**
     * Get message excerpt
     */
    public function getMessageExcerptAttribute()
    {
        return \Str::limit(strip_tags($this->message), 150);
    }
}
