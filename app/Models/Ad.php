<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Ad extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'type',
        'content_path',
        'thumbnail_path',
        'description',
        'link_url',
        'position',
        'status',
        'sort_order',
        'width',
        'height',
        'start_date',
        'end_date',
        'max_impressions',
        'impressions_count',
        'clicks_count',
        'open_in_new_tab',
        'target_pages',
        'alt_text'
    ];

    protected $casts = [
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'open_in_new_tab' => 'boolean',
        'target_pages' => 'array',
        'sort_order' => 'integer',
        'width' => 'integer',
        'height' => 'integer',
        'max_impressions' => 'integer',
        'impressions_count' => 'integer',
        'clicks_count' => 'integer'
    ];

    /**
     * Scope to get only active ads
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active')
                    ->where(function($q) {
                        $q->whereNull('start_date')
                          ->orWhere('start_date', '<=', now());
                    })
                    ->where(function($q) {
                        $q->whereNull('end_date')
                          ->orWhere('end_date', '>=', now());
                    })
                    ->where(function($q) {
                        $q->whereNull('max_impressions')
                          ->orWhereRaw('impressions_count < max_impressions');
                    });
    }

    /**
     * Scope to filter by position
     */
    public function scopeByPosition($query, $position)
    {
        return $query->where('position', $position);
    }

    /**
     * Scope to order by sort order
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order', 'asc')->orderBy('created_at', 'desc');
    }

    /**
     * Get the content URL
     */
    public function getContentUrlAttribute()
    {
        if ($this->content_path) {
            return asset('storage/' . $this->content_path);
        }
        return null;
    }

    /**
     * Get the thumbnail URL
     */
    public function getThumbnailUrlAttribute()
    {
        if ($this->thumbnail_path) {
            return asset('storage/' . $this->thumbnail_path);
        }
        return null;
    }

    /**
     * Check if ad is currently active
     */
    public function isActive()
    {
        if ($this->status !== 'active') {
            return false;
        }

        $now = now();

        if ($this->start_date && $this->start_date > $now) {
            return false;
        }

        if ($this->end_date && $this->end_date < $now) {
            return false;
        }

        if ($this->max_impressions && $this->impressions_count >= $this->max_impressions) {
            return false;
        }

        return true;
    }

    /**
     * Increment impression count
     */
    public function incrementImpressions()
    {
        $this->increment('impressions_count');
    }

    /**
     * Increment click count
     */
    public function incrementClicks()
    {
        $this->increment('clicks_count');
    }

    /**
     * Get click-through rate
     */
    public function getClickThroughRateAttribute()
    {
        if ($this->impressions_count == 0) {
            return 0;
        }
        return round(($this->clicks_count / $this->impressions_count) * 100, 2);
    }

    /**
     * Get status badge class
     */
    public function getStatusBadgeClassAttribute()
    {
        return match($this->status) {
            'active' => 'bg-success',
            'inactive' => 'bg-secondary',
            'scheduled' => 'bg-warning',
            default => 'bg-secondary'
        };
    }

    /**
     * Get position display name
     */
    public function getPositionDisplayAttribute()
    {
        return match($this->position) {
            'header' => 'Header',
            'sidebar' => 'Sidebar',
            'footer' => 'Footer',
            'content' => 'Content Area',
            'popup' => 'Popup',
            'banner' => 'Banner',
            default => ucfirst($this->position)
        };
    }
}
