<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Member extends Model
{
    protected $fillable = [
        'name',
        'address',
        'full_address',
        'description',
        'emails',
        'phones',
        'website',
        'contact_persons',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'emails' => 'array',
        'phones' => 'array',
        'contact_persons' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Scope a query to only include active members.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to order members by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }

    /**
     * Get the primary email address.
     */
    public function getPrimaryEmailAttribute()
    {
        return $this->emails && count($this->emails) > 0 ? $this->emails[0] : null;
    }

    /**
     * Get the primary phone number.
     */
    public function getPrimaryPhoneAttribute()
    {
        return $this->phones && count($this->phones) > 0 ? $this->phones[0] : null;
    }

    /**
     * Get all emails as a comma-separated string.
     */
    public function getEmailsStringAttribute()
    {
        return $this->emails ? implode(', ', $this->emails) : '';
    }

    /**
     * Get all phones as a comma-separated string.
     */
    public function getPhonesStringAttribute()
    {
        return $this->phones ? implode(', ', $this->phones) : '';
    }

    /**
     * Add an email address.
     */
    public function addEmail($email)
    {
        $emails = $this->emails ?? [];
        if (!in_array($email, $emails)) {
            $emails[] = $email;
            $this->emails = $emails;
        }
    }

    /**
     * Add a phone number.
     */
    public function addPhone($phone)
    {
        $phones = $this->phones ?? [];
        if (!in_array($phone, $phones)) {
            $phones[] = $phone;
            $this->phones = $phones;
        }
    }

    /**
     * Remove an email address.
     */
    public function removeEmail($email)
    {
        $emails = $this->emails ?? [];
        $emails = array_values(array_filter($emails, function($e) use ($email) {
            return $e !== $email;
        }));
        $this->emails = $emails;
    }

    /**
     * Remove a phone number.
     */
    public function removePhone($phone)
    {
        $phones = $this->phones ?? [];
        $phones = array_values(array_filter($phones, function($p) use ($phone) {
            return $p !== $phone;
        }));
        $this->phones = $phones;
    }
}
