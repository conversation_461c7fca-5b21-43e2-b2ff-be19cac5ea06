<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Quotation extends Model
{
    use HasFactory;
    
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'full_name',
        'email',
        'phone',
        'company_name',
        'products',
        'message',
        'terms_accepted',
        'processed'
    ];
    
    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'products' => 'array',
        'terms_accepted' => 'boolean',
        'processed' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];
    
    /**
     * Get the products as a comma-separated string.
     *
     * @return string
     */
    public function getProductsStringAttribute()
    {
        if (is_array($this->products)) {
            return implode(', ', $this->products);
        }
        return $this->products;
    }
    
    /**
     * Scope a query to only include processed quotations.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeProcessed($query)
    {
        return $query->where('processed', true);
    }
    
    /**
     * Scope a query to only include unprocessed quotations.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeUnprocessed($query)
    {
        return $query->where('processed', false);
    }
}
