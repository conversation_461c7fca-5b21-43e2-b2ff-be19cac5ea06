<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Ad;
use App\Services\AdsService;

class TestAdsSystem extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ads:test {--create-samples : Create sample ads for testing}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the ads system and create sample ads';

    protected $adsService;

    public function __construct(AdsService $adsService)
    {
        parent::__construct();
        $this->adsService = $adsService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🎯 Testing UIA Ads System');
        $this->newLine();

        // Show current ads count
        $adsCount = Ad::count();
        $this->info("📊 Current ads in database: {$adsCount}");

        if ($this->option('create-samples')) {
            $this->createSampleAds();
        }

        // Test ads retrieval
        $this->testAdsRetrieval();

        // Show ads by position
        $this->showAdsByPosition();

        $this->newLine();
        $this->info('✅ Ads system test completed!');
    }

    private function createSampleAds()
    {
        $this->info('🔧 Creating sample ads...');

        $sampleAds = [
            [
                'title' => 'UIA Header Banner',
                'type' => 'image',
                'content_path' => 'https://via.placeholder.com/800x200/007bff/ffffff?text=UIA+Header+Banner',
                'description' => 'Join the leading industrial association',
                'link_url' => 'https://example.com/join',
                'position' => 'header',
                'status' => 'active',
                'sort_order' => 1,
                'width' => 800,
                'height' => 200,
                'start_date' => now(),
                'end_date' => now()->addMonths(6),
                'max_impressions' => 10000,
                'open_in_new_tab' => true,
                'target_pages' => ['all'],
                'alt_text' => 'Join UIA'
            ],
            [
                'title' => 'Member Benefits',
                'type' => 'image',
                'content_path' => 'https://via.placeholder.com/400x250/28a745/ffffff?text=Member+Benefits',
                'description' => 'Discover exclusive member benefits',
                'link_url' => route('uia-members'),
                'position' => 'content',
                'status' => 'active',
                'sort_order' => 1,
                'width' => 400,
                'height' => 250,
                'start_date' => now(),
                'end_date' => now()->addMonths(12),
                'max_impressions' => 5000,
                'open_in_new_tab' => false,
                'target_pages' => ['home', 'about-us'],
                'alt_text' => 'Member Benefits'
            ],
            [
                'title' => 'Contact Footer Ad',
                'type' => 'image',
                'content_path' => 'https://via.placeholder.com/600x150/dc3545/ffffff?text=Contact+UIA+Today',
                'description' => 'Get in touch for business opportunities',
                'link_url' => route('contact'),
                'position' => 'footer',
                'status' => 'active',
                'sort_order' => 1,
                'width' => 600,
                'height' => 150,
                'start_date' => now(),
                'end_date' => now()->addYear(),
                'max_impressions' => 15000,
                'open_in_new_tab' => false,
                'target_pages' => ['all'],
                'alt_text' => 'Contact UIA'
            ]
        ];

        foreach ($sampleAds as $adData) {
            Ad::create($adData);
            $this->line("✓ Created: {$adData['title']} ({$adData['position']})");
        }

        $this->info('✅ Sample ads created successfully!');
        $this->newLine();
    }

    private function testAdsRetrieval()
    {
        $this->info('🔍 Testing ads retrieval...');

        $ads = $this->adsService->getAdsForPage('home');

        foreach ($ads as $position => $positionAds) {
            $count = $positionAds->count();
            $this->line("  {$position}: {$count} ads");
        }
    }

    private function showAdsByPosition()
    {
        $this->info('📍 Ads by position:');

        $positions = ['header', 'content', 'banner', 'footer', 'sidebar', 'popup'];

        foreach ($positions as $position) {
            $ads = Ad::where('position', $position)->where('status', 'active')->get();
            $this->line("  {$position}: {$ads->count()} ads");

            foreach ($ads as $ad) {
                $this->line("    - {$ad->title} (ID: {$ad->id})");
            }
        }
    }
}
