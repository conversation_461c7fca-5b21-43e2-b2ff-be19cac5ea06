<?php

namespace App\Services;

use App\Models\Ad;
use Illuminate\Support\Collection;

class AdsService
{
    /**
     * Get active ads by position
     */
    public function getAdsByPosition(string $position, string $currentPage = null): Collection
    {
        $query = Ad::where('status', 'active')
                   ->where('position', $position)
                   ->orderBy('sort_order', 'asc')
                   ->orderBy('created_at', 'desc');

        $ads = $query->get()->filter(function ($ad) use ($currentPage) {
            // Check if ad is currently active (date range, impressions, etc.)
            if (!$ad->isActive()) {
                return false;
            }

            // Check if ad should be shown on current page
            if ($currentPage && $ad->target_pages && !empty($ad->target_pages)) {
                return in_array($currentPage, $ad->target_pages) || in_array('all', $ad->target_pages);
            }

            return true;
        });

        // Increment impressions for displayed ads
        foreach ($ads as $ad) {
            $ad->incrementImpressions();
        }

        return $ads;
    }

    /**
     * Get all active ads for a specific page
     */
    public function getAdsForPage(string $currentPage = null): array
    {
        return [
            'header' => $this->getAdsByPosition('header', $currentPage),
            'sidebar' => $this->getAdsByPosition('sidebar', $currentPage),
            'footer' => $this->getAdsByPosition('footer', $currentPage),
            'content' => $this->getAdsByPosition('content', $currentPage),
            'banner' => $this->getAdsByPosition('banner', $currentPage),
            'popup' => $this->getAdsByPosition('popup', $currentPage),
        ];
    }

    /**
     * Record ad click
     */
    public function recordClick(int $adId): bool
    {
        $ad = Ad::find($adId);
        if ($ad && $ad->isActive()) {
            $ad->incrementClicks();
            return true;
        }
        return false;
    }

    /**
     * Get random ads by position (for rotation)
     */
    public function getRandomAdsByPosition(string $position, int $limit = 1, string $currentPage = null): Collection
    {
        $ads = $this->getAdsByPosition($position, $currentPage);
        return $ads->shuffle()->take($limit);
    }
}
