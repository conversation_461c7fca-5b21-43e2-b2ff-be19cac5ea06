<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use App\Models\User;
use App\Models\Member;
use App\Models\News;
use App\Models\Quotation;
use App\Models\ContactForm;
use App\Models\HeroSlider;
use App\Models\Gallery;

class AdminController extends Controller
{
    /**
     * Show the admin login form.
     */
    public function showLoginForm()
    {
        return view('admin.login');
    }

    /**
     * Handle admin login request.
     */
    public function login(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'password' => 'required|min:6',
        ]);

        $credentials = $request->only('email', 'password');

        if (Auth::attempt($credentials, $request->filled('remember'))) {
            $request->session()->regenerate();
            
            return redirect()->intended(route('admin.dashboard'))
                           ->with('success', 'Welcome to Admin Dashboard!');
        }

        return back()->withErrors([
            'email' => 'The provided credentials do not match our records.',
        ])->onlyInput('email');
    }

    /**
     * Show the admin dashboard.
     */
    public function dashboard()
    {
                // Get statistics
        $totalMembers = Member::count();
        $activeMembers = Member::active()->count();
        $totalNews = News::count();
        $activeNews = News::active()->count();
        $publishedNews = News::published()->count();
        $totalQuotations = Quotation::count();
        $unprocessedQuotations = Quotation::unprocessed()->count();
        $totalContactForms = ContactForm::count();
        $unprocessedContactForms = ContactForm::unprocessed()->count();
        $totalHeroSliders = HeroSlider::count();
        $activeHeroSliders = HeroSlider::active()->count();
        $totalGalleries = Gallery::count();
        $activeGalleries = Gallery::active()->count();

        // Get recent activities

        // Get recent activities for dashboard overview
        $recentMembers = Member::latest()->take(5)->get();
        $recentNews = News::latest()->take(5)->get();
        $recentQuotations = Quotation::latest()->take(5)->get();
        $recentContactForms = ContactForm::latest()->take(5)->get();
        // Get monthly statistics for charts
        $membersByMonth = Member::selectRaw('MONTH(created_at) as month, COUNT(*) as count')
            ->whereYear('created_at', date('Y'))
            ->groupBy('month')
            ->orderBy('month')
            ->get();

        $newsByMonth = News::selectRaw('MONTH(created_at) as month, COUNT(*) as count')
            ->whereYear('created_at', date('Y'))
            ->groupBy('month')
            ->orderBy('month')
            ->get();

        $quotationsByMonth = Quotation::selectRaw('MONTH(created_at) as month, COUNT(*) as count')
            ->whereYear('created_at', date('Y'))
            ->groupBy('month')
            ->orderBy('month')
            ->get();


        return view('admin.dashboard', compact(
                        'totalMembers',
            'activeMembers',
            'totalNews',
            'activeNews',
            'publishedNews',
            'totalQuotations',
            'unprocessedQuotations',
            'totalContactForms',
            'unprocessedContactForms',
            'totalHeroSliders',
            'activeHeroSliders',
            'totalGalleries',
            'activeGalleries',
            'recentMembers',
            'recentNews',
            'recentQuotations',
            'recentContactForms',
                        'recentContactForms',
            'membersByMonth',
            'newsByMonth',
            'quotationsByMonth'

        ));
    }

    /**
     * Handle admin logout.
     */
    public function logout(Request $request)
    {
        Auth::logout();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect()->route('login')
                       ->with('success', 'You have been logged out successfully.');
    }
}
