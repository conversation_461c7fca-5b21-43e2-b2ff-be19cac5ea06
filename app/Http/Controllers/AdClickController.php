<?php

namespace App\Http\Controllers;

use App\Models\Ad;
use App\Services\AdsService;
use Illuminate\Http\Request;

class AdClickController extends Controller
{
    protected $adsService;

    public function __construct(AdsService $adsService)
    {
        $this->adsService = $adsService;
    }

    /**
     * Handle ad click tracking and redirect
     */
    public function click(Request $request, $adId)
    {
        $ad = Ad::find($adId);
        
        if (!$ad || !$ad->isActive()) {
            abort(404);
        }

        // Record the click
        $this->adsService->recordClick($adId);

        // Redirect to the ad's link URL
        if ($ad->link_url) {
            return redirect()->away($ad->link_url);
        }

        // If no link URL, return to previous page
        return redirect()->back();
    }
}
