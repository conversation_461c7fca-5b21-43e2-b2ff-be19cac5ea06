<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class ExecutiveCommitteeController extends Controller
{
    /**
     * Display a listing of the executive committees.
     */
    public function index()
    {
        $executiveCommittees = \App\Models\ExecutiveCommittee::orderBy('created_at', 'desc')->paginate(10);

        return view('admin.executive-committees.index', compact('executiveCommittees'));
    }
    
    /**
     * Show the form for creating a new executive committee.
     */
    public function create()
    {
        return view('admin.executive-committees.create');
    }
    
    /**
     * Store a newly created executive committee in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'year' => 'required|string|max:255',
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'pdf' => 'nullable|file|mimes:pdf|max:2048', // 2MB max
            'is_active' => 'nullable',
        ]);
        
        $data = $request->only(['year', 'title', 'description', 'is_active']);
        $data['is_active'] = $request->has('is_active');
        
        // Handle PDF upload
        if ($request->hasFile('pdf')) {
            $pdfPath = $request->file('pdf')->store('executive-committees', 'public');
            $data['pdf_path'] = $pdfPath;
        }
        
        \App\Models\ExecutiveCommittee::create($data);
        
        return redirect()->route('admin.executive-committees.index')->with('success', 'Executive committee created successfully.');
    }
    
    /**
     * Display the specified executive committee.
     */
    public function show(\App\Models\ExecutiveCommittee $executiveCommittee)
    {
        return view('admin.executive-committees.show', compact('executiveCommittee'));
    }
    
    /**
     * Show the form for editing the specified executive committee.
     */
    public function edit(\App\Models\ExecutiveCommittee $executiveCommittee)
    {
        return view('admin.executive-committees.edit', compact('executiveCommittee'));
    }
    
    /**
     * Update the specified executive committee in storage.
     */
    public function update(Request $request, \App\Models\ExecutiveCommittee $executiveCommittee)
    {
        $request->validate([
            'year' => 'required|string|max:255',
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'pdf' => 'nullable|file|mimes:pdf|max:2048', // 2MB max
            'is_active' => 'nullable',
        ]);
        
        $data = $request->only(['year', 'title', 'description', 'is_active']);
        $data['is_active'] = $request->has('is_active');
        
        // Handle PDF upload
        if ($request->hasFile('pdf')) {
            // Delete old PDF if exists
            if ($executiveCommittee->pdf_path) {
                \Storage::disk('public')->delete($executiveCommittee->pdf_path);
            }
            
            $pdfPath = $request->file('pdf')->store('executive-committees', 'public');
            $data['pdf_path'] = $pdfPath;
        }
        
        $executiveCommittee->update($data);
        
        return redirect()->back()->with('success', 'Executive committee updated successfully.');
    }
    
    /**
     * Remove the specified executive committee from storage.
     */
    public function destroy(\App\Models\ExecutiveCommittee $executiveCommittee)
    {
        // Delete PDF if exists
        if ($executiveCommittee->pdf_path) {
            \Storage::disk('public')->delete($executiveCommittee->pdf_path);
        }
        
        $executiveCommittee->delete();
        
        return redirect()->route('admin.executive-committees.index')->with('success', 'Executive committee deleted successfully.');
    }
    
    /**
     * Toggle the active status of the specified executive committee.
     */
    public function toggleActive(\App\Models\ExecutiveCommittee $executiveCommittee)
    {
        $executiveCommittee->update(['is_active' => !$executiveCommittee->is_active]);
        
        return redirect()->back()->with('success', 'Executive committee status updated successfully.');
    }
}
