<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class ContactFormController extends Controller
{
    /**
     * Display a listing of the contact forms.
     */
    public function index()
    {
        $contactForms = \App\Models\ContactForm::orderBy('created_at', 'desc')->paginate(10);

        return view('admin.contact-forms.index', compact('contactForms'));
    }
    
    /**
     * Display the specified contact form.
     */
    public function show(\App\Models\ContactForm $contactForm)
    {
        return view('admin.contact-forms.show', compact('contactForm'));
    }
    
    /**
     * Update the specified contact form in storage.
     */
    public function update(Request $request, \App\Models\ContactForm $contactForm)
    {
        $contactForm->update([
            'processed' => $request->has('processed')
        ]);
        
        return redirect()->back()->with('success', 'Contact form updated successfully.');
    }
    
    /**
     * Remove the specified contact form from storage.
     */
    public function destroy(\App\Models\ContactForm $contactForm)
    {
        $contactForm->delete();
        
        return redirect()->route('admin.contact-forms.index')->with('success', 'Contact form deleted successfully.');
    }
}
