<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Ad;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class AdsController extends Controller
{
    /**
     * Display a listing of the ads.
     */
    public function index(Request $request)
    {
        $query = Ad::query();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by position
        if ($request->filled('position')) {
            $query->where('position', $request->position);
        }

        // Filter by type
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        $ads = $query->orderBy('sort_order', 'asc')
                    ->orderBy('created_at', 'desc')
                    ->paginate(15);

        // Get statistics
        $stats = [
            'total' => Ad::count(),
            'active' => Ad::where('status', 'active')->count(),
            'inactive' => Ad::where('status', 'inactive')->count(),
            'scheduled' => Ad::where('status', 'scheduled')->count(),
            'image_ads' => Ad::where('type', 'image')->count(),
            'video_ads' => Ad::where('type', 'video')->count(),
        ];

        return view('admin.ads.index', compact('ads', 'stats'));
    }

    /**
     * Show the form for creating a new ad.
     */
    public function create()
    {
        return view('admin.ads.create');
    }

    /**
     * Store a newly created ad in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'type' => 'required|in:image,video',
            'content_file' => 'required|file|mimes:jpg,jpeg,png,gif,mp4,avi,mov,wmv|max:20480', // 20MB max
            'thumbnail_file' => 'nullable|file|mimes:jpg,jpeg,png,gif|max:2048', // 2MB max for thumbnail
            'description' => 'nullable|string',
            'link_url' => 'nullable|url',
            'position' => 'required|in:header,sidebar,footer,content,popup,banner',
            'status' => 'required|in:active,inactive,scheduled',
            'sort_order' => 'nullable|integer|min:0',
            'width' => 'nullable|integer|min:1',
            'height' => 'nullable|integer|min:1',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'max_impressions' => 'nullable|integer|min:1',
            'open_in_new_tab' => 'boolean',
            'target_pages' => 'nullable|array',
            'target_pages.*' => 'string',
            'alt_text' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                           ->withErrors($validator)
                           ->withInput();
        }

        try {
            // Handle file upload
            $contentPath = null;
            $thumbnailPath = null;

            if ($request->hasFile('content_file')) {
                $file = $request->file('content_file');
                $filename = time() . '_' . Str::random(10) . '.' . $file->getClientOriginalExtension();
                $contentPath = $file->storeAs('ads/content', $filename, 'public');
            }

            // Handle thumbnail upload for videos or custom thumbnails
            if ($request->hasFile('thumbnail_file')) {
                $thumbnailFile = $request->file('thumbnail_file');
                $thumbnailFilename = time() . '_thumb_' . Str::random(10) . '.' . $thumbnailFile->getClientOriginalExtension();
                $thumbnailPath = $thumbnailFile->storeAs('ads/thumbnails', $thumbnailFilename, 'public');
            }

            // Process target pages
            $targetPages = null;
            if ($request->filled('target_pages') && is_array($request->target_pages)) {
                $pages = array_filter($request->target_pages);
                $targetPages = !empty($pages) ? $pages : null;
            }

            // Create the ad
            $ad = Ad::create([
                'title' => $request->title,
                'type' => $request->type,
                'content_path' => $contentPath,
                'thumbnail_path' => $thumbnailPath,
                'description' => $request->description,
                'link_url' => $request->link_url,
                'position' => $request->position,
                'status' => $request->status,
                'sort_order' => $request->sort_order ?? 0,
                'width' => $request->width,
                'height' => $request->height,
                'start_date' => $request->start_date,
                'end_date' => $request->end_date,
                'max_impressions' => $request->max_impressions,
                'open_in_new_tab' => $request->boolean('open_in_new_tab', true),
                'target_pages' => $targetPages,
                'alt_text' => $request->alt_text,
            ]);

            return redirect()->route('admin.ads.index')
                           ->with('success', 'Ad created successfully!');

        } catch (\Exception $e) {
            // Clean up uploaded files if ad creation fails
            if ($contentPath && Storage::disk('public')->exists($contentPath)) {
                Storage::disk('public')->delete($contentPath);
            }
            if ($thumbnailPath && Storage::disk('public')->exists($thumbnailPath)) {
                Storage::disk('public')->delete($thumbnailPath);
            }

            return redirect()->back()
                           ->with('error', 'Failed to create ad: ' . $e->getMessage())
                           ->withInput();
        }
    }

    /**
     * Display the specified ad.
     */
    public function show(Ad $ad)
    {
        return view('admin.ads.show', compact('ad'));
    }

    /**
     * Show the form for editing the specified ad.
     */
    public function edit(Ad $ad)
    {
        return view('admin.ads.edit', compact('ad'));
    }

    /**
     * Update the specified ad in storage.
     */
    public function update(Request $request, Ad $ad)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'type' => 'required|in:image,video',
            'content_file' => 'nullable|file|mimes:jpg,jpeg,png,gif,mp4,avi,mov,wmv|max:20480',
            'thumbnail_file' => 'nullable|file|mimes:jpg,jpeg,png,gif|max:2048',
            'description' => 'nullable|string',
            'link_url' => 'nullable|url',
            'position' => 'required|in:header,sidebar,footer,content,popup,banner',
            'status' => 'required|in:active,inactive,scheduled',
            'sort_order' => 'nullable|integer|min:0',
            'width' => 'nullable|integer|min:1',
            'height' => 'nullable|integer|min:1',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'max_impressions' => 'nullable|integer|min:1',
            'open_in_new_tab' => 'boolean',
            'target_pages' => 'nullable|array',
            'target_pages.*' => 'string',
            'alt_text' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                           ->withErrors($validator)
                           ->withInput();
        }

        try {
            $updateData = [
                'title' => $request->title,
                'type' => $request->type,
                'description' => $request->description,
                'link_url' => $request->link_url,
                'position' => $request->position,
                'status' => $request->status,
                'sort_order' => $request->sort_order ?? 0,
                'width' => $request->width,
                'height' => $request->height,
                'start_date' => $request->start_date,
                'end_date' => $request->end_date,
                'max_impressions' => $request->max_impressions,
                'open_in_new_tab' => $request->boolean('open_in_new_tab', true),
                'alt_text' => $request->alt_text,
            ];

            // Handle content file update
            if ($request->hasFile('content_file')) {
                // Delete old content file
                if ($ad->content_path && Storage::disk('public')->exists($ad->content_path)) {
                    Storage::disk('public')->delete($ad->content_path);
                }

                $file = $request->file('content_file');
                $filename = time() . '_' . Str::random(10) . '.' . $file->getClientOriginalExtension();
                $updateData['content_path'] = $file->storeAs('ads/content', $filename, 'public');
            }

            // Handle thumbnail file update
            if ($request->hasFile('thumbnail_file')) {
                // Delete old thumbnail file
                if ($ad->thumbnail_path && Storage::disk('public')->exists($ad->thumbnail_path)) {
                    Storage::disk('public')->delete($ad->thumbnail_path);
                }

                $thumbnailFile = $request->file('thumbnail_file');
                $thumbnailFilename = time() . '_thumb_' . Str::random(10) . '.' . $thumbnailFile->getClientOriginalExtension();
                $updateData['thumbnail_path'] = $thumbnailFile->storeAs('ads/thumbnails', $thumbnailFilename, 'public');
            }

            // Process target pages
            $targetPages = null;
            if ($request->filled('target_pages') && is_array($request->target_pages)) {
                $pages = array_filter($request->target_pages);
                $targetPages = !empty($pages) ? $pages : null;
            }
            $updateData['target_pages'] = $targetPages;

            $ad->update($updateData);

            return redirect()->route('admin.ads.index')
                           ->with('success', 'Ad updated successfully!');

        } catch (\Exception $e) {
            return redirect()->back()
                           ->with('error', 'Failed to update ad: ' . $e->getMessage())
                           ->withInput();
        }
    }

    /**
     * Remove the specified ad from storage.
     */
    public function destroy(Ad $ad)
    {
        try {
            // Delete associated files
            if ($ad->content_path && Storage::disk('public')->exists($ad->content_path)) {
                Storage::disk('public')->delete($ad->content_path);
            }

            if ($ad->thumbnail_path && Storage::disk('public')->exists($ad->thumbnail_path)) {
                Storage::disk('public')->delete($ad->thumbnail_path);
            }

            $ad->delete();

            return redirect()->route('admin.ads.index')
                           ->with('success', 'Ad deleted successfully!');

        } catch (\Exception $e) {
            return redirect()->back()
                           ->with('error', 'Failed to delete ad: ' . $e->getMessage());
        }
    }

    /**
     * Toggle ad status (active/inactive).
     */
    public function toggleStatus(Ad $ad)
    {
        try {
            $newStatus = $ad->status === 'active' ? 'inactive' : 'active';
            $ad->update(['status' => $newStatus]);

            return response()->json([
                'success' => true,
                'message' => "Ad status changed to {$newStatus}",
                'status' => $newStatus
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to toggle status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Bulk actions for ads.
     */
    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:activate,deactivate,delete',
            'ads' => 'required|array',
            'ads.*' => 'exists:ads,id'
        ]);

        try {
            $ads = Ad::whereIn('id', $request->ads);

            switch ($request->action) {
                case 'activate':
                    $ads->update(['status' => 'active']);
                    $message = 'Selected ads have been activated.';
                    break;

                case 'deactivate':
                    $ads->update(['status' => 'inactive']);
                    $message = 'Selected ads have been deactivated.';
                    break;

                case 'delete':
                    // Delete files for each ad
                    foreach ($ads->get() as $ad) {
                        if ($ad->content_path && Storage::disk('public')->exists($ad->content_path)) {
                            Storage::disk('public')->delete($ad->content_path);
                        }
                        if ($ad->thumbnail_path && Storage::disk('public')->exists($ad->thumbnail_path)) {
                            Storage::disk('public')->delete($ad->thumbnail_path);
                        }
                    }
                    $ads->delete();
                    $message = 'Selected ads have been deleted.';
                    break;
            }

            return redirect()->route('admin.ads.index')
                           ->with('success', $message);

        } catch (\Exception $e) {
            return redirect()->back()
                           ->with('error', 'Bulk action failed: ' . $e->getMessage());
        }
    }

    /**
     * Track ad click.
     */
    public function trackClick(Ad $ad)
    {
        try {
            $ad->incrementClicks();

            if ($ad->link_url) {
                return redirect($ad->link_url);
            }

            return response()->json(['success' => true]);

        } catch (\Exception $e) {
            return response()->json(['success' => false], 500);
        }
    }

    /**
     * Get ads analytics data.
     */
    public function analytics()
    {
        $ads = Ad::select('id', 'title', 'impressions_count', 'clicks_count', 'position', 'status')
                 ->orderBy('impressions_count', 'desc')
                 ->get();

        $analytics = [
            'total_impressions' => Ad::sum('impressions_count'),
            'total_clicks' => Ad::sum('clicks_count'),
            'average_ctr' => $ads->avg('click_through_rate'),
            'top_performing' => $ads->take(5),
            'position_stats' => Ad::selectRaw('position, COUNT(*) as count, SUM(impressions_count) as impressions, SUM(clicks_count) as clicks')
                                  ->groupBy('position')
                                  ->get(),
        ];

        return view('admin.ads.analytics', compact('analytics'));
    }

    /**
     * Duplicate an ad.
     */
    public function duplicate(Ad $ad)
    {
        try {
            $newAd = $ad->replicate();
            $newAd->title = $ad->title . ' (Copy)';
            $newAd->status = 'inactive';
            $newAd->impressions_count = 0;
            $newAd->clicks_count = 0;
            $newAd->save();

            return redirect()->route('admin.ads.edit', $newAd)
                           ->with('success', 'Ad duplicated successfully! Please review and update as needed.');

        } catch (\Exception $e) {
            return redirect()->back()
                           ->with('error', 'Failed to duplicate ad: ' . $e->getMessage());
        }
    }
}
