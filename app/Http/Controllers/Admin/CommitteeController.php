<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Committee;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class CommitteeController extends Controller
{
    /**
     * Display a listing of the committees.
     */
    public function index()
    {
        $committees = Committee::orderBy('sort_order')->orderBy('name')->paginate(10);

        return view('admin.committees.index', compact('committees'));
    }

    /**
     * Show the form for creating a new committee.
     */
    public function create()
    {
        return view('admin.committees.create');
    }

    /**
     * Store a newly created committee in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'position' => 'required|string|max:255',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048', // 2MB max
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'nullable',
        ]);

        $data = $request->only(['name', 'position', 'sort_order', 'is_active']);
        $data['is_active'] = $request->has('is_active');
        $data['sort_order'] = $request->input('sort_order', 0);

        // Handle image upload
        if ($request->hasFile('image')) {
            $imagePath = $request->file('image')->store('committees', 'public');
            $data['image_path'] = $imagePath;
        }

        Committee::create($data);

        return redirect()->route('admin.committees.index')->with('success', 'Committee member created successfully.');
    }

    /**
     * Display the specified committee.
     */
    public function show(Committee $committee)
    {
        return view('admin.committees.show', compact('committee'));
    }

    /**
     * Show the form for editing the specified committee.
     */
    public function edit(Committee $committee)
    {
        return view('admin.committees.edit', compact('committee'));
    }

    /**
     * Update the specified committee in storage.
     */
    public function update(Request $request, Committee $committee)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'position' => 'required|string|max:255',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048', // 2MB max
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'nullable',
        ]);

        $data = $request->only(['name', 'position', 'sort_order', 'is_active']);
        $data['is_active'] = $request->has('is_active');
        $data['sort_order'] = $request->input('sort_order', 0);

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($committee->image_path) {
                Storage::disk('public')->delete($committee->image_path);
            }

            $imagePath = $request->file('image')->store('committees', 'public');
            $data['image_path'] = $imagePath;
        }

        $committee->update($data);

        return redirect()->back()->with('success', 'Committee member updated successfully.');
    }

    /**
     * Remove the specified committee from storage.
     */
    public function destroy(Committee $committee)
    {
        // Delete image if exists
        if ($committee->image_path) {
            Storage::disk('public')->delete($committee->image_path);
        }

        $committee->delete();

        return redirect()->route('admin.committees.index')->with('success', 'Committee member deleted successfully.');
    }

    /**
     * Toggle the active status of the specified committee.
     */
    public function toggleActive(Committee $committee)
    {
        $committee->update(['is_active' => !$committee->is_active]);

        return redirect()->back()->with('success', 'Committee member status updated successfully.');
    }
}
