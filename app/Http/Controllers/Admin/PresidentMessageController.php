<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\PresidentMessage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class PresidentMessageController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $messages = PresidentMessage::ordered()->get();
        return view('admin.president-messages.index', compact('messages'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.president-messages.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'position' => 'required|string|max:255',
            'message' => 'required|string',
            'profile_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'objectives' => 'nullable|array',
            'objectives.*' => 'string|max:1000',
            'sort_order' => 'required|integer|min:0',
            'is_active' => 'boolean'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $data = $request->all();

        // Handle profile image upload
        if ($request->hasFile('profile_image')) {
            $imagePath = $request->file('profile_image')->store('president-messages', 'public');
            $data['profile_image_path'] = $imagePath;
        }

        // Remove the profile_image field from data as we store it as profile_image_path
        unset($data['profile_image']);

        // Filter out empty objectives
        if (isset($data['objectives'])) {
            $data['objectives'] = array_filter($data['objectives'], function($objective) {
                return !empty(trim($objective));
            });
        }

        PresidentMessage::create($data);

        return redirect()->route('admin.president-messages.index')
            ->with('success', 'President message created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(PresidentMessage $presidentMessage)
    {
        return view('admin.president-messages.show', compact('presidentMessage'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(PresidentMessage $presidentMessage)
    {
        return view('admin.president-messages.edit', compact('presidentMessage'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, PresidentMessage $presidentMessage)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'position' => 'required|string|max:255',
            'message' => 'required|string',
            'profile_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'objectives' => 'nullable|array',
            'objectives.*' => 'string|max:1000',
            'sort_order' => 'required|integer|min:0',
            'is_active' => 'boolean'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $data = $request->all();

        // Handle profile image upload
        if ($request->hasFile('profile_image')) {
            // Delete old image if exists
            if ($presidentMessage->profile_image_path) {
                Storage::disk('public')->delete($presidentMessage->profile_image_path);
            }

            $imagePath = $request->file('profile_image')->store('president-messages', 'public');
            $data['profile_image_path'] = $imagePath;
        }

        // Remove the profile_image field from data
        unset($data['profile_image']);

        // Filter out empty objectives
        if (isset($data['objectives'])) {
            $data['objectives'] = array_filter($data['objectives'], function($objective) {
                return !empty(trim($objective));
            });
        }

        $presidentMessage->update($data);

        return redirect()->route('admin.president-messages.index')
            ->with('success', 'President message updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(PresidentMessage $presidentMessage)
    {
        // Delete associated profile image
        if ($presidentMessage->profile_image_path) {
            Storage::disk('public')->delete($presidentMessage->profile_image_path);
        }

        $presidentMessage->delete();

        return redirect()->route('admin.president-messages.index')
            ->with('success', 'President message deleted successfully.');
    }
}
