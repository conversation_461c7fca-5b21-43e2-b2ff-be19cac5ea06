<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Member;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use App\Imports\MembersImport;
use Maatwebsite\Excel\Facades\Excel;

class MemberController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $members = Member::ordered()->get();
        return view('admin.members.index', compact('members'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.members.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'address' => 'required|string|max:255',
            'full_address' => 'nullable|string',
            'description' => 'nullable|string',
            'emails' => 'nullable|array',
            'emails.*' => 'email|max:255',
            'phones' => 'nullable|array',
            'phones.*' => 'string|max:255',
            'website' => 'nullable|url|max:255',
            'contact_persons' => 'nullable|array',
            'contact_persons.*.name' => 'required_with:contact_persons|string|max:255',
            'contact_persons.*.phone' => 'nullable|string|max:255',
            'is_active' => 'boolean',
            'sort_order' => 'required|integer|min:0',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $data = $request->all();



        // Process emails
        if (isset($data['emails'])) {
            $data['emails'] = array_values(array_filter($data['emails'], function($email) {
                return !empty(trim($email));
            }));
        }

        // Process phones
        if (isset($data['phones'])) {
            $data['phones'] = array_values(array_filter($data['phones'], function($phone) {
                return !empty(trim($phone));
            }));
        }

        // Process contact persons
        if (isset($data['contact_persons'])) {
            $contactPersons = [];
            foreach ($data['contact_persons'] as $contact) {
                if (!empty($contact['name'])) {
                    $contactPersons[] = [
                        'name' => $contact['name'],
                        'phone' => $contact['phone'] ?? null,
                    ];
                }
            }
            $data['contact_persons'] = $contactPersons;
        }

        Member::create($data);

        return redirect()->route('admin.members.index')
            ->with('success', 'Member created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Member $member)
    {
        return view('admin.members.show', compact('member'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Member $member)
    {
        return view('admin.members.edit', compact('member'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Member $member)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'address' => 'required|string|max:255',
            'full_address' => 'nullable|string',
            'description' => 'nullable|string',
            'emails' => 'nullable|array',
            'emails.*' => 'email|max:255',
            'phones' => 'nullable|array',
            'phones.*' => 'string|max:255',
            'website' => 'nullable|url|max:255',
            'contact_persons' => 'nullable|array',
            'contact_persons.*.name' => 'required_with:contact_persons|string|max:255',
            'contact_persons.*.phone' => 'nullable|string|max:255',
            'is_active' => 'boolean',
            'sort_order' => 'required|integer|min:0',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $data = $request->all();



        // Process emails
        if (isset($data['emails'])) {
            $data['emails'] = array_values(array_filter($data['emails'], function($email) {
                return !empty(trim($email));
            }));
        }

        // Process phones
        if (isset($data['phones'])) {
            $data['phones'] = array_values(array_filter($data['phones'], function($phone) {
                return !empty(trim($phone));
            }));
        }

        // Process contact persons
        if (isset($data['contact_persons'])) {
            $contactPersons = [];
            foreach ($data['contact_persons'] as $contact) {
                if (!empty($contact['name'])) {
                    $contactPersons[] = [
                        'name' => $contact['name'],
                        'phone' => $contact['phone'] ?? null,
                    ];
                }
            }
            $data['contact_persons'] = $contactPersons;
        }

        $member->update($data);

        return redirect()->route('admin.members.index')
            ->with('success', 'Member updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Member $member)
    {
        $member->delete();

        return redirect()->route('admin.members.index')
            ->with('success', 'Member deleted successfully.');
    }

    /**
     * Toggle active status
     */
    public function toggleActive(Member $member)
    {
        $member->update(['is_active' => !$member->is_active]);
        
        $status = $member->is_active ? 'activated' : 'deactivated';
        return redirect()->back()
            ->with('success', "Member {$status} successfully.");
    }

    /**
     * Import members from Excel file.
     */
    public function import(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'file' => 'required|mimes:xlsx,xls,csv|max:2048',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            $beforeCount = Member::count();
            Excel::import(new MembersImport, $request->file('file'));
            $afterCount = Member::count();
            $importedCount = $afterCount - $beforeCount;

            return redirect()->back()
                ->with('success', "Successfully imported {$importedCount} members.");
        } catch (\Maatwebsite\Excel\Validators\ValidationException $e) {
            $failures = $e->failures();
            $errorMessages = [];
            foreach ($failures as $failure) {
                $errorMessages[] = "Row {$failure->row()}: " . implode(', ', $failure->errors());
            }
            return redirect()->back()
                ->with('error', 'Import validation failed: ' . implode(' | ', $errorMessages));
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Error importing members: ' . $e->getMessage());
        }
    }

    /**
     * Export members to Excel file.
     */
    public function export()
    {
        try {
            return Excel::download(new \App\Exports\MembersExport, 'members.xlsx');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Error exporting members: ' . $e->getMessage());
        }
    }

    /**
     * Handle bulk actions on members.
     */
    public function bulkAction(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'action' => 'required|in:activate,deactivate,delete',
            'ids' => 'required|json',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid request data.'
            ], 400);
        }

        try {
            $ids = json_decode($request->ids, true);
            $action = $request->action;
            $count = 0;

            switch ($action) {
                case 'activate':
                    $count = Member::whereIn('id', $ids)->update(['is_active' => true]);
                    break;
                case 'deactivate':
                    $count = Member::whereIn('id', $ids)->update(['is_active' => false]);
                    break;
                case 'delete':
                    $count = Member::whereIn('id', $ids)->delete();
                    break;
            }

            return response()->json([
                'success' => true,
                'message' => "Successfully {$action}d {$count} members."
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error processing bulk action: ' . $e->getMessage()
            ], 500);
        }
    }
}
