<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class SettingsController extends Controller
{
    /**
     * Display the settings page.
     */
    public function index()
    {
        $settings = Setting::ordered()->get()->groupBy('group');
        
        return view('admin.settings.index', compact('settings'));
    }

    /**
     * Update the settings.
     */
    public function update(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'settings' => 'required|array',
            'settings.*' => 'nullable|string|max:1000'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            foreach ($request->input('settings', []) as $key => $value) {
                $setting = Setting::where('key', $key)->first();
                
                if ($setting) {
                    // Handle different input types
                    if ($setting->type === 'boolean') {
                        $value = $request->has("settings.{$key}") ? '1' : '0';
                    }
                    
                    $setting->update(['value' => $value]);
                }
            }

            // Clear settings cache
            Setting::clearCache();

            return redirect()->route('admin.settings.index')
                ->with('success', 'Settings updated successfully.');
                
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'An error occurred while updating settings: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Reset settings to default values.
     */
    public function reset(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'group' => 'required|string|in:general,contact,social,appearance,seo'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator);
        }

        try {
            $group = $request->input('group');
            
            // Get default values for the group
            $defaultSettings = $this->getDefaultSettings($group);
            
            foreach ($defaultSettings as $key => $data) {
                Setting::updateOrCreate(
                    ['key' => $key],
                    [
                        'value' => $data['value'],
                        'type' => $data['type'],
                        'group' => $group,
                        'label' => $data['label'],
                        'description' => $data['description'] ?? null,
                        'is_public' => $data['is_public'] ?? false,
                        'sort_order' => $data['sort_order'] ?? 0
                    ]
                );
            }

            // Clear settings cache
            Setting::clearCache();

            return redirect()->route('admin.settings.index')
                ->with('success', ucfirst($group) . ' settings have been reset to default values.');
                
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'An error occurred while resetting settings: ' . $e->getMessage());
        }
    }

    /**
     * Get default settings for a group.
     */
    private function getDefaultSettings($group)
    {
        $defaults = [
            'general' => [
                'site_name' => [
                    'value' => 'Urla Industries Association',
                    'type' => 'string',
                    'label' => 'Site Name',
                    'description' => 'The name of your website',
                    'is_public' => true,
                    'sort_order' => 1
                ],
                'site_tagline' => [
                    'value' => 'Building Industrial Excellence',
                    'type' => 'string',
                    'label' => 'Site Tagline',
                    'description' => 'A short description of your site',
                    'is_public' => true,
                    'sort_order' => 2
                ],
                'site_description' => [
                    'value' => 'Urla Industries Association is dedicated to promoting industrial growth and excellence in the region.',
                    'type' => 'text',
                    'label' => 'Site Description',
                    'description' => 'A longer description of your site for SEO',
                    'is_public' => true,
                    'sort_order' => 3
                ],
                'admin_email' => [
                    'value' => '<EMAIL>',
                    'type' => 'string',
                    'label' => 'Admin Email',
                    'description' => 'Primary email for administrative purposes',
                    'is_public' => false,
                    'sort_order' => 4
                ],
                'maintenance_mode' => [
                    'value' => '0',
                    'type' => 'boolean',
                    'label' => 'Maintenance Mode',
                    'description' => 'Enable to put the site in maintenance mode',
                    'is_public' => false,
                    'sort_order' => 5
                ]
            ],
            'contact' => [
                'contact_phone' => [
                    'value' => '+90 232 XXX XX XX',
                    'type' => 'string',
                    'label' => 'Phone Number',
                    'description' => 'Primary contact phone number',
                    'is_public' => true,
                    'sort_order' => 1
                ],
                'contact_email' => [
                    'value' => '<EMAIL>',
                    'type' => 'string',
                    'label' => 'Contact Email',
                    'description' => 'Public contact email address',
                    'is_public' => true,
                    'sort_order' => 2
                ],
                'contact_address' => [
                    'value' => 'Urla, İzmir, Turkey',
                    'type' => 'text',
                    'label' => 'Address',
                    'description' => 'Physical address of the organization',
                    'is_public' => true,
                    'sort_order' => 3
                ],
                'contact_hours' => [
                    'value' => 'Monday - Friday: 9:00 AM - 6:00 PM',
                    'type' => 'string',
                    'label' => 'Business Hours',
                    'description' => 'Operating hours',
                    'is_public' => true,
                    'sort_order' => 4
                ]
            ],
            'social' => [
                'facebook_url' => [
                    'value' => '',
                    'type' => 'string',
                    'label' => 'Facebook URL',
                    'description' => 'Facebook page URL',
                    'is_public' => true,
                    'sort_order' => 1
                ],
                'twitter_url' => [
                    'value' => '',
                    'type' => 'string',
                    'label' => 'Twitter URL',
                    'description' => 'Twitter profile URL',
                    'is_public' => true,
                    'sort_order' => 2
                ],
                'linkedin_url' => [
                    'value' => '',
                    'type' => 'string',
                    'label' => 'LinkedIn URL',
                    'description' => 'LinkedIn company page URL',
                    'is_public' => true,
                    'sort_order' => 3
                ],
                'instagram_url' => [
                    'value' => '',
                    'type' => 'string',
                    'label' => 'Instagram URL',
                    'description' => 'Instagram profile URL',
                    'is_public' => true,
                    'sort_order' => 4
                ]
            ]
        ];

        return $defaults[$group] ?? [];
    }
}
