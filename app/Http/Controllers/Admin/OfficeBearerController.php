<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class OfficeBearerController extends Controller
{
    /**
     * Display a listing of the office bearers.
     */
    public function index()
    {
        $officeBearers = \App\Models\OfficeBearer::orderBy('created_at', 'desc')->paginate(10);
        return view('admin.office-bearers.index', compact('officeBearers'));
    }
    
    /**
     * Show the form for creating a new office bearer.
     */
    public function create()
    {
        return view('admin.office-bearers.create');
    }
    
    /**
     * Store a newly created office bearer in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'year' => 'required|string|max:255',
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'pdf' => 'nullable|file|mimes:pdf|max:2048',
        ]);
        
        $data = $request->only(['year', 'title', 'description', 'is_active']);
        $data['is_active'] = $request->has('is_active');
        
        if ($request->hasFile('pdf')) {
            $data['pdf_path'] = $request->file('pdf')->store('office-bearers', 'public');
        }
        
        \App\Models\OfficeBearer::create($data);
        
        return redirect()->route('admin.office-bearers.index')->with('success', 'Office bearer created successfully.');
    }
    
    /**
     * Display the specified office bearer.
     */
    public function show(\App\Models\OfficeBearer $officeBearer)
    {
        return view('admin.office-bearers.show', compact('officeBearer'));
    }
    
    /**
     * Show the form for editing the specified office bearer.
     */
    public function edit(\App\Models\OfficeBearer $officeBearer)
    {
        return view('admin.office-bearers.edit', compact('officeBearer'));
    }
    
    /**
     * Update the specified office bearer in storage.
     */
    public function update(Request $request, \App\Models\OfficeBearer $officeBearer)
    {
        $request->validate([
            'year' => 'required|string|max:255',
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'pdf' => 'nullable|file|mimes:pdf|max:2048',
            'is_active' => 'nullable|boolean',
        ]);
        
        $data = $request->only(['year', 'title', 'description', 'is_active']);
        $data['is_active'] = $request->has('is_active');
        
        if ($request->hasFile('pdf')) {
            // Delete old PDF if exists
            if ($officeBearer->pdf_path && \Storage::disk('public')->exists($officeBearer->pdf_path)) {
                \Storage::disk('public')->delete($officeBearer->pdf_path);
            }
            $data['pdf_path'] = $request->file('pdf')->store('office-bearers', 'public');
        }
        
        $officeBearer->update($data);
        
        return redirect()->route('admin.office-bearers.index')->with('success', 'Office bearer updated successfully.');
    }
    
    /**
     * Remove the specified office bearer from storage.
     */
    public function destroy(\App\Models\OfficeBearer $officeBearer)
    {
        // Delete PDF if exists
        if ($officeBearer->pdf_path && \Storage::disk('public')->exists($officeBearer->pdf_path)) {
            \Storage::disk('public')->delete($officeBearer->pdf_path);
        }
        
        $officeBearer->delete();
        
        return redirect()->route('admin.office-bearers.index')->with('success', 'Office bearer deleted successfully.');
    }
    
    /**
     * Toggle the active status of the specified office bearer.
     */
    public function toggleActive(\App\Models\OfficeBearer $officeBearer)
    {
        $officeBearer->update(['is_active' => !$officeBearer->is_active]);
        
        return redirect()->back()->with('success', 'Office bearer status updated successfully.');
    }
}
