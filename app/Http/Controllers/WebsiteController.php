<?php

namespace App\Http\Controllers;

use App\Models\HeroSlider;
use App\Models\PresidentMessage;
use App\Models\Gallery;
use App\Models\Member;
use Illuminate\Http\Request;

class WebsiteController extends Controller
{
    /**
     * Display the home page.
     */
    public function index()
    {
        $heroSliders = HeroSlider::active()->ordered()->get();
        $presidentMessage = PresidentMessage::active()->ordered()->first();
        $featuredMembers = Member::active()->ordered()->limit(12)->get();
        $latestNews = \App\Models\News::active()->published()->ordered()->limit(3)->get();
        return view('index', compact('heroSliders', 'presidentMessage', 'featuredMembers', 'latestNews'));
    }

    /**
     * Display the about us page.
     */
    public function aboutUs()
    {
        return view('about-us');
    }

    /**
     * Display the president message page.
     */
    public function presidentMessage()
    {
        $presidentMessage = PresidentMessage::active()->ordered()->first();
        return view('president-message', compact('presidentMessage'));
    }

    /**
     * Display the office bearers page.
     */
    public function officeBearers()
    {
        $officeBearers = \App\Models\OfficeBearer::active()->orderBy('year', 'desc')->get();
        $committees = \App\Models\Committee::active()->orderBy('sort_order')->get();
        return view('office-bearers', compact('officeBearers', 'committees'));
    }

    /**
     * Display the executive committee page.
     */
    public function executiveCommittee()
    {
        $executiveCommittees = \App\Models\ExecutiveCommittee::active()->orderBy('year', 'desc')->get();
        return view('executive-committee', compact('executiveCommittees', 'committees'));
    }

    /**
     * Display the UIA members page.
     */
    public function uiaMembers()
    {
        $members = Member::active()->ordered()->get();
        return view('uia-members', compact('members'));
    }

    /**
     * Display the member profile page.
     */
    public function memberProfile()
    {
        return view('member-profile');
    }

    /**
     * Display the member details page.
     */
    public function memberDetails(Request $request)
    {
        $memberName = $request->query('name');
        $member = Member::where('name', $memberName)->first();
        
        if (!$member) {
            abort(404);
        }
        
        return view('member-details', compact('member'));
    }

    /**
     * Display the news page.
     */
    public function news()
    {
        $news = \App\Models\News::active()->published()->ordered()->paginate(12);
        return view('news', compact('news'));
    }

    /**
     * Display the news detail page.
     */
    public function newsDetail(\App\Models\News $news)
    {
        // Check if news is active and published
        if (!$news->is_active || $news->status !== 'published') {
            abort(404);
        }

        // Get related news (same category, excluding current)
        $relatedNews = \App\Models\News::active()
            ->published()
            ->where('category', $news->category)
            ->where('id', '!=', $news->id)
            ->ordered()
            ->limit(3)
            ->get();

        return view('news-detail', compact('news', 'relatedNews'));
    }

    /**
     * Display the get quotation page.
     */
    public function getQuotation()
    {
        return view('get-quotation');
    }

    /**
     * Store a new quotation request.
     */
    public function storeQuotation(Request $request)
    {
        $request->validate([
            'fullName' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'company' => 'nullable|string|max:255',
            'products' => 'required|array|min:1',
            'products.*' => 'string|max:255',
            'message' => 'required|string',
            'terms' => 'required|accepted',
        ]);

        \App\Models\Quotation::create([
            'full_name' => $request->fullName,
            'email' => $request->email,
            'phone' => $request->phone,
            'company_name' => $request->company,
            'products' => $request->products,
            'message' => $request->message,
            'terms_accepted' => true,
            'processed' => false,
        ]);

        return response()->json(['success' => true, 'message' => 'Quotation request submitted successfully.']);
    }

    /**
     * Display the contact page.
     */
    public function contact()
    {
        return view('contact');
    }

    /**
     * Display the gallery page.
     */
    public function gallery()
    {
        $galleries = Gallery::active()->ordered()->get();
        return view('gallery', compact('galleries'));
    }
    
    /**
     * Store a new contact form submission.
     */
    public function storeContactForm(Request $request)
    {
        try {
            $request->validate([
                'firstName' => 'required|string|max:255',
                'lastName' => 'required|string|max:255',
                'email' => 'required|email|max:255',
                'phone' => 'nullable|string|max:20',
                'company' => 'nullable|string|max:255',
                'subject' => 'required|string|max:255',
                'message' => 'required|string',
                'newsletter' => 'nullable',
            ]);

            \App\Models\ContactForm::create([
                'first_name' => $request->firstName,
                'last_name' => $request->lastName,
                'email' => $request->email,
                'phone' => $request->phone,
                'company' => $request->company,
                'subject' => $request->subject,
                'message' => $request->message,
                'newsletter' => $request->has('newsletter'),
                'processed' => false,
            ]);

            return response()->json(['success' => true, 'message' => 'Contact form submitted successfully. We will get back to you soon.']);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            \Log::error('Contact form submission error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while processing your request. Please try again.'
            ], 500);
        }
    }
}
