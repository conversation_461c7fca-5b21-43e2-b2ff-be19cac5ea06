<?php

namespace App\Http\View\Composers;

use App\Services\AdsService;
use Illuminate\View\View;

class AdsComposer
{
    protected $adsService;

    public function __construct(AdsService $adsService)
    {
        $this->adsService = $adsService;
    }

    /**
     * Bind data to the view.
     */
    public function compose(View $view)
    {
        // Get current route name to determine page context
        $currentPage = request()->route() ? request()->route()->getName() : 'home';
        
        // Get all ads for the current page
        $ads = $this->adsService->getAdsForPage($currentPage);
        
        // Share ads data with the view
        $view->with('ads', $ads);
    }
}
