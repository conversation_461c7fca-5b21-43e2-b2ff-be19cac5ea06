<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\View;
use App\Http\View\Composers\AdsComposer;

class ViewServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Register the AdsComposer for all website views
        View::composer([
            'layouts.app',
            'layouts.app-with-sidebar',
            'index',
            'about-us',
            'president-message',
            'office-bearers',
            'executive-committee',
            'uia-members',
            'member-profile',
            'member-details',
            'news',
            'news-detail',
            'get-quotation',
            'contact',
            'gallery'
        ], AdsComposer::class);
    }
}
