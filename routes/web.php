<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\WebsiteController;
use App\Http\Controllers\AdminController;
use App\Http\Controllers\Admin\HeroSliderController;
use App\Http\Controllers\Admin\PresidentMessageController;
use App\Http\Controllers\Admin\NewsController;
use App\Http\Controllers\Admin\GalleryController;
use App\Http\Controllers\Admin\MemberController;
use App\Http\Controllers\Admin\QuotationController;
use App\Http\Controllers\Admin\ContactFormController;
use App\Http\Controllers\Admin\OfficeBearerController;
use App\Http\Controllers\Admin\ExecutiveCommitteeController;
use App\Http\Controllers\Admin\CommitteeController;
use App\Http\Controllers\Admin\SettingsController;
use App\Http\Controllers\Admin\AdsController;
use App\Http\Controllers\AdClickController;







// Website Routes
Route::get('/', [WebsiteController::class, 'index'])->name('home');
Route::get('/about-us', [WebsiteController::class, 'aboutUs'])->name('about-us');
Route::get('/president-message', [WebsiteController::class, 'presidentMessage'])->name('president-message');
Route::get('/office-bearers', [WebsiteController::class, 'officeBearers'])->name('office-bearers');
Route::get('/executive-committee', [WebsiteController::class, 'executiveCommittee'])->name('executive-committee');
Route::get('/uia-members', [WebsiteController::class, 'uiaMembers'])->name('uia-members');
Route::get('/member-profile', [WebsiteController::class, 'memberProfile'])->name('member-profile');
Route::get('/member-details', [WebsiteController::class, 'memberDetails'])->name('member-details');
Route::get('/news', [WebsiteController::class, 'news'])->name('news');
Route::get('/news/{news}', [WebsiteController::class, 'newsDetail'])->name('news-detail');
Route::get('/get-quotation', [WebsiteController::class, 'getQuotation'])->name('get-quotation');
Route::post('/get-quotation', [WebsiteController::class, 'storeQuotation'])->name('store-quotation');
Route::get('/contact', [WebsiteController::class, 'contact'])->name('contact');
Route::post('/contact', [WebsiteController::class, 'storeContactForm'])->name('store-contact-form');
Route::get('/gallery', [WebsiteController::class, 'gallery'])->name('gallery');
Route::get('/admin/login', [AdminController::class, 'showLoginForm'])->name('login');

// Ad Click Tracking Route
Route::get('/ad/click/{ad}', [AdClickController::class, 'click'])->name('ad.click');

// Admin Routes
Route::prefix('admin')->group(function () {
    // Guest routes (not authenticated)
    // Route::middleware('guest')->group(function () {
        Route::get('/login', [AdminController::class, 'showLoginForm'])->name('login');
        Route::post('/login', [AdminController::class, 'login']);
    // });

    // Authenticated routes
    Route::middleware('auth')->group(function () {
        Route::get('/dashboard', [AdminController::class, 'dashboard'])->name('admin.dashboard');
        Route::post('/logout', [AdminController::class, 'logout'])->name('admin.logout');

        // Hero Sliders Management
        Route::resource('hero-sliders', HeroSliderController::class)->names([
            'index' => 'admin.hero-sliders.index',
            'create' => 'admin.hero-sliders.create',
            'store' => 'admin.hero-sliders.store',
            'show' => 'admin.hero-sliders.show',
            'edit' => 'admin.hero-sliders.edit',
            'update' => 'admin.hero-sliders.update',
            'destroy' => 'admin.hero-sliders.destroy',
        ]);

        // President Messages Management
        Route::resource('president-messages', PresidentMessageController::class)->names([
            'index' => 'admin.president-messages.index',
            'create' => 'admin.president-messages.create',
            'store' => 'admin.president-messages.store',
            'show' => 'admin.president-messages.show',
            'edit' => 'admin.president-messages.edit',
            'update' => 'admin.president-messages.update',
            'destroy' => 'admin.president-messages.destroy',
        ]);

        // News Management
        Route::resource('news', NewsController::class)->names([
            'index' => 'admin.news.index',
            'create' => 'admin.news.create',
            'store' => 'admin.news.store',
            'show' => 'admin.news.show',
            'edit' => 'admin.news.edit',
            'update' => 'admin.news.update',
            'destroy' => 'admin.news.destroy',
        ]);

        // Additional news routes
        Route::patch('news/{news}/toggle-featured', [NewsController::class, 'toggleFeatured'])->name('admin.news.toggle-featured');
        Route::patch('news/{news}/toggle-active', [NewsController::class, 'toggleActive'])->name('admin.news.toggle-active');
        
        // Gallery Management
        Route::resource('galleries', GalleryController::class)->names([
            'index' => 'admin.galleries.index',
            'create' => 'admin.galleries.create',
            'store' => 'admin.galleries.store',
            'show' => 'admin.galleries.show',
            'edit' => 'admin.galleries.edit',
            'update' => 'admin.galleries.update',
            'destroy' => 'admin.galleries.destroy',
        ]);
        
        // Additional gallery routes
        Route::patch('galleries/{gallery}/toggle-active', [GalleryController::class, 'toggleActive'])->name('admin.galleries.toggle-active');
        
        // Member Management
        Route::resource('members', MemberController::class)->names([
            'index' => 'admin.members.index',
            'create' => 'admin.members.create',
            'store' => 'admin.members.store',
            'show' => 'admin.members.show',
            'edit' => 'admin.members.edit',
            'update' => 'admin.members.update',
            'destroy' => 'admin.members.destroy',
        ]);
        
        // Additional member routes
        Route::patch('members/{member}/toggle-active', [MemberController::class, 'toggleActive'])->name('admin.members.toggle-active');
        Route::post('members/import', [MemberController::class, 'import'])->name('admin.members.import');
        Route::get('members/export', [MemberController::class, 'export'])->name('admin.members.export');
        Route::post('members/bulk-action', [MemberController::class, 'bulkAction'])->name('admin.members.bulk-action');
        
        // Quotation Management
        Route::resource('quotations', QuotationController::class)->names([
            'index' => 'admin.quotations.index',
            'show' => 'admin.quotations.show',
            'update' => 'admin.quotations.update',
            'destroy' => 'admin.quotations.destroy',
        ]);
        
        // Contact Form Management
        Route::resource('contact-forms', ContactFormController::class)->names([
            'index' => 'admin.contact-forms.index',
            'show' => 'admin.contact-forms.show',
            'update' => 'admin.contact-forms.update',
            'destroy' => 'admin.contact-forms.destroy',
        ]);
        
        // Office Bearers Management
        Route::resource('office-bearers', OfficeBearerController::class)->names([
            'index' => 'admin.office-bearers.index',
            'create' => 'admin.office-bearers.create',
            'store' => 'admin.office-bearers.store',
            'show' => 'admin.office-bearers.show',
            'edit' => 'admin.office-bearers.edit',
            'update' => 'admin.office-bearers.update',
            'destroy' => 'admin.office-bearers.destroy',
        ]);
        
        Route::patch('office-bearers/{officeBearer}/toggle-active', [OfficeBearerController::class, 'toggleActive'])->name('admin.office-bearers.toggle-active');
        
        // Executive Committee Management
        Route::resource('executive-committees', ExecutiveCommitteeController::class)->names([
            'index' => 'admin.executive-committees.index',
            'create' => 'admin.executive-committees.create',
            'store' => 'admin.executive-committees.store',
            'show' => 'admin.executive-committees.show',
            'edit' => 'admin.executive-committees.edit',
            'update' => 'admin.executive-committees.update',
            'destroy' => 'admin.executive-committees.destroy',
        ]);
        
        Route::patch('executive-committees/{executiveCommittee}/toggle-active', [ExecutiveCommitteeController::class, 'toggleActive'])->name('admin.executive-committees.toggle-active');

        // Committee Management
        Route::resource('committees', CommitteeController::class)->names([
            'index' => 'admin.committees.index',
            'create' => 'admin.committees.create',
            'store' => 'admin.committees.store',
            'show' => 'admin.committees.show',
            'edit' => 'admin.committees.edit',
            'update' => 'admin.committees.update',
            'destroy' => 'admin.committees.destroy',
        ]);

        Route::patch('committees/{committee}/toggle-active', [CommitteeController::class, 'toggleActive'])->name('admin.committees.toggle-active');

        // Settings Management
        Route::get('settings', [SettingsController::class, 'index'])->name('admin.settings.index');
        Route::put('settings', [SettingsController::class, 'update'])->name('admin.settings.update');
        Route::post('settings/reset', [SettingsController::class, 'reset'])->name('admin.settings.reset');
        
        // Ads Management
        Route::resource('ads', AdsController::class)->names([
            'index' => 'admin.ads.index',
            'create' => 'admin.ads.create',
            'store' => 'admin.ads.store',
            'show' => 'admin.ads.show',
            'edit' => 'admin.ads.edit',
            'update' => 'admin.ads.update',
            'destroy' => 'admin.ads.destroy',
        ]);
        
        // Additional ads routes
        Route::patch('ads/{ad}/toggle-status', [AdsController::class, 'toggleStatus'])->name('admin.ads.toggle-status');
        Route::post('ads/bulk-action', [AdsController::class, 'bulkAction'])->name('admin.ads.bulk-action');
        Route::get('ads/{ad}/track-click', [AdsController::class, 'trackClick'])->name('admin.ads.track-click');
        Route::get('ads/analytics', [AdsController::class, 'analytics'])->name('admin.ads.analytics');
        Route::post('ads/{ad}/duplicate', [AdsController::class, 'duplicate'])->name('admin.ads.duplicate');
    });
});
