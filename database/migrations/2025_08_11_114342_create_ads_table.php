<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ads', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->enum('type', ['image', 'video'])->default('image');
            $table->string('content_path'); // Path to image or video file
            $table->string('thumbnail_path')->nullable(); // Thumbnail for videos
            $table->text('description')->nullable();
            $table->string('link_url')->nullable(); // URL to redirect when clicked
            $table->enum('position', ['header', 'sidebar', 'footer', 'content', 'popup', 'banner'])->default('sidebar');
            $table->enum('status', ['active', 'inactive', 'scheduled'])->default('active');
            $table->integer('sort_order')->default(0);
            $table->integer('width')->nullable(); // Ad width in pixels
            $table->integer('height')->nullable(); // Ad height in pixels
            $table->datetime('start_date')->nullable(); // When ad should start showing
            $table->datetime('end_date')->nullable(); // When ad should stop showing
            $table->integer('max_impressions')->nullable(); // Maximum number of times to show
            $table->integer('impressions_count')->default(0); // Current impression count
            $table->integer('clicks_count')->default(0); // Current click count
            $table->boolean('open_in_new_tab')->default(true);
            $table->json('target_pages')->nullable(); // Specific pages to show ad on
            $table->string('alt_text')->nullable(); // Alt text for images
            $table->timestamps();

            $table->index(['status', 'position']);
            $table->index(['start_date', 'end_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ads');
    }
};
