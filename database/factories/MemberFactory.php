<?php

namespace Database\Factories;

use App\Models\Member;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Member>
 */
class MemberFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Member::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->company(),
            'address' => $this->faker->city(),
            'full_address' => $this->faker->address(),
            'description' => $this->faker->paragraph(2),

            'emails' => $this->faker->randomElements([
                $this->faker->companyEmail(),
                $this->faker->safeEmail(),
                $this->faker->email()
            ], $this->faker->numberBetween(1, 2)),
            'phones' => $this->faker->randomElements([
                '+91-' . $this->faker->numerify('##########'),
                '0771-' . $this->faker->numerify('#######'),
                $this->faker->phoneNumber()
            ], $this->faker->numberBetween(1, 2)),
            'website' => $this->faker->optional(0.7)->url(),
            'contact_persons' => $this->faker->optional(0.8)->randomElements([
                [
                    'name' => $this->faker->name(),
                    'phone' => '+91-' . $this->faker->numerify('##########')
                ],
                [
                    'name' => $this->faker->name(),
                    'phone' => '+91-' . $this->faker->numerify('##########')
                ]
            ], $this->faker->numberBetween(1, 2)),
            'is_active' => $this->faker->boolean(85), // 85% chance of being active
            'sort_order' => $this->faker->numberBetween(0, 100),
        ];
    }

    /**
     * Indicate that the member is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
        ]);
    }

    /**
     * Indicate that the member is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Create a member with multiple emails and phones.
     */
    public function withMultipleContacts(): static
    {
        return $this->state(fn (array $attributes) => [
            'emails' => [
                $this->faker->companyEmail(),
                $this->faker->safeEmail(),
                $this->faker->email()
            ],
            'phones' => [
                '+91-' . $this->faker->numerify('##########'),
                '0771-' . $this->faker->numerify('#######'),
                '+91-' . $this->faker->numerify('##########')
            ],
            'contact_persons' => [
                [
                    'name' => $this->faker->name(),
                    'phone' => '+91-' . $this->faker->numerify('##########')
                ],
                [
                    'name' => $this->faker->name(),
                    'phone' => '+91-' . $this->faker->numerify('##########')
                ],
                [
                    'name' => $this->faker->name(),
                    'phone' => '+91-' . $this->faker->numerify('##########')
                ]
            ]
        ]);
    }

    /**
     * Create a member with minimal data.
     */
    public function minimal(): static
    {
        return $this->state(fn (array $attributes) => [
            'full_address' => null,
            'description' => null,

            'emails' => [],
            'phones' => [],
            'website' => null,
            'contact_persons' => [],
        ]);
    }



    /**
     * Create a member with specific sort order.
     */
    public function withSortOrder(int $sortOrder): static
    {
        return $this->state(fn (array $attributes) => [
            'sort_order' => $sortOrder,
        ]);
    }
}
