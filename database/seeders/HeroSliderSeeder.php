<?php

namespace Database\Seeders;

use App\Models\HeroSlider;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class HeroSliderSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $sliders = [
            [
                'title' => 'Welcome to Urla Industries <span class="text-warning">Association</span>',
                'subtitle' => 'Leading Industry Association of Chhattisgarh',
                'description' => 'Leading Industry Association of Chhattisgarh, representing over 250+ members in manufacturing steel, aluminium, zinc, power, cement, and much more.',
                'image_path' => 'image/AdobeStock_220757323.jpg',
                'badge_text' => 'Welcome to UIA',
                'badge_icon' => 'fas fa-award',
                'primary_button_text' => 'Learn More',
                'primary_button_link' => '#welcome',
                'secondary_button_text' => 'Our Members',
                'secondary_button_link' => '/uia-members',
                'sort_order' => 1,
                'is_active' => true,
            ],
            [
                'title' => 'Bridging Industries & <span class="text-warning">Government</span>',
                'subtitle' => '40+ Years of Excellence',
                'description' => 'Founded in 1990, UIA has been the voice of industries in Urla Industrial Area, fostering growth and development through strategic partnerships.',
                'image_path' => 'image/istockphoto-1295808919-612x612.jpg',
                'badge_text' => '40+ Years of Excellence',
                'badge_icon' => 'fas fa-handshake',
                'primary_button_text' => 'Our Journey',
                'primary_button_link' => '#about',
                'secondary_button_text' => 'Services',
                'secondary_button_link' => '#services',
                'sort_order' => 2,
                'is_active' => true,
            ],
            [
                'title' => 'Building a <span class="text-warning">Sustainable</span> Tomorrow',
                'subtitle' => 'Environmental Responsibility',
                'description' => 'Committed to environmental responsibility and sustainable industrial practices for a better future for generations to come.',
                'image_path' => 'image/pexels-binyaminmellish-186077.jpg',
                'badge_text' => 'Sustainable Future',
                'badge_icon' => 'fas fa-leaf',
                'primary_button_text' => 'Our Vision',
                'primary_button_link' => '#objectives',
                'secondary_button_text' => 'Contact Us',
                'secondary_button_link' => '/contact',
                'sort_order' => 3,
                'is_active' => true,
            ],
        ];

        foreach ($sliders as $slider) {
            HeroSlider::create($slider);
        }
    }
}
