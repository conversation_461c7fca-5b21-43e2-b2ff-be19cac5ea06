<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Gallery;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\File;

class GallerySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Define gallery images data
        $galleries = [
            [
                'title' => 'Industrial Excellence Award Ceremony',
                'description' => 'Recognition of outstanding achievements in industrial excellence',
                'image_path' => 'image/AdobeStock_220757323.jpg',
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'title' => 'Annual General Meeting 2024',
                'description' => 'Strategic planning session for the upcoming fiscal year',
                'image_path' => 'image/istockphoto-1295808919-612x612.jpg',
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'title' => 'Industrial Facility Tour',
                'description' => 'Showcasing industrial excellence and best practices',
                'image_path' => 'image/pexels-binyaminmellish-186077.jpg',
                'is_active' => true,
                'sort_order' => 3,
            ],
            [
                'title' => 'Community Outreach Program',
                'description' => 'Supporting local development and social initiatives',
                'image_path' => 'image/AdobeStock_220757323.jpg',
                'is_active' => true,
                'sort_order' => 4,
            ],
            [
                'title' => 'Technical Workshop Session',
                'description' => 'Knowledge sharing initiative for industry professionals',
                'image_path' => 'image/istockphoto-1295808919-612x612.jpg',
                'is_active' => true,
                'sort_order' => 5,
            ],
            [
                'title' => 'Leadership Team Meeting',
                'description' => 'Strategic decision making for organizational growth',
                'image_path' => 'image/pexels-binyaminmellish-186077.jpg',
                'is_active' => true,
                'sort_order' => 6,
            ],
        ];

        // Insert gallery images
        foreach ($galleries as $gallery) {
            Gallery::create($gallery);
        }
    }
}
