<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Ad;
use Illuminate\Support\Facades\Storage;

class SampleAdsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create sample ads for testing
        $sampleAds = [
            [
                'title' => 'Welcome to UIA - Header Banner',
                'type' => 'image',
                'content_path' => null, // Will use placeholder
                'description' => 'Join the leading industrial association of Chhattisgarh',
                'link_url' => 'https://example.com/join-uia',
                'position' => 'header',
                'status' => 'active',
                'sort_order' => 1,
                'width' => 800,
                'height' => 200,
                'start_date' => now(),
                'end_date' => now()->addMonths(6),
                'max_impressions' => 10000,
                'open_in_new_tab' => true,
                'target_pages' => ['all'],
                'alt_text' => 'Join UIA - Industrial Association'
            ],
            [
                'title' => 'Industrial Excellence Awards 2024',
                'type' => 'image',
                'content_path' => null,
                'description' => 'Nominate your company for the prestigious Industrial Excellence Awards',
                'link_url' => 'https://example.com/awards-2024',
                'position' => 'banner',
                'status' => 'active',
                'sort_order' => 1,
                'width' => 600,
                'height' => 300,
                'start_date' => now(),
                'end_date' => now()->addMonths(3),
                'max_impressions' => 5000,
                'open_in_new_tab' => true,
                'target_pages' => ['home', 'about-us'],
                'alt_text' => 'Industrial Excellence Awards 2024'
            ],
            [
                'title' => 'Member Benefits Program',
                'type' => 'image',
                'content_path' => null,
                'description' => 'Discover exclusive benefits for UIA members',
                'link_url' => 'https://example.com/member-benefits',
                'position' => 'content',
                'status' => 'active',
                'sort_order' => 1,
                'width' => 400,
                'height' => 250,
                'start_date' => now(),
                'end_date' => now()->addMonths(12),
                'max_impressions' => 8000,
                'open_in_new_tab' => false,
                'target_pages' => ['uia-members', 'about-us'],
                'alt_text' => 'UIA Member Benefits'
            ],
            [
                'title' => 'Contact UIA Today',
                'type' => 'image',
                'content_path' => null,
                'description' => 'Get in touch with us for business opportunities',
                'link_url' => route('contact'),
                'position' => 'footer',
                'status' => 'active',
                'sort_order' => 1,
                'width' => 500,
                'height' => 150,
                'start_date' => now(),
                'end_date' => now()->addYear(),
                'max_impressions' => 15000,
                'open_in_new_tab' => false,
                'target_pages' => ['all'],
                'alt_text' => 'Contact UIA'
            ],
            [
                'title' => 'Special Announcement',
                'type' => 'image',
                'content_path' => null,
                'description' => 'Important updates from UIA leadership',
                'link_url' => 'https://example.com/announcements',
                'position' => 'popup',
                'status' => 'active',
                'sort_order' => 1,
                'width' => 400,
                'height' => 300,
                'start_date' => now(),
                'end_date' => now()->addWeeks(2),
                'max_impressions' => 1000,
                'open_in_new_tab' => true,
                'target_pages' => ['home'],
                'alt_text' => 'UIA Special Announcement'
            ]
        ];

        foreach ($sampleAds as $adData) {
            // Create a placeholder image URL for demonstration
            $adData['content_path'] = $this->createPlaceholderImagePath($adData['width'], $adData['height'], $adData['title']);
            
            Ad::create($adData);
        }

        $this->command->info('Sample ads created successfully!');
    }

    /**
     * Create a placeholder image path for demonstration
     */
    private function createPlaceholderImagePath($width, $height, $title): string
    {
        // Using placeholder.com for demo images
        $encodedTitle = urlencode($title);
        return "https://via.placeholder.com/{$width}x{$height}/007bff/ffffff?text=" . $encodedTitle;
    }
}
