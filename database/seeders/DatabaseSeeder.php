<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Seed admin users first
        $this->call([
            AdminSeeder::class,
            HeroSliderSeeder::class,
            PresidentMessageSeeder::class,
            GallerySeeder::class,
            MemberSeeder::class,
            NewsSeeder::class,
        ]);

        // User::factory(10)->create();
        // Uncomment below if you want a test user
        // User::factory()->create([
        //     'name' => 'Test User',
        //     'email' => '<EMAIL>',
        // ]);
    }
}
