<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class AdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create default admin user
        User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'UIA Admin',
                'email' => '<EMAIL>',
                'password' => Hash::make('admin123'),
                'email_verified_at' => now(),
            ]
        );

        // Create additional admin users if needed
        User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'UIA Super Admin',
                'email' => '<EMAIL>',
                'password' => Hash::make('superadmin123'),
                'email_verified_at' => now(),
            ]
        );

        $this->command->info('Admin users created successfully!');
        $this->command->info('Login credentials:');
        $this->command->info('Email: <EMAIL> | Password: admin123');
        $this->command->info('Email: <EMAIL> | Password: superadmin123');
    }
}
