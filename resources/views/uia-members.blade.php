@extends('layouts.app')

@section('title', 'UIA Members - Urla Industries Association')

@section('content')
    <!-- Content Ads -->
    @if(isset($ads['content']) && $ads['content']->count() > 0)
        <div class="content-ads-container">
            @include('partials.ads', ['ads' => $ads['content'], 'position' => 'content'])
        </div>
    @endif

    <!-- Page Header -->
    <section class="page-header" style="background: url('image/header.jpg'); background-size: cover; background-position: center; background-attachment: fixed;">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center" data-aos="fade-up">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb justify-content-center">
                            <li class="breadcrumb-item"><a href="{{ route('home') }}">Home</a></li>
                            <li class="breadcrumb-item active" aria-current="page">UIA Members</li>
                        </ol>
                    </nav>
                    <h1 class="display-4 fw-bold mb-3">UIA Member Companies</h1>
                    <p class="lead">Discover our diverse network of 250+ member companies across 14+ industry sectors</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Search & Filter Section -->
    <section class="py-4 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-12" data-aos="fade-up">
                    <div class="search-filter-card bg-white p-4 rounded-3 shadow-sm">
                        <div class="row align-items-center">
                            <div class="col-lg-6 mb-3 mb-lg-0">
                                <div class="search-box position-relative">
                                    <input type="text" class="form-control form-control-lg ps-5" id="memberSearch" placeholder="Search companies by name or location...">
                                    <i class="fas fa-search position-absolute top-50 start-0 translate-middle-y ms-3 text-muted"></i>
                                </div>
                            </div>
                            <div class="col-lg-3">
                                <select class="form-select form-select-lg" id="locationFilter">
                                    <option value="">All Locations</option>
                                    <option value="Raipur">Raipur</option>
                                    <option value="Urla">Urla</option>
                                    <option value="Bilaspur">Bilaspur</option>
                                    <option value="Korba">Korba</option>
                                    <option value="Bhilai">Bhilai</option>
                                </select>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="search-results-info">
                                        <span id="memberCount" class="text-muted">Loading members...</span>
                                    </div>
                                    <div class="view-toggle">
                                        <div class="btn-group" role="group" aria-label="View toggle">
                                            <button type="button" class="btn btn-outline-primary active" id="gridView">
                                                <i class="fas fa-th"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-primary" id="listView">
                                                <i class="fas fa-list"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Members Grid Section -->
    <section class="py-5">
        <div class="container">
            <div class="row" id="membersContainer">
                @forelse($members as $member)
                    <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up">
                        <div class="member-card h-100">
                            <div class="member-logo">
                                <i class="fas fa-building fa-3x text-primary"></i>
                            </div>
                            <div class="member-info">
                                <h5 class="member-name">{{ $member->name }}</h5>
                                <p class="member-address">
                                    <i class="fas fa-map-marker-alt me-2 text-muted"></i>
                                    Address: {{ $member->address }}
                                </p>
                                <div class="member-actions">
                                    <button class="btn btn-sm btn-primary me-2" onclick="viewMemberProfile('{{ $member->name }}')">
                                        <i class="fas fa-eye me-1"></i>View Details
                                    </button>
                                    @php
                                            $phoneNumber = null;
                                            // First try to get phone from main phone field
                                            if ($member->phone) {
                                                $phoneNumber = $member->phone;
                                            }
                                            // If no main phone, try to get from first contact person
                                            elseif ($member->contact_persons && is_array($member->contact_persons) && count($member->contact_persons) > 0) {
                                                $firstContact = $member->contact_persons[0];
                                                if (isset($firstContact['phone']) && $firstContact['phone']) {
                                                    $phoneNumber = $firstContact['phone'];
                                                }
                                            }
                                        @endphp
                                        @if($phoneNumber)
                                            <a href="tel:{{ $phoneNumber }}" class="btn btn-sm btn-success">
                                                <i class="fas fa-phone me-1"></i>Call
                                            </a>
                                        @else
                                            <button class="btn btn-sm btn-outline-secondary" disabled>
                                                <i class="fas fa-phone me-1"></i>No Phone
                                            </button>
                                        @endif
                                </div>
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="col-12 text-center py-5">
                        <div class="no-results-card">
                            <i class="fas fa-search fa-3x text-muted mb-3"></i>
                            <h4 class="text-muted">No members found</h4>
                            <p class="text-muted">Try adjusting your search criteria or filters</p>
                            <button class="btn btn-primary" onclick="clearFilters()">Clear Filters</button>
                        </div>
                    </div>
                @endforelse
            </div>
        </div>
    </section>

    <!-- Search Modal -->
    <div class="modal fade" id="searchModal" tabindex="-1" aria-labelledby="searchModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header border-0">
                    <h5 class="modal-title" id="searchModalLabel">Search Members</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="input-group">
                        <input type="text" class="form-control form-control-lg" id="modalSearchInput" placeholder="Search for members or information...">
                        <button class="btn btn-primary" type="button" onclick="performSearch()">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

@endsection

@section('js')

    <!-- Custom JS -->
    <script src="js/members.js"></script>

    @endsection