@extends('layouts.app')

@section('title', 'Gallery - Urla Industries Association')

@section('content')
    <!-- Content Ads -->
    @if(isset($ads['content']) && $ads['content']->count() > 0)
        <div class="content-ads-container">
            @include('partials.ads', ['ads' => $ads['content'], 'position' => 'content'])
        </div>
    @endif
    <!-- Page Header -->
    <section class="page-header" style="background: url('image/header.jpg'); background-size: cover; background-position: center; background-attachment: fixed;">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center" data-aos="fade-up">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb justify-content-center">
                            <li class="breadcrumb-item"><a href="{{ route('home') }}">Home</a></li>
                            <li class="breadcrumb-item active" aria-current="page">Gallery</li>
                        </ol>
                    </nav>
                    <h1 class="display-4 fw-bold mb-3">Photo Gallery</h1>
                    <p class="lead">Explore moments from our events, activities, and achievements</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Gallery Section -->
    <section class="py-5">
        <div class="container">
            <div class="row mb-4">
                <div class="col-12 text-center" data-aos="fade-up">
                    <p class="lead text-muted">A visual journey through our association's history and milestones</p>
                </div>
            </div>

            <!-- Gallery Grid -->
            <div class="row g-4" data-aos="fade-up">
                @forelse($galleries as $gallery)
                    <div class="col-md-6 col-lg-4">
                        <div class="gallery-item rounded-3 overflow-hidden shadow-lg h-100">
                            @if($gallery->image_path)
                                <a href="{{ asset('storage/' . $gallery->image_path) }}" data-fancybox="gallery" data-caption="{{ $gallery->title ?? 'Gallery Image' }}">
                                    <img src="{{ asset('storage/' . $gallery->image_path) }}" alt="{{ $gallery->title ?? 'Gallery Image' }}" class="img-fluid w-100 h-100 object-fit-cover">
                                    <div class="gallery-overlay d-flex align-items-center justify-content-center">
                                        <div class="gallery-info text-center text-white p-3">
                                            <h5 class="mb-1">{{ $gallery->title ?? 'Untitled' }}</h5>
                                            @if($gallery->description)
                                                <p class="mb-0 small">{{ Str::limit($gallery->description, 50) }}</p>
                                            @endif
                                        </div>
                                    </div>
                                </a>
                            @else
                                <div class="bg-light d-flex align-items-center justify-content-center" style="height: 300px;">
                                    <i class="fas fa-image fa-3x text-muted"></i>
                                </div>
                            @endif
                        </div>
                    </div>
                @empty
                    <div class="col-12 text-center">
                        <div class="bg-light p-5 rounded-3">
                            <i class="fas fa-images fa-3x text-muted mb-3"></i>
                            <h3 class="text-muted">No images available</h3>
                            <p class="text-muted">Check back later for updates to our gallery.</p>
                        </div>
                    </div>
                @endforelse
            </div>

            <!-- Additional Gallery Info -->
            <div class="row mt-5" data-aos="fade-up">
                <div class="col-lg-8 mx-auto text-center">
                    <div class="gallery-info-card bg-light p-5 rounded-3 shadow-sm">
                        <h3 class="mb-4 text-primary">Preserving Our Legacy</h3>
                        <p class="lead mb-4">
                            The Urla Industries Association has been documenting our journey through photographs since our inception in 1990. 
                            This gallery showcases key moments from our events, member activities, and community initiatives.
                        </p>
                        <p class="mb-0">
                            <i class="fas fa-camera-retro text-primary me-2"></i>
                            More images will be added regularly as we continue to grow and achieve new milestones.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Lightbox CSS and JS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/fancybox/3.5.7/jquery.fancybox.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fancybox/3.5.7/jquery.fancybox.min.js"></script>

    <script>
        // Initialize Fancybox lightbox
        $(document).ready(function() {
            $('[data-fancybox="gallery"]').fancybox({
                buttons: [
                    "zoom",
                    "share",
                    "slideShow",
                    "fullScreen",
                    "download",
                    "thumbs",
                    "close"
                ],
            });

            // Add hover effect to gallery items
            $('.gallery-item').hover(
                function() {
                    $(this).find('img').css('transform', 'scale(1.05)');
                },
                function() {
                    $(this).find('img').css('transform', 'scale(1)');
                }
            );
        });
    </script>

    <style>
        .gallery-item {
            position: relative;
            transition: all 0.3s ease;
        }

        .gallery-item img {
            transition: transform 0.3s ease;
            aspect-ratio: 4/3;
            object-fit: cover;
        }

        .gallery-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 123, 255, 0.8);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .gallery-item:hover .gallery-overlay {
            opacity: 1;
        }

        .gallery-info-card {
            border-left: 4px solid #0d6efd;
        }
    </style>
@endsection