{{-- Ad Display Component --}}
@if(isset($ads) && $ads->count() > 0)
    <div class="ads-container ads-{{ $position ?? 'default' }}" data-position="{{ $position ?? 'default' }}">
        @foreach($ads as $ad)
            <div class="ad-item mb-3" data-ad-id="{{ $ad->id }}">
                @if($ad->type === 'image')
                    {{-- Image Ad --}}
                    @if($ad->link_url)
                        <a href="{{ route('ad.click', $ad->id) }}" 
                           class="ad-link d-block"
                           @if($ad->open_in_new_tab) target="_blank" @endif
                           title="{{ $ad->alt_text ?? $ad->title }}">
                            <img src="{{ $ad->content_url }}" 
                                 alt="{{ $ad->alt_text ?? $ad->title }}"
                                 class="img-fluid ad-image"
                                 @if($ad->width) style="max-width: {{ $ad->width }}px;" @endif
                                 @if($ad->height) style="max-height: {{ $ad->height }}px;" @endif>
                        </a>
                    @else
                        <img src="{{ $ad->content_url }}" 
                             alt="{{ $ad->alt_text ?? $ad->title }}"
                             class="img-fluid ad-image"
                             @if($ad->width) style="max-width: {{ $ad->width }}px;" @endif
                             @if($ad->height) style="max-height: {{ $ad->height }}px;" @endif>
                    @endif
                @elseif($ad->type === 'video')
                    {{-- Video Ad --}}
                    <div class="ad-video-container">
                        @if($ad->link_url)
                            <a href="{{ route('ad.click', $ad->id) }}" 
                               class="ad-link d-block"
                               @if($ad->open_in_new_tab) target="_blank" @endif
                               title="{{ $ad->title }}">
                        @endif
                        
                        <video class="ad-video w-100" 
                               @if($ad->width) style="max-width: {{ $ad->width }}px;" @endif
                               @if($ad->height) style="max-height: {{ $ad->height }}px;" @endif
                               controls 
                               preload="metadata"
                               @if($ad->thumbnail_url) poster="{{ $ad->thumbnail_url }}" @endif>
                            <source src="{{ $ad->content_url }}" type="video/mp4">
                            Your browser does not support the video tag.
                        </video>
                        
                        @if($ad->link_url)
                            </a>
                        @endif
                    </div>
                @endif
                
                {{-- Ad Description (if any) --}}
                @if($ad->description && ($position === 'content' || $position === 'banner'))
                    <div class="ad-description mt-2">
                        <small class="text-muted">{{ $ad->description }}</small>
                    </div>
                @endif
            </div>
        @endforeach
    </div>
@endif

{{-- Ad Styles --}}
<style>
.ads-container {
    margin: 15px 0;
}

.ads-header {
    text-align: center;
    margin-bottom: 20px;
}

.ads-sidebar {
    margin: 10px 0;
}

.ads-footer {
    text-align: center;
    margin-top: 20px;
}

.ads-content {
    margin: 20px 0;
    text-align: center;
}

.ads-banner {
    text-align: center;
    margin: 25px 0;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.ads-popup {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 9999;
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    max-width: 90vw;
    max-height: 90vh;
}

.ad-item {
    position: relative;
    overflow: hidden;
    border-radius: 8px;
    transition: transform 0.3s ease;
}

.ad-item:hover {
    transform: translateY(-2px);
}

.ad-link {
    text-decoration: none;
    color: inherit;
}

.ad-image, .ad-video {
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: box-shadow 0.3s ease;
}

.ad-image:hover, .ad-video:hover {
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.ad-description {
    font-size: 0.9em;
    line-height: 1.4;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .ads-container {
        margin: 10px 0;
    }
    
    .ads-banner {
        margin: 15px 0;
        padding: 10px;
    }
    
    .ad-image, .ad-video {
        max-width: 100% !important;
        height: auto !important;
    }
}

/* Animation for popup ads */
@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.8);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

.ads-popup {
    animation: fadeInScale 0.3s ease-out;
}
</style>

{{-- JavaScript for ad interactions --}}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle popup ads
    const popupAds = document.querySelectorAll('.ads-popup');
    popupAds.forEach(function(popup) {
        // Add close button to popup ads
        const closeBtn = document.createElement('button');
        closeBtn.innerHTML = '&times;';
        closeBtn.className = 'btn-close position-absolute top-0 end-0 m-2';
        closeBtn.style.cssText = 'background: rgba(0,0,0,0.5); color: white; border: none; width: 30px; height: 30px; border-radius: 50%; font-size: 18px; cursor: pointer; z-index: 10000;';
        
        closeBtn.addEventListener('click', function() {
            popup.style.display = 'none';
        });
        
        popup.appendChild(closeBtn);
        
        // Auto-hide popup after 10 seconds
        setTimeout(function() {
            if (popup.style.display !== 'none') {
                popup.style.display = 'none';
            }
        }, 10000);
    });
    
    // Track ad impressions (optional - can be enhanced with AJAX)
    const adItems = document.querySelectorAll('.ad-item');
    adItems.forEach(function(adItem) {
        // You can add impression tracking here if needed
        // For now, impressions are tracked server-side when ads are loaded
    });
});
</script>
