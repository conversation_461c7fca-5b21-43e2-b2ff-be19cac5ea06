@extends('layouts.app')

@section('title', 'Executive Committee - Urla Industries Association')

@section('content')


    <!-- Page Header -->
    <section class="page-header" style="background: url('image/header.jpg'); background-size: cover; background-position: center; background-attachment: fixed;">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center" data-aos="fade-up">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb justify-content-center">
                            <li class="breadcrumb-item"><a href="{{ route('home') }}">Home</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('about-us') }}">About Us</a></li>
                            <li class="breadcrumb-item active" aria-current="page">Executive Committee</li>
                        </ol>
                    </nav>
                    <h1 class="display-4 fw-bold mb-3">Executive Committee</h1>
                    <p class="lead text-muted">Year-wise Executive Committee Documents</p>
                </div>
            </div>
        </div>
    </section>

   
    <!-- Executive Committee Section -->
    <section class="py-5">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5" data-aos="fade-up">
                    <span class="section-badge">Governance</span>
                    <h2 class="section-title">URLA INDUSTRIES ASSOCIATION</h2>
                    <p class="lead text-muted">Executive Committee Documents</p>
                </div>
            </div>

            <!-- Executive Committee List -->
            <div class="row justify-content-center">
                <div class="col-lg-10">
                    <div class="executive-committee-list">
                        @forelse($executiveCommittees as $executiveCommittee)
                        <div class="committee-year-card mb-4" data-aos="fade-up" data-aos-delay="100">
                            <div class="card border-0 shadow-lg">
                                <div class="card-header bg-gradient-primary text-white py-4">
                                    <div class="row align-items-center">
                                        <div class="col-md-8">
                                            <h4 class="mb-0 fw-bold">
                                                <i class="fas fa-calendar-alt me-3"></i>
                                                {{ $executiveCommittee->title }}
                                            </h4>
                                        </div>
                                        <div class="col-md-4 text-md-end">
                                            @if($executiveCommittee->pdf_path)
                                            <a href="{{ $executiveCommittee->pdf_url }}" target="_blank" class="btn btn-light btn-sm download-btn">
                                                <i class="fas fa-download me-2"></i>Download
                                            </a>
                                            @else
                                            <span class="btn btn-light btn-sm disabled">
                                                <i class="fas fa-download me-2"></i>No Document
                                            </span>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body p-4">
                                    <div class="row align-items-center">
                                        <div class="col-md-8">
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-file-pdf fa-3x text-danger me-4"></i>
                                                <div>
                                                    <h6 class="mb-1 fw-semibold">Executive Committee {{ $executiveCommittee->year }}</h6>
                                                    <p class="text-muted mb-0">{{ $executiveCommittee->description ?? 'Complete list of executive committee members' }}</p>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4 text-md-end">
                                            <span class="badge bg-primary-subtle text-primary px-3 py-2">
                                                <i class="fas fa-users me-1"></i>Committee Members
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @empty
                        <div class="text-center py-5">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Executive Committee Documents Found</h5>
                            <p class="text-muted">Executive committee documents will be available soon.</p>
                        </div>
                        @endforelse
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Search Modal -->
    <div class="modal fade" id="searchModal" tabindex="-1" aria-labelledby="searchModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="searchModalLabel">
                        <i class="fas fa-search me-2"></i>Search UIA
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form>
                        <div class="mb-3">
                            <input type="text" class="form-control form-control-lg" placeholder="Search for members, news, or information...">
                        </div>
                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-2"></i>Search
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->

    <script>
        // Initialize AOS
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true
        });

        // Download function for executive committee documents
        function downloadDocument(year) {
            // This function would handle the actual download
            // For now, it shows an alert
            alert('Download functionality for Executive Committee ' + year + ' will be implemented with actual document links.');

            // In a real implementation, you would:
            // window.open('path/to/executive-committee-' + year + '.pdf', '_blank');
        }
    </script>
@endsection