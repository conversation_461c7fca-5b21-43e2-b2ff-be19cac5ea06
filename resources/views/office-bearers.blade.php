@extends('layouts.app')

@section('title', 'Office Bearers - Urla Industries Association')

@section('content')

    <!-- Page Header -->
    <section class="page-header" style="background: url('image/header.jpg'); background-size: cover; background-position: center; background-attachment: fixed;">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center" data-aos="fade-up">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb justify-content-center">
                            <li class="breadcrumb-item"><a href="{{ route('home') }}">Home</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('about-us') }}">About Us</a></li>
                            <li class="breadcrumb-item active" aria-current="page">Office Bearers</li>
                        </ol>
                    </nav>
                    <h1 class="display-4 fw-bold mb-3">Office Bearers</h1>
                    <p class="lead text-muted">Year-wise List of Office Bearers</p>
                </div>
            </div>
        </div>
    </section>

     <!-- Committee Members Section -->
    @if($committees->count() > 0)
    <section class="py-5 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5" data-aos="fade-up">
                    <span class="section-badge">Leadership</span>
                    <h2 class="section-title">Office Bearers Members</h2>
                    <p class="lead text-muted">Current Office Bearers members</p>
                </div>
            </div>

            <div class="row justify-content-center">
                @foreach($committees as $committee)
                <div class="col-lg-3 col-md-4 col-sm-6 mb-4" data-aos="fade-up" data-aos-delay="{{ $loop->index * 100 }}">
                    <div class="committee-member-card h-100">
                        <div class="card border-0 shadow-sm h-100">
                            <div class="card-body text-center p-4">
                                <div class="member-image-wrapper mb-3">
                                    @if($committee->image_path)
                                        <img src="{{ $committee->image_url }}"
                                             alt="{{ $committee->name }}"
                                             class="rounded-circle shadow-sm member-image">
                                    @else
                                        <div class="member-placeholder rounded-circle shadow-sm d-flex align-items-center justify-content-center mx-auto">
                                            <i class="fas fa-user text-muted"></i>
                                        </div>
                                    @endif
                                </div>
                                <h5 class="card-title fw-bold text-dark mb-2">{{ $committee->name }}</h5>
                                <p class="card-text text-muted mb-0">{{ $committee->position }}</p>
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </section>
    @endif


    <!-- Office Bearers Section -->
    <section class="py-5">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5" data-aos="fade-up">
                    <span class="section-badge">Leadership</span>
                    <h2 class="section-title">URLA INDUSTRIES ASSOCIATION</h2>
                    <p class="lead text-muted">Year-wise List of Office Bearers</p>
                </div>
            </div>

            <!-- Office Bearers List -->
            <div class="row justify-content-center">
                <div class="col-lg-10">
                    <div class="office-bearers-list">
                        @forelse($officeBearers as $officeBearer)
                        <div class="bearer-year-card mb-4" data-aos="fade-up" data-aos-delay="100">
                            <div class="card border-0 shadow-lg">
                                <div class="card-header bg-gradient-primary text-white py-4">
                                    <div class="row align-items-center">
                                        <div class="col-md-8">
                                            <h4 class="mb-0 fw-bold">
                                                <i class="fas fa-calendar-alt me-3"></i>
                                                {{ $officeBearer->title }}
                                            </h4>
                                        </div>
                                        <div class="col-md-4 text-md-end">
                                            @if($officeBearer->pdf_path)
                                                <a href="{{ $officeBearer->pdf_url }}" class="btn btn-light btn-sm download-btn" target="_blank">
                                                    <i class="fas fa-download me-2"></i>Download
                                                </a>
                                            @else
                                                <button class="btn btn-light btn-sm" disabled>
                                                    <i class="fas fa-download me-2"></i>Download
                                                </button>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body p-4">
                                    <div class="row align-items-center">
                                        <div class="col-md-8">
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-file-pdf fa-3x text-danger me-4"></i>
                                                <div>
                                                    <h6 class="mb-1 fw-semibold">{{ $officeBearer->year }}</h6>
                                                    <p class="text-muted mb-0">{{ $officeBearer->description ?? 'Complete list of office bearers' }}</p>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4 text-md-end">
                                            <span class="badge bg-primary-subtle text-primary px-3 py-2">
                                                <i class="fas fa-users me-1"></i>Office Bearers
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @empty
                        <div class="text-center py-5">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Office Bearers Found</h5>
                            <p class="text-muted">There are currently no office bearers documents available.</p>
                        </div>
                        @endforelse
                    </div>
                </div>
            </div>
        </div>
    </section>

   
@endsection

@section('js')   
    <script>
        // Initialize AOS
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true
        });

    </script>
@endsection