@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-9 col-md-8">
            <!-- Banner Ads -->
            @if(isset($ads['banner']) && $ads['banner']->count() > 0)
                @include('partials.ads', ['ads' => $ads['banner'], 'position' => 'banner'])
            @endif

            <!-- Page Content -->
            @yield('page-content')

            <!-- Content Ads -->
            @if(isset($ads['content']) && $ads['content']->count() > 0)
                @include('partials.ads', ['ads' => $ads['content'], 'position' => 'content'])
            @endif
        </div>

        <!-- Sidebar -->
        <div class="col-lg-3 col-md-4">
            <div class="sidebar">
                <!-- Sidebar Ads -->
                @if(isset($ads['sidebar']) && $ads['sidebar']->count() > 0)
                    @include('partials.ads', ['ads' => $ads['sidebar'], 'position' => 'sidebar'])
                @endif

                <!-- Additional Sidebar Content -->
                @yield('sidebar-content')

                <!-- Default Sidebar Content -->
                @if(!View::hasSection('sidebar-content'))
                    <!-- Latest News Widget -->
                    <div class="sidebar-widget mb-4">
                        <h5 class="widget-title">Latest News</h5>
                        <div class="widget-content">
                            @php
                                $latestNews = \App\Models\News::active()->published()->ordered()->limit(5)->get();
                            @endphp
                            @foreach($latestNews as $newsItem)
                                <div class="news-item-small mb-3">
                                    <h6 class="mb-1">
                                        <a href="{{ route('news-detail', $newsItem) }}" class="text-decoration-none">
                                            {{ Str::limit($newsItem->title, 60) }}
                                        </a>
                                    </h6>
                                    <small class="text-muted">
                                        {{ $newsItem->published_at ? $newsItem->published_at->format('M d, Y') : $newsItem->created_at->format('M d, Y') }}
                                    </small>
                                </div>
                            @endforeach
                        </div>
                    </div>

                    <!-- Quick Links Widget -->
                    <div class="sidebar-widget mb-4">
                        <h5 class="widget-title">Quick Links</h5>
                        <div class="widget-content">
                            <ul class="list-unstyled">
                                <li class="mb-2"><a href="{{ route('about-us') }}" class="text-decoration-none">About Us</a></li>
                                <li class="mb-2"><a href="{{ route('uia-members') }}" class="text-decoration-none">UIA Members</a></li>
                                <li class="mb-2"><a href="{{ route('office-bearers') }}" class="text-decoration-none">Office Bearers</a></li>
                                <li class="mb-2"><a href="{{ route('get-quotation') }}" class="text-decoration-none">Get Quotation</a></li>
                                <li class="mb-2"><a href="{{ route('contact') }}" class="text-decoration-none">Contact Us</a></li>
                            </ul>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<style>
.sidebar {
    padding: 20px 0;
}

.sidebar-widget {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.widget-title {
    color: #333;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 2px solid #007bff;
}

.news-item-small h6 a {
    color: #333;
    transition: color 0.3s ease;
}

.news-item-small h6 a:hover {
    color: #007bff;
}

.widget-content ul li a {
    color: #666;
    transition: color 0.3s ease;
}

.widget-content ul li a:hover {
    color: #007bff;
}

@media (max-width: 768px) {
    .sidebar {
        margin-top: 30px;
        padding: 15px 0;
    }
    
    .sidebar-widget {
        padding: 15px;
        margin-bottom: 20px;
    }
}
</style>
@endsection
