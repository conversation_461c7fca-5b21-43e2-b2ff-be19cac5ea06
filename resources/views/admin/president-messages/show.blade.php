@extends('layouts.admin')

@section('title', 'View President Message')

@section('content')
<div class="row">
    <div class="col-lg-8">
        <div class="admin-card">
            <h5 class="admin-card-title">President Message Details</h5>
            
            <div class="row mb-4">
                <div class="col-sm-3"><strong>Name:</strong></div>
                <div class="col-sm-9">{{ $presidentMessage->name }}</div>
            </div>
            
            <div class="row mb-4">
                <div class="col-sm-3"><strong>Position:</strong></div>
                <div class="col-sm-9">{{ $presidentMessage->position }}</div>
            </div>
            
            <div class="row mb-4">
                <div class="col-sm-3"><strong>Message:</strong></div>
                <div class="col-sm-9">{{ $presidentMessage->message }}</div>
            </div>
            
            @if($presidentMessage->objectives)
            <div class="row mb-4">
                <div class="col-sm-3"><strong>Objectives:</strong></div>
                <div class="col-sm-9">
                    <ul>
                        @foreach($presidentMessage->objectives as $objective)
                        <li>{{ $objective }}</li>
                        @endforeach
                    </ul>
                </div>
            </div>
            @endif
            
            <div class="row mb-4">
                <div class="col-sm-3"><strong>Sort Order:</strong></div>
                <div class="col-sm-9">
                    <span class="badge bg-secondary">{{ $presidentMessage->sort_order }}</span>
                </div>
            </div>
            
            <div class="row mb-4">
                <div class="col-sm-3"><strong>Status:</strong></div>
                <div class="col-sm-9">
                    @if($presidentMessage->is_active)
                        <span class="badge bg-success">Active</span>
                    @else
                        <span class="badge bg-danger">Inactive</span>
                    @endif
                </div>
            </div>
            
            <div class="row mb-4">
                <div class="col-sm-3"><strong>Created:</strong></div>
                <div class="col-sm-9">{{ $presidentMessage->created_at->format('F d, Y \a\t g:i A') }}</div>
            </div>
            
            <div class="row mb-4">
                <div class="col-sm-3"><strong>Last Updated:</strong></div>
                <div class="col-sm-9">{{ $presidentMessage->updated_at->format('F d, Y \a\t g:i A') }}</div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="admin-card">
            <h5 class="admin-card-title">Profile Image</h5>
            @if($presidentMessage->profile_image_path)
                <img src="{{ $presidentMessage->profile_image_url }}" alt="{{ $presidentMessage->name }}" 
                     class="img-fluid rounded">
            @else
                <div class="bg-light text-center p-5">
                    <i class="fas fa-user fa-3x text-muted"></i>
                    <p class="mt-3">No profile image</p>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Logout Form -->
<form id="logout-form" action="{{ route('admin.logout') }}" method="POST" class="d-none">
    @csrf
</form>
@endsection