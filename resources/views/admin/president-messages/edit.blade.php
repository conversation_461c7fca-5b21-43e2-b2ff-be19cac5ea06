@extends('layouts.admin')

@section('title', 'Edit President Message')

@section('content')
<div class="admin-main">
        <div class="admin-content">
            @if($errors->any())
                <div class="admin-alert admin-alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <strong>Please fix the following errors:</strong>
                    <ul class="mb-0 mt-2">
                        @foreach($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif
            
            <div class="admin-card">
                <h5 class="admin-card-title">Edit President Message Information</h5>
                
                <form action="{{ route('admin.president-messages.update', $presidentMessage) }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    @method('PUT')
                    
                    <div class="row">
                        <div class="col-md-8">
                            <!-- Basic Information -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="name" class="form-label">Name <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                               id="name" name="name" value="{{ old('name', $presidentMessage->name) }}" required>
                                        @error('name')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="position" class="form-label">Position <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control @error('position') is-invalid @enderror" 
                                               id="position" name="position" value="{{ old('position', $presidentMessage->position) }}" required>
                                        @error('position')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Message -->
                            <div class="mb-3">
                                <label for="message" class="form-label">Message <span class="text-danger">*</span></label>
                                <textarea class="form-control @error('message') is-invalid @enderror" 
                                          id="message" name="message" rows="10" required>{{ old('message', $presidentMessage->message) }}</textarea>
                                <div class="form-text">Write the complete president message here.</div>
                                @error('message')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <!-- Objectives -->
                            <div class="mb-3">
                                <label class="form-label">Major Objectives</label>
                                <div id="objectivesContainer">
                                    @if(old('objectives') || $presidentMessage->objectives)
                                        @foreach(old('objectives', $presidentMessage->objectives ?? []) as $index => $objective)
                                        <div class="objective-item mb-2">
                                            <div class="input-group">
                                                <span class="input-group-text">{{ $index + 1 }}.</span>
                                                <textarea class="form-control" name="objectives[]" rows="2" placeholder="Enter objective...">{{ $objective }}</textarea>
                                                <button type="button" class="btn btn-outline-danger" onclick="removeObjective(this)">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                        @endforeach
                                    @else
                                        <div class="objective-item mb-2">
                                            <div class="input-group">
                                                <span class="input-group-text">1.</span>
                                                <textarea class="form-control" name="objectives[]" rows="2" placeholder="Enter objective..."></textarea>
                                                <button type="button" class="btn btn-outline-danger" onclick="removeObjective(this)">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                    @endif
                                </div>
                                <button type="button" class="btn btn-outline-primary btn-sm mt-2" onclick="addObjective()">
                                    <i class="fas fa-plus me-1"></i>Add Objective
                                </button>
                                <div class="form-text">Add the major objectives for the association.</div>
                            </div>
                            
                            <!-- Action Buttons -->
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>Update Message
                                </button>
                                <a href="{{ route('admin.president-messages.index') }}" class="btn btn-secondary">
                                    <i class="fas fa-times me-2"></i>Cancel
                                </a>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <!-- Current Profile Image -->
                            @if($presidentMessage->profile_image_path)
                            <div class="mb-3">
                                <label class="form-label">Current Profile Image</label>
                                <div>
                                    <img src="{{ $presidentMessage->profile_image_url }}" alt="{{ $presidentMessage->name }}" 
                                         class="img-fluid rounded" style="max-height: 200px;">
                                </div>
                            </div>
                            @endif
                            
                            <!-- Profile Image Upload -->
                            <div class="mb-3">
                                <label for="profile_image" class="form-label">{{ $presidentMessage->profile_image_path ? 'New Profile Image' : 'Profile Image' }}</label>
                                <input type="file" class="form-control @error('profile_image') is-invalid @enderror" 
                                       id="profile_image" name="profile_image" accept="image/*">
                                <div class="form-text">{{ $presidentMessage->profile_image_path ? 'Leave empty to keep current image.' : '' }} Recommended size: 300x300px. Max size: 2MB</div>
                                @error('profile_image')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <!-- Preview -->
                            <div class="mb-3">
                                <img id="imagePreview" src="#" alt="Preview" class="img-fluid rounded" style="display: none; max-height: 200px;">
                            </div>
                            
                            <!-- Settings -->
                            <div class="mb-3">
                                <label for="sort_order" class="form-label">Sort Order <span class="text-danger">*</span></label>
                                <input type="number" class="form-control @error('sort_order') is-invalid @enderror" 
                                       id="sort_order" name="sort_order" value="{{ old('sort_order', $presidentMessage->sort_order) }}" min="0" required>
                                @error('sort_order')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" 
                                           {{ old('is_active', $presidentMessage->is_active) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="is_active">
                                        Active Status
                                    </label>
                                </div>
                                <div class="form-text">Only active messages will be displayed on the website.</div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Logout Form -->
<form id="logout-form" action="{{ route('admin.logout') }}" method="POST" class="d-none">
    @csrf
</form>

<script>
// Image preview
document.getElementById('profile_image').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const preview = document.getElementById('imagePreview');
            preview.src = e.target.result;
            preview.style.display = 'block';
        };
        reader.readAsDataURL(file);
    }
});

// Objectives management
let objectiveCount = {{ count(old('objectives', $presidentMessage->objectives ?? [])) }};

function addObjective() {
    objectiveCount++;
    const container = document.getElementById('objectivesContainer');
    const div = document.createElement('div');
    div.className = 'objective-item mb-2';
    div.innerHTML = `
        <div class="input-group">
            <span class="input-group-text">${objectiveCount}.</span>
            <textarea class="form-control" name="objectives[]" rows="2" placeholder="Enter objective..."></textarea>
            <button type="button" class="btn btn-outline-danger" onclick="removeObjective(this)">
                <i class="fas fa-trash"></i>
            </button>
        </div>
    `;
    container.appendChild(div);
    updateObjectiveNumbers();
}

function removeObjective(button) {
    button.closest('.objective-item').remove();
    updateObjectiveNumbers();
}

function updateObjectiveNumbers() {
    const objectives = document.querySelectorAll('.objective-item');
    objectives.forEach((item, index) => {
        const numberSpan = item.querySelector('.input-group-text');
        numberSpan.textContent = (index + 1) + '.';
    });
    objectiveCount = objectives.length;
}
</script>
@endsection
