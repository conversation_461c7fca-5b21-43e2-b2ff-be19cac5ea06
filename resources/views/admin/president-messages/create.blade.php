@extends('layouts.admin')

@section('title', 'Create President Message')

@section('content')
<div class="admin-main-content flex-grow-1">
        <div class="admin-content">
            @if($errors->any())
                <div class="admin-alert admin-alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <strong>Please fix the following errors:</strong>
                    <ul class="mb-0 mt-2">
                        @foreach($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif
            
            <div class="admin-card">
                <h5 class="admin-card-title">President Message Information</h5>
                
                <form action="{{ route('admin.president-messages.store') }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    
                    <div class="row">
                        <div class="col-md-8">
                            <!-- Basic Information -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="admin-form-group">
                                        <label for="name" class="admin-form-label">Name <span class="text-danger">*</span></label>
                                        <input type="text" class="admin-form-control @error('name') is-invalid @enderror"
                                               id="name" name="name" value="{{ old('name') }}" required>
                                        @error('name')
                                            <div class="admin-invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="admin-form-group">
                                        <label for="position" class="admin-form-label">Position <span class="text-danger">*</span></label>
                                        <input type="text" class="admin-form-control @error('position') is-invalid @enderror"
                                               id="position" name="position" value="{{ old('position', 'President') }}" required>
                                        @error('position')
                                            <div class="admin-invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            
                            <div class="admin-form-group">
                                <label for="message" class="admin-form-label">Message <span class="text-danger">*</span></label>
                                <textarea class="admin-form-control @error('message') is-invalid @enderror"
                                          id="message" name="message" rows="8" required>{{ old('message') }}</textarea>
                                @error('message')
                                    <div class="admin-invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <!-- Objectives Section -->
                            <div class="admin-form-group">
                                <label class="admin-form-label">Objectives (Optional)</label>
                                <div id="objectives-container">
                                    @if(old('objectives'))
                                        @foreach(old('objectives') as $index => $objective)
                                        <div class="objective-item mb-2">
                                            <div class="input-group">
                                                <input type="text" class="admin-form-control" name="objectives[]"
                                                       placeholder="Enter objective..." value="{{ $objective }}">
                                                <button type="button" class="btn btn-outline-danger remove-objective"
                                                        style="{{ $index === 0 && count(old('objectives')) === 1 ? 'display: none;' : '' }}">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                        @endforeach
                                    @else
                                    <div class="objective-item mb-2">
                                        <div class="input-group">
                                            <input type="text" class="admin-form-control" name="objectives[]"
                                                   placeholder="Enter objective...">
                                            <button type="button" class="btn btn-outline-danger remove-objective" style="display: none;">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    @endif
                                </div>
                                <button type="button" class="btn btn-sm btn-outline-primary mt-2" id="add-objective">
                                    <i class="fas fa-plus me-1"></i>Add Objective
                                </button>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <!-- Profile Image Upload -->
                            <div class="admin-form-group">
                                <label for="profile_image" class="admin-form-label">Profile Image</label>
                                <input type="file" class="admin-form-control @error('profile_image') is-invalid @enderror"
                                       id="profile_image" name="profile_image" accept="image/*">
                                <div class="form-text">Recommended size: 400x400px. Max size: 2MB</div>
                                @error('profile_image')
                                    <div class="admin-invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <!-- Preview -->
                            <div class="admin-form-group">
                                <img id="imagePreview" src="#" alt="Preview" class="img-fluid rounded" style="display: none; max-height: 200px;">
                            </div>
                            
                            <!-- Settings -->
                            <div class="admin-form-group">
                                <label for="sort_order" class="admin-form-label">Sort Order <span class="text-danger">*</span></label>
                                <input type="number" class="admin-form-control @error('sort_order') is-invalid @enderror"
                                       id="sort_order" name="sort_order" value="{{ old('sort_order', 0) }}" min="0" required>
                                @error('sort_order')
                                    <div class="admin-invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="admin-form-group">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1"
                                           {{ old('is_active', true) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="is_active">
                                        Active
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-end gap-2 mt-4">
                        <a href="{{ route('admin.president-messages.index') }}" class="btn btn-secondary">Cancel</a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Create Message
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Logout Form -->
<form id="logout-form" action="{{ route('admin.logout') }}" method="POST" class="d-none">
    @csrf
</form>

<script>
// Image preview
document.getElementById('profile_image').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const preview = document.getElementById('imagePreview');
            preview.src = e.target.result;
            preview.style.display = 'block';
        };
        reader.readAsDataURL(file);
    }
});

// Objectives management
let objectiveCount = 1;

document.getElementById('add-objective').addEventListener('click', function() {
    const container = document.getElementById('objectives-container');
    const newObjective = document.createElement('div');
    newObjective.className = 'objective-item mb-2';
    newObjective.innerHTML = `
        <div class="input-group">
            <input type="text" class="form-control" name="objectives[]" placeholder="Enter objective...">
            <button type="button" class="btn btn-outline-danger remove-objective">
                <i class="fas fa-trash"></i>
            </button>
        </div>
    `;
    container.appendChild(newObjective);
    objectiveCount++;
    
    // Show remove buttons if more than one objective
    if (objectiveCount > 1) {
        document.querySelectorAll('.remove-objective').forEach(btn => {
            btn.style.display = 'block';
        });
    }
});

// Remove objective
document.addEventListener('click', function(e) {
    if (e.target.closest('.remove-objective')) {
        e.target.closest('.objective-item').remove();
        objectiveCount--;
        
        // Hide remove buttons if only one objective left
        if (objectiveCount <= 1) {
            document.querySelectorAll('.remove-objective').forEach(btn => {
                btn.style.display = 'none';
            });
        }
    }
});
</script>
@endsection
