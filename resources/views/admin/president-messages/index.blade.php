@extends('layouts.admin')

@section('title', 'President Messages Management')

@section('content')
<!-- Enhanced Header Section -->
<div class="admin-page-header">
    <div class="d-flex justify-content-between align-items-start mb-4">
        <div class="page-header-content">
            <div class="d-flex align-items-center mb-2">
                <div class="page-icon me-3">
                    <i class="fas fa-user-tie"></i>
                </div>
                <div>
                    <h2 class="admin-title mb-1">President Messages Management</h2>
                    <p class="text-muted mb-0">Manage presidential messages and communications to members</p>
                </div>
            </div>
            <div class="page-breadcrumb d-none">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item active">President Messages</li>
                    </ol>
                </nav>
            </div>
        </div>

        <div class="page-actions d-none">
            <div class="d-flex gap-2">
                <button class="btn btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#messagesPreviewModal">
                    <i class="fas fa-eye me-2"></i>Preview Messages
                </button>
                <a href="{{ route('admin.president-messages.create') }}" class="btn btn-primary btn-enhanced">
                    <i class="fas fa-plus me-2"></i>Add New Message
                </a>
            </div>
        </div>
    </div>

    <!-- Enhanced Stats Cards -->
    <div class="row mb-4 d-none">
        <div class="col-md-3">
            <div class="stats-card stats-card-primary">
                <div class="stats-icon">
                    <i class="fas fa-envelope"></i>
                </div>
                <div class="stats-content">
                    <h3>{{ $messages->count() }}</h3>
                    <p>Total Messages</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card stats-card-success">
                <div class="stats-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stats-content">
                    <h3>{{ $messages->where('is_active', true)->count() }}</h3>
                    <p>Published Messages</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card stats-card-warning">
                <div class="stats-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stats-content">
                    <h3>{{ $messages->where('is_active', false)->count() }}</h3>
                    <p>Draft Messages</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card stats-card-info">
                <div class="stats-icon">
                    <i class="fas fa-calendar-plus"></i>
                </div>
                <div class="stats-content">
                    <h3>{{ $messages->where('created_at', '>=', now()->subDays(30))->count() }}</h3>
                    <p>This Month</p>
                </div>
            </div>
        </div>
    </div>
</div>

@if(session('success'))
    <div class="admin-alert admin-alert-success">
        <i class="fas fa-check-circle me-2"></i>
        {{ session('success') }}
    </div>
@endif

@if(session('error'))
    <div class="admin-alert admin-alert-danger">
        <i class="fas fa-exclamation-circle me-2"></i>
        {{ session('error') }}
    </div>
@endif

<div class="admin-card enhanced-card">
    <div class="card-header-enhanced">
        <div class="d-flex justify-content-between align-items-center">
            <div class="card-title-section">
                <h5 class="admin-card-title mb-1">
                    <i class="fas fa-comments me-2"></i>Presidential Messages
                </h5>
                <p class="card-subtitle">Manage messages from the president to association members</p>
            </div>
            <div class="card-actions">
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-sm btn-outline-secondary" id="cardViewBtn">
                        <i class="fas fa-th-large"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary active" id="tableViewBtn">
                        <i class="fas fa-list"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    @if($messages->count() > 0)
        <!-- Table View -->
        <div id="tableView" class="view-container">
            <div class="admin-table-responsive enhanced-table">
                <table class="admin-table">
                    <thead>
                        <tr>
                            <th class="sortable" data-sort="profile">
                                <i class="fas fa-user-circle me-2"></i>Profile
                            </th>
                            <th class="sortable" data-sort="name">
                                <i class="fas fa-user me-2"></i>Name & Position
                            </th>
                            <th class="sortable" data-sort="message">
                                <i class="fas fa-comment me-2"></i>Message Preview
                            </th>
                            <th class="sortable" data-sort="order">
                                <i class="fas fa-sort-numeric-up me-2"></i>Order
                            </th>
                            <th class="sortable" data-sort="status">
                                <i class="fas fa-toggle-on me-2"></i>Status
                            </th>
                            <th class="sortable" data-sort="created">
                                <i class="fas fa-calendar me-2"></i>Created
                            </th>
                            <th class="text-center">
                                <i class="fas fa-cogs me-2"></i>Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($messages as $message)
                        <tr class="table-row-enhanced">
                            <td>
                                <div class="profile-preview-container">
                                    <img src="{{ $message->profile_image_url }}" alt="{{ $message->name }}"
                                         class="president-profile-img"
                                         data-bs-toggle="modal"
                                         data-bs-target="#profileModal{{ $message->id }}" height="80px">
                                    <div class="profile-overlay">
                                        <i class="fas fa-search-plus"></i>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="president-info">
                                    <h6 class="president-name">{{ $message->name }}</h6>
                                    <p class="president-position">{{ $message->position }}</p>
                                    @if($message->tenure_start || $message->tenure_end)
                                        <small class="president-tenure">
                                            <i class="fas fa-calendar-alt me-1"></i>
                                            @if($message->tenure_start)
                                                {{ \Carbon\Carbon::parse($message->tenure_start)->format('Y') }}
                                            @endif
                                            @if($message->tenure_start && $message->tenure_end)
                                                -
                                            @endif
                                            @if($message->tenure_end)
                                                {{ \Carbon\Carbon::parse($message->tenure_end)->format('Y') }}
                                            @endif
                                        </small>
                                    @endif
                                </div>
                            </td>
                            <td>
                                <div class="message-preview">
                                    <div class="message-excerpt">
                                        {{ $message->message_excerpt }}
                                    </div>
                                    <div class="message-meta">
                                        <small class="text-muted">
                                            <i class="fas fa-align-left me-1"></i>
                                            {{ str_word_count(strip_tags($message->message)) }} words
                                        </small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="order-badge-container">
                                    <span class="order-badge">{{ $message->sort_order }}</span>
                                </div>
                            </td>
                            <td>
                                <div class="status-container">
                                    @if($message->is_active)
                                        <span class="status-badge status-active">
                                            <i class="fas fa-check-circle me-1"></i>Published
                                        </span>
                                    @else
                                        <span class="status-badge status-inactive">
                                            <i class="fas fa-clock me-1"></i>Draft
                                        </span>
                                    @endif
                                </div>
                            </td>
                            <td>
                                <div class="date-container">
                                    <span class="date-primary">{{ $message->created_at->format('M d, Y') }}</span>
                                    <small class="date-secondary">{{ $message->created_at->format('g:i A') }}</small>
                                </div>
                            </td>
                            <td>
                                <div class="action-buttons">
                                    <a href="{{ route('admin.president-messages.show', $message) }}"
                                       class="btn btn-sm btn-action btn-view"
                                       title="View Message"
                                       data-bs-toggle="tooltip">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('admin.president-messages.edit', $message) }}"
                                       class="btn btn-sm btn-action btn-edit"
                                       title="Edit Message"
                                       data-bs-toggle="tooltip">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button"
                                            class="btn btn-sm btn-action btn-delete"
                                            onclick="confirmDelete({{ $message->id }})"
                                            title="Delete Message"
                                            data-bs-toggle="tooltip">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Card View -->
        <div id="cardView" class="view-container d-none">
            <div class="row">
                @foreach($messages as $message)
                <div class="col-lg-6 col-xl-4 mb-4">
                    <div class="president-message-card">
                        <div class="card-header-section">
                            <div class="president-profile">
                                <img src="{{ $message->profile_image_url }}" alt="{{ $message->name }}" class="profile-image">
                                <div class="president-details">
                                    <h6 class="president-name">{{ $message->name }}</h6>
                                    <p class="president-position">{{ $message->position }}</p>
                                </div>
                            </div>
                            <div class="message-status">
                                @if($message->is_active)
                                    <span class="status-badge status-active">
                                        <i class="fas fa-check-circle"></i> Published
                                    </span>
                                @else
                                    <span class="status-badge status-inactive">
                                        <i class="fas fa-clock"></i> Draft
                                    </span>
                                @endif
                            </div>
                        </div>
                        <div class="card-body-section">
                            <div class="message-content">
                                <p class="message-excerpt">{{ $message->message_excerpt }}</p>
                                <div class="message-stats">
                                    <small class="text-muted">
                                        <i class="fas fa-align-left me-1"></i>
                                        {{ str_word_count(strip_tags($message->message)) }} words
                                    </small>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer-section">
                            <div class="card-meta">
                                <small class="text-muted">
                                    <i class="fas fa-calendar me-1"></i>
                                    Created {{ $message->created_at->format('M d, Y') }}
                                </small>
                                <span class="order-badge">{{ $message->sort_order }}</span>
                            </div>
                            <div class="card-actions">
                                <a href="{{ route('admin.president-messages.show', $message) }}"
                                   class="btn btn-sm btn-outline-info" title="View">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ route('admin.president-messages.edit', $message) }}"
                                   class="btn btn-sm btn-outline-primary" title="Edit">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button type="button" class="btn btn-sm btn-outline-danger"
                                        onclick="confirmDelete({{ $message->id }})" title="Delete">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    @else
        <div class="empty-state">
            <div class="empty-state-content">
                <div class="empty-state-icon">
                    <i class="fas fa-user-tie"></i>
                </div>
                <h4 class="empty-state-title">No President Messages Found</h4>
                <p class="empty-state-description">
                    Create presidential messages to communicate important announcements, updates, and insights
                    to your association members. These messages help maintain strong leadership communication.
                </p>
                <div class="empty-state-actions">
                    <a href="{{ route('admin.president-messages.create') }}" class="btn btn-primary btn-enhanced">
                        <i class="fas fa-plus me-2"></i>Create First Message
                    </a>
                    <button class="btn btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#helpModal">
                        <i class="fas fa-question-circle me-2"></i>Learn More
                    </button>
                </div>
            </div>
        </div>
    @endif
</div>

<!-- Profile Preview Modals -->
@foreach($messages as $message)
<div class="modal fade" id="profileModal{{ $message->id }}" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ $message->name }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <img src="{{ $message->profile_image_url }}" alt="{{ $message->name }}"
                     class="img-fluid rounded-circle mb-3" style="width: 150px; height: 150px; object-fit: cover;">
                <h6>{{ $message->name }}</h6>
                <p class="text-muted">{{ $message->position }}</p>
                @if($message->tenure_start || $message->tenure_end)
                    <p class="text-muted">
                        <i class="fas fa-calendar-alt me-1"></i>
                        @if($message->tenure_start)
                            {{ \Carbon\Carbon::parse($message->tenure_start)->format('M Y') }}
                        @endif
                        @if($message->tenure_start && $message->tenure_end)
                            -
                        @endif
                        @if($message->tenure_end)
                            {{ \Carbon\Carbon::parse($message->tenure_end)->format('M Y') }}
                        @endif
                    </p>
                @endif
            </div>
        </div>
    </div>
</div>
@endforeach

<!-- Messages Preview Modal -->
<div class="modal fade" id="messagesPreviewModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-eye me-2"></i>President Messages Preview
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                @if($messages->where('is_active', true)->count() > 0)
                    <div class="row">
                        @foreach($messages->where('is_active', true)->take(6) as $message)
                        <div class="col-md-6 mb-4">
                            <div class="preview-message-card">
                                <div class="d-flex align-items-center mb-3">
                                    <img src="{{ $message->profile_image_url }}" alt="{{ $message->name }}"
                                         class="rounded-circle me-3" style="width: 60px; height: 60px; object-fit: cover;">
                                    <div>
                                        <h6 class="mb-1">{{ $message->name }}</h6>
                                        <small class="text-muted">{{ $message->position }}</small>
                                    </div>
                                </div>
                                <p class="mb-0">{{ $message->message_excerpt }}</p>
                            </div>
                        </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-5">
                        <i class="fas fa-eye-slash fa-3x text-muted mb-3"></i>
                        <h5>No Published Messages</h5>
                        <p class="text-muted">Publish some messages to see the preview</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Help Modal -->
<div class="modal fade" id="helpModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-question-circle me-2"></i>President Messages Help
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <h6>What are President Messages?</h6>
                <p>President messages are official communications from the association's leadership to members. They're perfect for:</p>
                <ul>
                    <li>Sharing important announcements and updates</li>
                    <li>Communicating vision and strategic direction</li>
                    <li>Addressing member concerns and feedback</li>
                    <li>Celebrating achievements and milestones</li>
                </ul>
                <h6>Best Practices:</h6>
                <ul>
                    <li>Keep messages clear and concise</li>
                    <li>Use professional profile photos</li>
                    <li>Include relevant dates and context</li>
                    <li>Maintain a consistent tone and style</li>
                </ul>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <a href="{{ route('admin.president-messages.create') }}" class="btn btn-primary">Create Message</a>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header border-0">
                <h5 class="modal-title text-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>Confirm Delete
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <div class="mb-3">
                        <i class="fas fa-trash-alt fa-3x text-danger"></i>
                    </div>
                    <h6>Are you sure you want to delete this president message?</h6>
                    <p class="text-muted">This action cannot be undone. The message will be permanently removed.</p>
                </div>
            </div>
            <div class="modal-footer border-0">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>Cancel
                </button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-2"></i>Delete Message
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
// View Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const cardViewBtn = document.getElementById('cardViewBtn');
    const tableViewBtn = document.getElementById('tableViewBtn');
    const cardView = document.getElementById('cardView');
    const tableView = document.getElementById('tableView');

    if (cardViewBtn && tableViewBtn) {
        cardViewBtn.addEventListener('click', function() {
            cardView.classList.remove('d-none');
            tableView.classList.add('d-none');
            cardViewBtn.classList.add('active');
            tableViewBtn.classList.remove('active');
        });

        tableViewBtn.addEventListener('click', function() {
            tableView.classList.remove('d-none');
            cardView.classList.add('d-none');
            tableViewBtn.classList.add('active');
            cardViewBtn.classList.remove('active');
        });
    }

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

function confirmDelete(id) {
    const form = document.getElementById('deleteForm');
    form.action = `/admin/president-messages/${id}`;
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}
</script>
@endpush
@endsection
