@extends('layouts.admin')

@section('title', 'News Management')

@section('content')
<div class="admin-wrapper">
    <div class="admin-main">
        <div class="admin-content">
            <!-- Enhanced Header Section -->
            <div class="d-flex justify-content-between align-items-center mb-5">
                <div>
                    <h1 class="admin-title mb-2 fw-bold text-dark">
                        <i class="fas fa-newspaper me-3 text-primary"></i>News Management
                    </h1>
                    <p class="text-muted mb-0 fs-6">Create, manage and publish news articles and updates</p>
                </div>

                <div class="d-flex gap-2">
                    
                   
                    <a href="{{ route('admin.news.create') }}" class="btn btn-primary btn-lg shadow-sm">
                        <i class="fas fa-plus me-2"></i>Add New Article
                    </a>
                </div>
            </div>

            <!-- Enhanced Alert Messages -->
            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show border-0 shadow-sm mb-4" role="alert">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-check-circle me-3 fs-5"></i>
                        <div>
                            <strong>Success!</strong> {{ session('success') }}
                        </div>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-danger alert-dismissible fade show border-0 shadow-sm mb-4" role="alert">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-exclamation-circle me-3 fs-5"></i>
                        <div>
                            <strong>Error!</strong> {{ session('error') }}
                        </div>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <!-- Enhanced Card Design -->
            <div class="admin-card shadow-sm border-0">
                <div class="d-flex justify-content-between align-items-center mb-4 pb-3 border-bottom">
                    <div>
                        <h4 class="admin-card-title mb-1 fw-semibold text-dark">
                            <i class="fas fa-list me-2 text-primary"></i>All News Articles
                        </h4>
                        <p class="text-muted mb-0 small">Manage your news articles and their publication status</p>
                    </div>
                </div>

                <!-- Enhanced Table with Modern Design -->
                <div class="admin-table-responsive rounded-3 overflow-hidden shadow-sm">
                    <table class="admin-table data-table news-table mb-0">
                        <thead class="bg-gradient-primary">
                            <tr>
                                <th class="no-export border-0 text-white">
                                    <div class="form-check">
                                        <input type="checkbox" id="selectAll" class="form-check-input border-dark">
                                        <label class="form-check-label visually-hidden" for="selectAll">Select All</label>
                                    </div>
                                </th>
                                <th class="border-0 text-dark fw-semibold">
                                    <i class="fas fa-image me-2"></i>Image
                                </th>
                                <th class="border-0 text-dark fw-semibold">
                                    <i class="fas fa-heading me-2"></i>Title & Details
                                </th>
                                <th class="border-0 text-dark fw-semibold">
                                    <i class="fas fa-tag me-2"></i>Category
                                </th>
                                <th class="border-0 text-dark fw-semibold">
                                    <i class="fas fa-circle-dot me-2"></i>Status
                                </th>
                                <th class="border-0 text-dark fw-semibold">
                                    <i class="fas fa-user me-2"></i>Author
                                </th>
                                <th class="border-0 text-dark fw-semibold">
                                    <i class="fas fa-calendar me-2"></i>Published
                                </th>
                                <th class="no-export border-0 text-dark fw-semibold text-center">
                                    <i class="fas fa-star me-2"></i>Featured
                                </th>
                                <th class="no-export border-0 text-dark fw-semibold text-center">
                                    <i class="fas fa-eye me-2"></i>Active
                                </th>
                                <th class="no-export border-0 text-dark fw-semibold text-center">
                                    <i class="fas fa-cogs me-2"></i>Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($news as $article)
                                <tr data-article-id="{{ $article->id }}" class="border-bottom">
                                    <td class="align-middle">
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input row-checkbox border-dark" value="{{ $article->id }}">
                                        </div>
                                    </td>
                                    <td class="align-middle">
                                        @if($article->image_path)
                                            <div class="position-relative">
                                                <img src="{{ asset('storage/' . $article->image_path) }}"
                                                     alt="{{ $article->title }}"
                                                     class="rounded-3 shadow-sm border"
                                                     style="width: 90px; height: 70px; object-fit: cover; cursor: pointer; transition: transform 0.2s ease;"
                                                     onclick="showImagePreview('{{ asset('storage/' . $article->image_path) }}', '{{ $article->title }}')"
                                                     onmouseover="this.style.transform='scale(1.05)'"
                                                     onmouseout="this.style.transform='scale(1)'">
                                                @if($article->is_featured)
                                                    <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-warning shadow-sm">
                                                        <i class="fas fa-star"></i>
                                                    </span>
                                                @endif
                                            </div>
                                        @else
                                            <div class="bg-light d-flex align-items-center justify-content-center border rounded-3 shadow-sm"
                                                 style="width: 90px; height: 70px;">
                                                <i class="fas fa-image text-muted fs-4"></i>
                                            </div>
                                        @endif
                                    </td>
                                    <td class="align-middle">
                                        <div class="d-flex flex-column">
                                            <h6 class="mb-1 fw-semibold text-dark">{{ Str::limit($article->title, 40) }}</h6>
                                            @if($article->excerpt)
                                                <p class="text-muted mb-2 small">{{ Str::limit($article->excerpt, 60) }}</p>
                                            @endif
                                            <div class="d-flex align-items-center text-info small">
                                                <i class="fas fa-calendar-alt me-2"></i>
                                                <span>Created {{ $article->created_at->format('M j, Y') }}</span>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="align-middle">
                                        <span class="badge bg-info-subtle text-info border border-info-subtle px-3 py-2 rounded-pill fw-semibold">
                                            <i class="fas fa-tag me-1"></i>{{ ucfirst($article->category) }}
                                        </span>
                                    </td>
                                    <td class="align-middle">
                                        <span class="badge {{ $article->status_badge_class }} px-3 py-2 rounded-pill fw-semibold shadow-sm">
                                            <i class="fas fa-circle me-1" style="font-size: 0.6rem;"></i>{{ ucfirst($article->status) }}
                                        </span>
                                    </td>
                                    <td class="align-middle">
                                        <div class="d-flex align-items-center">
                                            <div class="bg-gradient-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3 shadow-sm"
                                                 style="width: 40px; height: 40px; font-size: 14px; font-weight: 600;">
                                                {{ strtoupper(substr($article->author ?? 'Admin', 0, 2)) }}
                                            </div>
                                            <div>
                                                <span class="fw-semibold text-dark">{{ $article->author ?? 'Admin' }}</span>
                                                <small class="d-block text-muted">Author</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="align-middle">
                                        @if($article->published_at)
                                            <div class="d-flex flex-column">
                                                <span class="fw-semibold text-dark">{{ $article->published_at->format('M j, Y') }}</span>
                                                <small class="text-muted">{{ $article->published_at->format('g:i A') }}</small>
                                            </div>
                                        @else
                                            <div class="d-flex align-items-center text-muted">
                                                <i class="fas fa-clock me-2"></i>
                                                <span class="small">Not published</span>
                                            </div>
                                        @endif
                                    </td>
                                    <td class="align-middle text-center">
                                        <form action="{{ route('admin.news.toggle-featured', $article) }}" method="POST" class="d-inline">
                                            @csrf
                                            @method('PATCH')
                                            <button type="submit"
                                                    class="btn btn-sm {{ $article->is_featured ? 'btn-warning shadow-sm' : 'btn-outline-warning' }} rounded-pill"
                                                    data-bs-toggle="tooltip"
                                                    title="{{ $article->is_featured ? 'Remove from featured' : 'Mark as featured' }}">
                                                <i class="fas fa-star"></i>
                                            </button>
                                        </form>
                                    </td>
                                    <td class="align-middle text-center">
                                        <form action="{{ route('admin.news.toggle-active', $article) }}" method="POST" class="d-inline">
                                            @csrf
                                            @method('PATCH')
                                            <button type="submit"
                                                    class="btn btn-sm {{ $article->is_active ? 'btn-success shadow-sm' : 'btn-outline-success' }} rounded-pill"
                                                    data-bs-toggle="tooltip"
                                                    title="{{ $article->is_active ? 'Deactivate article' : 'Activate article' }}">
                                                <i class="fas fa-{{ $article->is_active ? 'eye' : 'eye-slash' }}"></i>
                                            </button>
                                        </form>
                                    </td>
                                    <td class="align-middle text-center">
                                        <div class="btn-group shadow-sm" role="group">
                                            <a href="{{ route('admin.news.show', $article) }}"
                                               class="btn btn-sm btn-outline-info rounded-start-pill"
                                               data-bs-toggle="tooltip" title="View Article">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.news.edit', $article) }}"
                                               class="btn btn-sm btn-outline-primary"
                                               data-bs-toggle="tooltip" title="Edit Article">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button"
                                                    class="btn btn-sm btn-outline-danger rounded-end-pill"
                                                    onclick="confirmDelete('{{ $article->id }}', '{{ $article->title }}')"
                                                    data-bs-toggle="tooltip" title="Delete Article">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="10" class="text-center py-5">
                                        <div class="d-flex flex-column align-items-center justify-content-center py-4">
                                            <div class="bg-light rounded-circle p-4 mb-4">
                                                <i class="fas fa-newspaper fa-3x text-muted"></i>
                                            </div>
                                            <h5 class="text-muted mb-2">No News Articles Found</h5>
                                            <p class="text-muted mb-4">Start creating engaging news articles for your audience</p>
                                            <a href="{{ route('admin.news.create') }}" class="btn btn-primary btn-lg shadow-sm">
                                                <i class="fas fa-plus me-2"></i>Create Your First Article
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow-lg">
            <div class="modal-header bg-danger text-white border-0">
                <h5 class="modal-title fw-bold" id="deleteModalLabel">
                    <i class="fas fa-exclamation-triangle me-2"></i>Confirm Deletion
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-4">
                <div class="d-flex align-items-center mb-3">
                    <div class="bg-danger-subtle rounded-circle p-3 me-3">
                        <i class="fas fa-trash text-danger fs-4"></i>
                    </div>
                    <div>
                        <h6 class="mb-1">Delete News Article</h6>
                        <p class="text-muted mb-0 small">This action cannot be undone</p>
                    </div>
                </div>
                <p class="mb-2">Are you sure you want to delete the article:</p>
                <p class="fw-bold text-dark">"<span id="deleteItemName"></span>"</p>
                <div class="alert alert-warning border-0 bg-warning-subtle">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <small>This will permanently remove the article and all associated data.</small>
                </div>
            </div>
            <div class="modal-footer border-0 bg-light">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>Cancel
                </button>
                <form id="deleteForm" method="POST" class="d-inline">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-2"></i>Delete Article
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(id, name) {
    document.getElementById('deleteItemName').textContent = name;
    document.getElementById('deleteForm').action = `/admin/news/${id}`;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

// Initialize tooltips
document.addEventListener('DOMContentLoaded', function() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
@endsection
