@extends('layouts.admin')

@section('title', 'View News Article')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2 class="admin-title mb-0">News Article Details</h2>
    <a href="{{ route('admin.news.index') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i>Back to List
    </a>
</div>

@if(session('success'))
    <div class="admin-alert admin-alert-success">
        <i class="fas fa-check-circle me-2"></i>
        {{ session('success') }}
    </div>
@endif

<div class="row">
    <div class="col-lg-8">
        <div class="admin-card">
            <div class="d-flex justify-content-between align-items-start mb-4">
                <div>
                    <h5 class="admin-card-title mb-2">{{ $news->title }}</h5>
                    <p class="text-muted mb-0">{{ $news->slug }}</p>
                </div>
                <div class="d-flex gap-2">
                    @if($news->is_featured)
                        <span class="badge bg-warning">Featured</span>
                    @endif
                    @if($news->is_active)
                        <span class="badge bg-success">Active</span>
                    @else
                        <span class="badge bg-secondary">Inactive</span>
                    @endif
                    <span class="badge bg-{{ $news->status === 'published' ? 'success' : ($news->status === 'draft' ? 'warning' : 'secondary') }}">
                        {{ ucfirst($news->status) }}
                    </span>
                </div>
            </div>
            
            @if($news->image_path)
                <div class="mb-4">
                    <img src="{{ $news->image_url }}" alt="{{ $news->title }}" 
                         class="img-fluid rounded shadow-sm" style="max-width: 100%; height: auto;">
                </div>
            @endif
            
            @if($news->excerpt)
                <div class="mb-4">
                    <h6 class="text-muted">Excerpt</h6>
                    <p class="text-muted">{{ $news->excerpt }}</p>
                </div>
            @endif
            
            <div class="mb-4">
                <h6 class="text-muted">Content</h6>
                <div class="content-display">
                    {!! $news->content !!}
                </div>
            </div>
            
            <div class="row mb-4">
                <div class="col-md-6">
                    <h6 class="text-muted">Category</h6>
                    <p class="mb-0">
                        <span class="badge bg-primary">{{ ucfirst($news->category) }}</span>
                    </p>
                </div>
                <div class="col-md-6">
                    <h6 class="text-muted">Author</h6>
                    <p class="mb-0">{{ $news->author ?? 'Admin' }}</p>
                </div>
            </div>
            
            @if($news->published_at)
                <div class="mb-4">
                    <h6 class="text-muted">Published Date</h6>
                    <p class="mb-0">{{ $news->published_at->format('M d, Y g:i A') }}</p>
                </div>
            @endif
            
            <div class="d-flex gap-2">
                <a href="{{ route('admin.news.edit', $news) }}" class="btn btn-warning">
                    <i class="fas fa-edit me-2"></i>Edit
                </a>
                
                <form action="{{ route('admin.news.toggle-featured', $news) }}" method="POST" class="d-inline">
                    @csrf
                    @method('PATCH')
                    <button type="submit" class="btn {{ $news->is_featured ? 'btn-outline-warning' : 'btn-warning' }}">
                        <i class="fas fa-star me-2"></i>
                        {{ $news->is_featured ? 'Remove Featured' : 'Make Featured' }}
                    </button>
                </form>
                
                <form action="{{ route('admin.news.toggle-active', $news) }}" method="POST" class="d-inline">
                    @csrf
                    @method('PATCH')
                    <button type="submit" class="btn {{ $news->is_active ? 'btn-secondary' : 'btn-success' }}">
                        <i class="fas fa-{{ $news->is_active ? 'times' : 'check' }} me-2"></i>
                        {{ $news->is_active ? 'Deactivate' : 'Activate' }}
                    </button>
                </form>
                
                <button type="button" class="btn btn-danger" onclick="confirmDelete({{ $news->id }})">
                    <i class="fas fa-trash me-2"></i>Delete
                </button>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="admin-card">
            <h6 class="admin-card-title">Article Information</h6>
            
            <div class="mb-3">
                <small class="text-muted">Sort Order</small>
                <p class="mb-0">{{ $news->sort_order }}</p>
            </div>
            
            <div class="mb-3">
                <small class="text-muted">Created</small>
                <p class="mb-0">{{ $news->created_at->format('M d, Y g:i A') }}</p>
            </div>
            
            <div class="mb-3">
                <small class="text-muted">Last Updated</small>
                <p class="mb-0">{{ $news->updated_at->format('M d, Y g:i A') }}</p>
            </div>
            
            @if($news->image_path)
                <div class="mb-3">
                    <small class="text-muted">Image File</small>
                    <p class="mb-0 text-break">{{ basename($news->image_path) }}</p>
                </div>
            @endif
        </div>
        
        @if($news->meta_title || $news->meta_description)
        <div class="admin-card">
            <h6 class="admin-card-title">SEO Information</h6>
            
            @if($news->meta_title)
                <div class="mb-3">
                    <small class="text-muted">Meta Title</small>
                    <p class="mb-0">{{ $news->meta_title }}</p>
                </div>
            @endif
            
            @if($news->meta_description)
                <div class="mb-3">
                    <small class="text-muted">Meta Description</small>
                    <p class="mb-0">{{ $news->meta_description }}</p>
                </div>
            @endif
        </div>
        @endif
        
        <div class="admin-card">
            <h6 class="admin-card-title">Quick Actions</h6>
            
            <div class="d-grid gap-2">
                @if($news->status === 'published')
                    <a href="{{ route('news-detail', $news->slug) }}" target="_blank" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-external-link-alt me-2"></i>View on Website
                    </a>
                @endif
                
                <a href="{{ route('admin.news.create') }}" class="btn btn-outline-success btn-sm">
                    <i class="fas fa-plus me-2"></i>Create New Article
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this news article? This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" class="d-inline">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(newsId) {
    const deleteForm = document.getElementById('deleteForm');
    deleteForm.action = `/admin/news/${newsId}`;
    
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}
</script>

<style>
.content-display {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    min-height: 200px;
}

.content-display img {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
    margin: 10px 0;
}

.content-display h1, .content-display h2, .content-display h3, 
.content-display h4, .content-display h5, .content-display h6 {
    margin-top: 20px;
    margin-bottom: 10px;
}

.content-display p {
    margin-bottom: 15px;
    line-height: 1.6;
}

.content-display ul, .content-display ol {
    margin-bottom: 15px;
    padding-left: 30px;
}

.content-display blockquote {
    border-left: 4px solid #007bff;
    padding-left: 15px;
    margin: 20px 0;
    font-style: italic;
    color: #6c757d;
}
</style>
@endsection
