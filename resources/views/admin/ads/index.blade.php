@extends('layouts.admin')

@section('title', 'Ads Management')

@section('content')
<div class="admin-wrapper">
    <div class="admin-main">
        <div class="admin-content">
            <!-- Enhanced Header Section -->
            <div class="d-flex justify-content-between align-items-center mb-5">
                <div>
                    <h1 class="admin-title mb-2 fw-bold text-dark">
                        <i class="fas fa-ad me-3 text-primary"></i>Ads Management
                    </h1>
                    <p class="text-muted mb-0 fs-6">Manage website advertisements and promotional content</p>
                </div>

                <div class="d-flex gap-2">
                    <a href="{{ route('admin.ads.create') }}" class="btn btn-primary btn-lg shadow-sm">
                        <i class="fas fa-plus me-2"></i>Add New Ad
                    </a>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4 d-none">
                <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                    <div class="admin-stat-card bg-primary">
                        <div class="admin-stat-icon">
                            <i class="fas fa-ad"></i>
                        </div>
                        <div class="admin-stat-content">
                            <h3>{{ $stats['total'] }}</h3>
                            <p>Total Ads</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                    <div class="admin-stat-card bg-success">
                        <div class="admin-stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="admin-stat-content">
                            <h3>{{ $stats['active'] }}</h3>
                            <p>Active</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                    <div class="admin-stat-card bg-warning">
                        <div class="admin-stat-icon">
                            <i class="fas fa-pause-circle"></i>
                        </div>
                        <div class="admin-stat-content">
                            <h3>{{ $stats['inactive'] }}</h3>
                            <p>Inactive</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                    <div class="admin-stat-card bg-info">
                        <div class="admin-stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="admin-stat-content">
                            <h3>{{ $stats['scheduled'] }}</h3>
                            <p>Scheduled</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                    <div class="admin-stat-card bg-secondary">
                        <div class="admin-stat-icon">
                            <i class="fas fa-image"></i>
                        </div>
                        <div class="admin-stat-content">
                            <h3>{{ $stats['image_ads'] }}</h3>
                            <p>Image Ads</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                    <div class="admin-stat-card bg-dark">
                        <div class="admin-stat-icon">
                            <i class="fas fa-video"></i>
                        </div>
                        <div class="admin-stat-content">
                            <h3>{{ $stats['video_ads'] }}</h3>
                            <p>Video Ads</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="ads-filter-card">
                <h5 class="ads-filter-title">
                    <i class="fas fa-filter me-2"></i>Filter Ads
                </h5>
                <form method="GET" action="{{ route('admin.ads.index') }}" class="ads-filter-form row g-3">
                    <div class="col-md-3">
                        <label for="search" class="form-label">Search</label>
                        <input type="text" class="form-control" id="search" name="search"
                               value="{{ request('search') }}" placeholder="Search ads...">
                    </div>
                    <div class="col-md-2">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">All Status</option>
                            <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>Active</option>
                            <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                            <option value="scheduled" {{ request('status') == 'scheduled' ? 'selected' : '' }}>Scheduled</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="position" class="form-label">Position</label>
                        <select class="form-select" id="position" name="position">
                            <option value="">All Positions</option>
                            <option value="header" {{ request('position') == 'header' ? 'selected' : '' }}>Header</option>
                            <option value="sidebar" {{ request('position') == 'sidebar' ? 'selected' : '' }}>Sidebar</option>
                            <option value="footer" {{ request('position') == 'footer' ? 'selected' : '' }}>Footer</option>
                            <option value="content" {{ request('position') == 'content' ? 'selected' : '' }}>Content</option>
                            <option value="popup" {{ request('position') == 'popup' ? 'selected' : '' }}>Popup</option>
                            <option value="banner" {{ request('position') == 'banner' ? 'selected' : '' }}>Banner</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="type" class="form-label">Type</label>
                        <select class="form-select" id="type" name="type">
                            <option value="">All Types</option>
                            <option value="image" {{ request('type') == 'image' ? 'selected' : '' }}>Image</option>
                            <option value="video" {{ request('type') == 'video' ? 'selected' : '' }}>Video</option>
                        </select>
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-search me-1"></i>Filter
                        </button>
                        <a href="{{ route('admin.ads.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>Clear
                        </a>
                    </div>
                </form>
            </div>

            <!-- Ads Table -->
            <div class="admin-card">
                <div class="admin-card-header d-flex justify-content-between align-items-center">
                    <h5 class="admin-card-title mb-0">
                        <i class="fas fa-list me-2"></i>All Ads ({{ $ads->total() }})
                    </h5>
                    @if($ads->count() > 0)
                    <div class="dropdown">
                        <button class="btn btn-bulk btn-sm dropdown-toggle" type="button"
                                data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-cog me-1"></i>Bulk Actions
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="bulkAction('activate')">
                                <i class="fas fa-check me-2"></i>Activate Selected
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="bulkAction('deactivate')">
                                <i class="fas fa-pause me-2"></i>Deactivate Selected
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="#" onclick="bulkAction('delete')">
                                <i class="fas fa-trash me-2"></i>Delete Selected
                            </a></li>
                        </ul>
                    </div>
                    @endif
                </div>
                
                <div class="admin-card-body">
                    @if($ads->count() > 0)
                    <form id="bulk-action-form" method="POST" action="{{ route('admin.ads.bulk-action') }}">
                        @csrf
                        <input type="hidden" name="action" id="bulk-action-input">
                        
                        <div class="table-responsive">
                            <table class="table table-hover admin-table">
                                <thead>
                                    <tr>
                                        <th width="50">
                                            <input type="checkbox" id="select-all" class="form-check-input">
                                        </th>
                                        <th width="80">Preview</th>
                                        <th>Title</th>
                                        <th>Type</th>
                                        <th>Position</th>
                                        <th>Status</th>
                                        <th>Stats</th>
                                        <th>Order</th>
                                        <th>Created</th>
                                        <th width="120">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($ads as $ad)
                                    <tr>
                                        <td>
                                            <input type="checkbox" name="selected_ads[]" value="{{ $ad->id }}" 
                                                   class="form-check-input ad-checkbox">
                                        </td>
                                        <td>
                                            @if($ad->type === 'image')
                                                <img src="{{ asset('storage/' . $ad->content_path) }}" 
                                                     alt="{{ $ad->alt_text }}" 
                                                     class="img-thumbnail" 
                                                     style="width: 60px; height: 40px; object-fit: cover;">
                                            @else
                                                <div class="d-flex align-items-center justify-content-center bg-dark text-white rounded" 
                                                     style="width: 60px; height: 40px;">
                                                    <i class="fas fa-play"></i>
                                                </div>
                                            @endif
                                        </td>
                                        <td>
                                            <div>
                                                <strong>{{ $ad->title }}</strong>
                                                @if($ad->description)
                                                <br><small class="text-muted">{{ Str::limit($ad->description, 50) }}</small>
                                                @endif
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-{{ $ad->type === 'image' ? 'info' : 'warning' }}">
                                                <i class="fas fa-{{ $ad->type === 'image' ? 'image' : 'video' }} me-1"></i>
                                                {{ ucfirst($ad->type) }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">{{ ucfirst($ad->position) }}</span>
                                        </td>
                                        <td>
                                            @php
                                                $statusColors = [
                                                    'active' => 'success',
                                                    'inactive' => 'warning',
                                                    'scheduled' => 'info'
                                                ];
                                            @endphp
                                            <span class="badge bg-{{ $statusColors[$ad->status] ?? 'secondary' }}">
                                                {{ ucfirst($ad->status) }}
                                            </span>
                                        </td>
                                        <td>
                                            <small class="text-muted">
                                                <i class="fas fa-eye me-1"></i>{{ number_format($ad->impressions_count) }}<br>
                                                <i class="fas fa-mouse-pointer me-1"></i>{{ number_format($ad->clicks_count) }}
                                            </small>
                                        </td>
                                        <td>{{ $ad->sort_order }}</td>
                                        <td>
                                            <small class="text-muted">{{ $ad->created_at->format('M d, Y') }}</small>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('admin.ads.show', $ad) }}"
                                                   class="btn btn-action btn-view" title="View">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{{ route('admin.ads.edit', $ad) }}"
                                                   class="btn btn-action btn-edit" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button type="button" class="btn btn-action btn-delete"
                                                        onclick="deleteAd({{ $ad->id }})" title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </form>
                    
                    <!-- Pagination -->
                    <div class="d-flex justify-content-between align-items-center mt-4">
                        <div class="text-muted">
                            Showing {{ $ads->firstItem() }} to {{ $ads->lastItem() }} of {{ $ads->total() }} results
                        </div>
                        {{ $ads->links() }}
                    </div>
                    @else
                    <div class="text-center py-5">
                        <i class="fas fa-ad fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No ads found</h5>
                        <p class="text-muted">Start by creating your first advertisement.</p>
                        <a href="{{ route('admin.ads.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Create First Ad
                        </a>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this ad? This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="delete-form" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Select all functionality
document.getElementById('select-all').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.ad-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
});

// Delete ad function
function deleteAd(adId) {
    const deleteForm = document.getElementById('delete-form');
    deleteForm.action = `/admin/ads/${adId}`;

    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}

// Bulk actions
function bulkAction(action) {
    const selectedAds = document.querySelectorAll('.ad-checkbox:checked');

    if (selectedAds.length === 0) {
        alert('Please select at least one ad.');
        return;
    }

    if (action === 'delete' && !confirm('Are you sure you want to delete the selected ads?')) {
        return;
    }

    document.getElementById('bulk-action-input').value = action;
    document.getElementById('bulk-action-form').submit();
}
</script>
@endpush


