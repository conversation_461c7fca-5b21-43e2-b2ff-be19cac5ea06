@extends('layouts.admin')

@section('title', 'View Ad - ' . $ad->title)

@section('content')
<div class="admin-wrapper">
    <div class="admin-main">
        <div class="admin-content">
            <!-- Header Section -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="admin-title mb-2 fw-bold text-dark">
                        <i class="fas fa-eye me-3 text-primary"></i>{{ $ad->title }}
                    </h1>
                    <p class="text-muted mb-0 fs-6">
                        <span class="badge bg-{{ $ad->type === 'image' ? 'info' : 'warning' }} me-2">
                            <i class="fas fa-{{ $ad->type === 'image' ? 'image' : 'video' }} me-1"></i>
                            {{ ucfirst($ad->type) }} Ad
                        </span>
                        <span class="badge bg-secondary me-2">{{ ucfirst($ad->position) }}</span>
                        @php
                            $statusColors = [
                                'active' => 'success',
                                'inactive' => 'warning',
                                'scheduled' => 'info'
                            ];
                        @endphp
                        <span class="badge bg-{{ $statusColors[$ad->status] ?? 'secondary' }}">
                            {{ ucfirst($ad->status) }}
                        </span>
                    </p>
                </div>
                <div class="d-flex gap-2">
                    <a href="{{ route('admin.ads.edit', $ad) }}" class="btn btn-primary">
                        <i class="fas fa-edit me-2"></i>Edit Ad
                    </a>
                    <button type="button" class="btn btn-outline-danger" onclick="deleteAd({{ $ad->id }})">
                        <i class="fas fa-trash me-2"></i>Delete
                    </button>
                    <a href="{{ route('admin.ads.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Ads
                    </a>
                </div>
            </div>

            <div class="row">
                <!-- Main Content -->
                <div class="col-lg-8">
                    <!-- Ad Preview -->
                    <div class="admin-card mb-4">
                        <div class="admin-card-header">
                            <h5 class="admin-card-title mb-0">
                                <i class="fas fa-desktop me-2"></i>Ad Preview
                            </h5>
                        </div>
                        <div class="admin-card-body">
                            <div class="text-center">
                                @if($ad->type === 'image')
                                    <div class="position-relative d-inline-block">
                                        <img src="{{ asset('storage/' . $ad->content_path) }}" 
                                             alt="{{ $ad->alt_text }}" 
                                             class="img-fluid border rounded shadow-sm"
                                             @if($ad->width && $ad->height)
                                             style="width: {{ $ad->width }}px; height: {{ $ad->height }}px; object-fit: cover;"
                                             @else
                                             style="max-width: 100%; max-height: 400px;"
                                             @endif>
                                        @if($ad->link_url)
                                        <div class="position-absolute top-0 end-0 m-2">
                                            <span class="badge bg-primary">
                                                <i class="fas fa-external-link-alt"></i>
                                            </span>
                                        </div>
                                        @endif
                                    </div>
                                @else
                                    <div class="position-relative d-inline-block">
                                        <video controls class="border rounded shadow-sm"
                                               @if($ad->width && $ad->height)
                                               style="width: {{ $ad->width }}px; height: {{ $ad->height }}px;"
                                               @else
                                               style="max-width: 100%; max-height: 400px;"
                                               @endif
                                               @if($ad->thumbnail_path)
                                               poster="{{ asset('storage/' . $ad->thumbnail_path) }}"
                                               @endif>
                                            <source src="{{ asset('storage/' . $ad->content_path) }}" type="video/mp4">
                                            Your browser does not support the video tag.
                                        </video>
                                        @if($ad->link_url)
                                        <div class="position-absolute top-0 end-0 m-2">
                                            <span class="badge bg-primary">
                                                <i class="fas fa-external-link-alt"></i>
                                            </span>
                                        </div>
                                        @endif
                                    </div>
                                @endif
                            </div>
                            
                            @if($ad->link_url)
                            <div class="text-center mt-3">
                                <a href="{{ $ad->link_url }}" 
                                   class="btn btn-outline-primary"
                                   {{ $ad->open_in_new_tab ? 'target="_blank"' : '' }}>
                                    <i class="fas fa-external-link-alt me-2"></i>Visit Link
                                </a>
                            </div>
                            @endif
                        </div>
                    </div>

                    <!-- Ad Details -->
                    <div class="admin-card mb-4">
                        <div class="admin-card-header">
                            <h5 class="admin-card-title mb-0">
                                <i class="fas fa-info-circle me-2"></i>Ad Details
                            </h5>
                        </div>
                        <div class="admin-card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td class="fw-bold text-muted">Title:</td>
                                            <td>{{ $ad->title }}</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold text-muted">Type:</td>
                                            <td>
                                                <span class="badge bg-{{ $ad->type === 'image' ? 'info' : 'warning' }}">
                                                    <i class="fas fa-{{ $ad->type === 'image' ? 'image' : 'video' }} me-1"></i>
                                                    {{ ucfirst($ad->type) }}
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold text-muted">Position:</td>
                                            <td><span class="badge bg-secondary">{{ ucfirst($ad->position) }}</span></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold text-muted">Status:</td>
                                            <td>
                                                <span class="badge bg-{{ $statusColors[$ad->status] ?? 'secondary' }}">
                                                    {{ ucfirst($ad->status) }}
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold text-muted">Sort Order:</td>
                                            <td>{{ $ad->sort_order }}</td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        @if($ad->width || $ad->height)
                                        <tr>
                                            <td class="fw-bold text-muted">Dimensions:</td>
                                            <td>{{ $ad->width ?? 'Auto' }} × {{ $ad->height ?? 'Auto' }} px</td>
                                        </tr>
                                        @endif
                                        @if($ad->link_url)
                                        <tr>
                                            <td class="fw-bold text-muted">Link URL:</td>
                                            <td>
                                                <a href="{{ $ad->link_url }}" target="_blank" class="text-decoration-none">
                                                    {{ Str::limit($ad->link_url, 30) }}
                                                    <i class="fas fa-external-link-alt ms-1"></i>
                                                </a>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold text-muted">Open in:</td>
                                            <td>{{ $ad->open_in_new_tab ? 'New Tab' : 'Same Tab' }}</td>
                                        </tr>
                                        @endif
                                        @if($ad->alt_text)
                                        <tr>
                                            <td class="fw-bold text-muted">Alt Text:</td>
                                            <td>{{ $ad->alt_text }}</td>
                                        </tr>
                                        @endif
                                        <tr>
                                            <td class="fw-bold text-muted">Created:</td>
                                            <td>{{ $ad->created_at->format('M d, Y H:i') }}</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold text-muted">Updated:</td>
                                            <td>{{ $ad->updated_at->format('M d, Y H:i') }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                            
                            @if($ad->description)
                            <div class="mt-3">
                                <h6 class="fw-bold text-muted">Description:</h6>
                                <p class="mb-0">{{ $ad->description }}</p>
                            </div>
                            @endif
                        </div>
                    </div>

                    <!-- Scheduling Information -->
                    @if($ad->status === 'scheduled' || $ad->start_date || $ad->end_date || $ad->max_impressions)
                    <div class="admin-card mb-4">
                        <div class="admin-card-header">
                            <h5 class="admin-card-title mb-0">
                                <i class="fas fa-calendar-alt me-2"></i>Scheduling & Limits
                            </h5>
                        </div>
                        <div class="admin-card-body">
                            <div class="row">
                                @if($ad->start_date)
                                <div class="col-md-6 mb-3">
                                    <h6 class="fw-bold text-muted">Start Date:</h6>
                                    <p class="mb-0">{{ $ad->start_date->format('M d, Y H:i') }}</p>
                                </div>
                                @endif
                                @if($ad->end_date)
                                <div class="col-md-6 mb-3">
                                    <h6 class="fw-bold text-muted">End Date:</h6>
                                    <p class="mb-0">{{ $ad->end_date->format('M d, Y H:i') }}</p>
                                </div>
                                @endif
                                @if($ad->max_impressions)
                                <div class="col-md-6 mb-3">
                                    <h6 class="fw-bold text-muted">Max Impressions:</h6>
                                    <p class="mb-0">{{ number_format($ad->max_impressions) }}</p>
                                </div>
                                @endif
                            </div>
                        </div>
                    </div>
                    @endif

                    <!-- Target Pages -->
                    @if($ad->target_pages && count($ad->target_pages) > 0)
                    <div class="admin-card mb-4">
                        <div class="admin-card-header">
                            <h5 class="admin-card-title mb-0">
                                <i class="fas fa-bullseye me-2"></i>Target Pages
                            </h5>
                        </div>
                        <div class="admin-card-body">
                            <div class="d-flex flex-wrap gap-2">
                                @foreach($ad->target_pages as $page)
                                <span class="badge bg-light text-dark border">{{ ucfirst($page) }}</span>
                                @endforeach
                            </div>
                        </div>
                    </div>
                    @endif
                </div>

                <!-- Sidebar -->
                <div class="col-lg-4">
                    <!-- Statistics -->
                    <div class="admin-card mb-4">
                        <div class="admin-card-header">
                            <h5 class="admin-card-title mb-0">
                                <i class="fas fa-chart-bar me-2"></i>Performance Statistics
                            </h5>
                        </div>
                        <div class="admin-card-body">
                            <div class="row text-center mb-3">
                                <div class="col-6">
                                    <div class="border-end">
                                        <h3 class="text-primary mb-1">{{ number_format($ad->impressions_count) }}</h3>
                                        <small class="text-muted">Impressions</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <h3 class="text-success mb-1">{{ number_format($ad->clicks_count) }}</h3>
                                    <small class="text-muted">Clicks</small>
                                </div>
                            </div>
                            
                            @if($ad->impressions_count > 0)
                            <div class="text-center mb-3">
                                <h4 class="text-info mb-1">{{ number_format(($ad->clicks_count / $ad->impressions_count) * 100, 2) }}%</h4>
                                <small class="text-muted">Click-Through Rate</small>
                            </div>
                            @endif

                            @if($ad->max_impressions)
                            <div class="mb-3">
                                <div class="d-flex justify-content-between mb-1">
                                    <small class="text-muted">Impression Progress</small>
                                    <small class="text-muted">{{ number_format($ad->impressions_count) }}/{{ number_format($ad->max_impressions) }}</small>
                                </div>
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar" role="progressbar" 
                                         style="width: {{ min(($ad->impressions_count / $ad->max_impressions) * 100, 100) }}%">
                                    </div>
                                </div>
                            </div>
                            @endif
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="admin-card mb-4">
                        <div class="admin-card-header">
                            <h5 class="admin-card-title mb-0">
                                <i class="fas fa-bolt me-2"></i>Quick Actions
                            </h5>
                        </div>
                        <div class="admin-card-body">
                            <div class="d-grid gap-2">
                                @if($ad->status === 'active')
                                <form action="{{ route('admin.ads.bulk-action') }}" method="POST" style="display: inline;">
                                    @csrf
                                    <input type="hidden" name="action" value="deactivate">
                                    <input type="hidden" name="selected_ads[]" value="{{ $ad->id }}">
                                    <button type="submit" class="btn btn-warning btn-sm w-100">
                                        <i class="fas fa-pause me-2"></i>Deactivate
                                    </button>
                                </form>
                                @else
                                <form action="{{ route('admin.ads.bulk-action') }}" method="POST" style="display: inline;">
                                    @csrf
                                    <input type="hidden" name="action" value="activate">
                                    <input type="hidden" name="selected_ads[]" value="{{ $ad->id }}">
                                    <button type="submit" class="btn btn-success btn-sm w-100">
                                        <i class="fas fa-play me-2"></i>Activate
                                    </button>
                                </form>
                                @endif
                                
                                <a href="{{ route('admin.ads.edit', $ad) }}" class="btn btn-primary btn-sm">
                                    <i class="fas fa-edit me-2"></i>Edit Ad
                                </a>
                                
                                <button type="button" class="btn btn-outline-danger btn-sm" onclick="deleteAd({{ $ad->id }})">
                                    <i class="fas fa-trash me-2"></i>Delete Ad
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- File Information -->
                    <div class="admin-card">
                        <div class="admin-card-header">
                            <h5 class="admin-card-title mb-0">
                                <i class="fas fa-file me-2"></i>File Information
                            </h5>
                        </div>
                        <div class="admin-card-body">
                            <table class="table table-borderless table-sm">
                                <tr>
                                    <td class="fw-bold text-muted">Content File:</td>
                                    <td>
                                        <a href="{{ asset('storage/' . $ad->content_path) }}" target="_blank" class="text-decoration-none">
                                            {{ basename($ad->content_path) }}
                                            <i class="fas fa-external-link-alt ms-1"></i>
                                        </a>
                                    </td>
                                </tr>
                                @if($ad->thumbnail_path)
                                <tr>
                                    <td class="fw-bold text-muted">Thumbnail:</td>
                                    <td>
                                        <a href="{{ asset('storage/' . $ad->thumbnail_path) }}" target="_blank" class="text-decoration-none">
                                            {{ basename($ad->thumbnail_path) }}
                                            <i class="fas fa-external-link-alt ms-1"></i>
                                        </a>
                                    </td>
                                </tr>
                                @endif
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this ad? This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="delete-form" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Delete ad function
function deleteAd(adId) {
    const deleteForm = document.getElementById('delete-form');
    deleteForm.action = `/admin/ads/${adId}`;
    
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}
</script>
@endpush
