@extends('layouts.admin')

@section('title', 'Edit Ad')

@section('content')
<div class="admin-wrapper">
    <div class="admin-main">
        <div class="admin-content">
            <!-- Header Section -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="admin-title mb-2 fw-bold text-dark">
                        <i class="fas fa-edit me-3 text-primary"></i>Edit Ad
                    </h1>
                    <p class="text-muted mb-0 fs-6">Update advertisement details</p>
                </div>
                <div class="d-flex gap-2">
                    <a href="{{ route('admin.ads.show', $ad) }}" class="btn btn-outline-info">
                        <i class="fas fa-eye me-2"></i>View Ad
                    </a>
                    <a href="{{ route('admin.ads.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Ads
                    </a>
                </div>
            </div>

            <form action="{{ route('admin.ads.update', $ad) }}" method="POST" enctype="multipart/form-data" id="ad-form">
                @csrf
                @method('PUT')
                
                <div class="row">
                    <!-- Main Content -->
                    <div class="col-lg-8">
                        <div class="admin-card mb-4">
                            <div class="admin-card-header">
                                <h5 class="admin-card-title mb-0">
                                    <i class="fas fa-info-circle me-2"></i>Basic Information
                                </h5>
                            </div>
                            <div class="admin-card-body">
                                <div class="row">
                                    <div class="col-md-8 mb-3">
                                        <label for="title" class="form-label">Ad Title <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                               id="title" name="title" value="{{ old('title', $ad->title) }}" required>
                                        @error('title')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="type" class="form-label">Ad Type <span class="text-danger">*</span></label>
                                        <select class="form-select @error('type') is-invalid @enderror" 
                                                id="type" name="type" required onchange="toggleTypeFields()">
                                            <option value="">Select Type</option>
                                            <option value="image" {{ old('type', $ad->type) == 'image' ? 'selected' : '' }}>Image Ad</option>
                                            <option value="video" {{ old('type', $ad->type) == 'video' ? 'selected' : '' }}>Video Ad</option>
                                        </select>
                                        @error('type')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="description" class="form-label">Description</label>
                                    <textarea class="form-control @error('description') is-invalid @enderror" 
                                              id="description" name="description" rows="3">{{ old('description', $ad->description) }}</textarea>
                                    @error('description')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="link_url" class="form-label">Link URL</label>
                                    <input type="url" class="form-control @error('link_url') is-invalid @enderror" 
                                           id="link_url" name="link_url" value="{{ old('link_url', $ad->link_url) }}" 
                                           placeholder="https://example.com">
                                    @error('link_url')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">URL to redirect when the ad is clicked</div>
                                </div>

                                <div class="mb-3">
                                    <label for="alt_text" class="form-label">Alt Text</label>
                                    <input type="text" class="form-control @error('alt_text') is-invalid @enderror" 
                                           id="alt_text" name="alt_text" value="{{ old('alt_text', $ad->alt_text) }}" 
                                           placeholder="Descriptive text for accessibility">
                                    @error('alt_text')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Current Media -->
                        <div class="admin-card mb-4">
                            <div class="admin-card-header">
                                <h5 class="admin-card-title mb-0">
                                    <i class="fas fa-image me-2"></i>Current Media
                                </h5>
                            </div>
                            <div class="admin-card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <label class="form-label">Current {{ ucfirst($ad->type) }}</label>
                                        <div class="border rounded p-3 text-center bg-light">
                                            @if($ad->type === 'image')
                                                <img src="{{ asset('storage/' . $ad->content_path) }}" 
                                                     alt="{{ $ad->alt_text }}" 
                                                     class="img-fluid" 
                                                     style="max-height: 200px;">
                                            @else
                                                <video controls class="img-fluid" style="max-height: 200px;">
                                                    <source src="{{ asset('storage/' . $ad->content_path) }}" type="video/mp4">
                                                    Your browser does not support the video tag.
                                                </video>
                                            @endif
                                        </div>
                                    </div>
                                    @if($ad->type === 'video' && $ad->thumbnail_path)
                                    <div class="col-md-6">
                                        <label class="form-label">Current Thumbnail</label>
                                        <div class="border rounded p-3 text-center bg-light">
                                            <img src="{{ asset('storage/' . $ad->thumbnail_path) }}" 
                                                 alt="Thumbnail" 
                                                 class="img-fluid" 
                                                 style="max-height: 200px;">
                                        </div>
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <!-- Media Upload -->
                        <div class="admin-card mb-4">
                            <div class="admin-card-header">
                                <h5 class="admin-card-title mb-0">
                                    <i class="fas fa-upload me-2"></i>Update Media (Optional)
                                </h5>
                            </div>
                            <div class="admin-card-body">
                                <div class="mb-3">
                                    <label for="content_file" class="form-label">
                                        <span id="content-label">New Content File</span>
                                    </label>
                                    <input type="file" class="form-control @error('content_file') is-invalid @enderror" 
                                           id="content_file" name="content_file" 
                                           accept="image/*,video/*" onchange="previewFile()">
                                    @error('content_file')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text" id="content-help">
                                        Leave empty to keep current file. Supported formats: JPG, PNG, GIF, MP4, AVI, MOV, WMV. Max size: 20MB
                                    </div>
                                </div>

                                <!-- Video Thumbnail Upload -->
                                <div class="mb-3" id="thumbnail-section" style="display: none;">
                                    <label for="thumbnail_file" class="form-label">New Video Thumbnail</label>
                                    <input type="file" class="form-control @error('thumbnail_file') is-invalid @enderror" 
                                           id="thumbnail_file" name="thumbnail_file" 
                                           accept="image/*" onchange="previewThumbnail()">
                                    @error('thumbnail_file')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">Leave empty to keep current thumbnail. Max size: 2MB</div>
                                </div>

                                <!-- Preview Area -->
                                <div id="preview-area" class="mt-3" style="display: none;">
                                    <label class="form-label">New Media Preview</label>
                                    <div class="border rounded p-3 text-center bg-light">
                                        <div id="content-preview"></div>
                                        <div id="thumbnail-preview" style="display: none;"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Dimensions -->
                        <div class="admin-card mb-4">
                            <div class="admin-card-header">
                                <h5 class="admin-card-title mb-0">
                                    <i class="fas fa-expand-arrows-alt me-2"></i>Dimensions
                                </h5>
                            </div>
                            <div class="admin-card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="width" class="form-label">Width (px)</label>
                                        <input type="number" class="form-control @error('width') is-invalid @enderror" 
                                               id="width" name="width" value="{{ old('width', $ad->width) }}" min="1">
                                        @error('width')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="height" class="form-label">Height (px)</label>
                                        <input type="number" class="form-control @error('height') is-invalid @enderror" 
                                               id="height" name="height" value="{{ old('height', $ad->height) }}" min="1">
                                        @error('height')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="form-text">Leave empty to use original dimensions</div>
                            </div>
                        </div>
                    </div>

                    <!-- Sidebar -->
                    <div class="col-lg-4">
                        <!-- Placement Settings -->
                        <div class="admin-card mb-4">
                            <div class="admin-card-header">
                                <h5 class="admin-card-title mb-0">
                                    <i class="fas fa-map-marker-alt me-2"></i>Placement
                                </h5>
                            </div>
                            <div class="admin-card-body">
                                <div class="mb-3">
                                    <label for="position" class="form-label">Position <span class="text-danger">*</span></label>
                                    <select class="form-select @error('position') is-invalid @enderror" 
                                            id="position" name="position" required>
                                        <option value="">Select Position</option>
                                        <option value="header" {{ old('position', $ad->position) == 'header' ? 'selected' : '' }}>Header</option>
                                        <option value="sidebar" {{ old('position', $ad->position) == 'sidebar' ? 'selected' : '' }}>Sidebar</option>
                                        <option value="footer" {{ old('position', $ad->position) == 'footer' ? 'selected' : '' }}>Footer</option>
                                        <option value="content" {{ old('position', $ad->position) == 'content' ? 'selected' : '' }}>Content Area</option>
                                        <option value="popup" {{ old('position', $ad->position) == 'popup' ? 'selected' : '' }}>Popup</option>
                                        <option value="banner" {{ old('position', $ad->position) == 'banner' ? 'selected' : '' }}>Banner</option>
                                    </select>
                                    @error('position')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="sort_order" class="form-label">Sort Order</label>
                                    <input type="number" class="form-control @error('sort_order') is-invalid @enderror" 
                                           id="sort_order" name="sort_order" value="{{ old('sort_order', $ad->sort_order) }}" min="0">
                                    @error('sort_order')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">Lower numbers appear first</div>
                                </div>
                            </div>
                        </div>

                        <!-- Status & Scheduling -->
                        <div class="admin-card mb-4">
                            <div class="admin-card-header">
                                <h5 class="admin-card-title mb-0">
                                    <i class="fas fa-toggle-on me-2"></i>Status & Scheduling
                                </h5>
                            </div>
                            <div class="admin-card-body">
                                <div class="mb-3">
                                    <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                    <select class="form-select @error('status') is-invalid @enderror" 
                                            id="status" name="status" required onchange="toggleScheduleFields()">
                                        <option value="">Select Status</option>
                                        <option value="active" {{ old('status', $ad->status) == 'active' ? 'selected' : '' }}>Active</option>
                                        <option value="inactive" {{ old('status', $ad->status) == 'inactive' ? 'selected' : '' }}>Inactive</option>
                                        <option value="scheduled" {{ old('status', $ad->status) == 'scheduled' ? 'selected' : '' }}>Scheduled</option>
                                    </select>
                                    @error('status')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div id="schedule-fields" style="display: none;">
                                    <div class="mb-3">
                                        <label for="start_date" class="form-label">Start Date</label>
                                        <input type="datetime-local" class="form-control @error('start_date') is-invalid @enderror" 
                                               id="start_date" name="start_date" 
                                               value="{{ old('start_date', $ad->start_date ? $ad->start_date->format('Y-m-d\TH:i') : '') }}">
                                        @error('start_date')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="mb-3">
                                        <label for="end_date" class="form-label">End Date</label>
                                        <input type="datetime-local" class="form-control @error('end_date') is-invalid @enderror" 
                                               id="end_date" name="end_date" 
                                               value="{{ old('end_date', $ad->end_date ? $ad->end_date->format('Y-m-d\TH:i') : '') }}">
                                        @error('end_date')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="max_impressions" class="form-label">Max Impressions</label>
                                    <input type="number" class="form-control @error('max_impressions') is-invalid @enderror" 
                                           id="max_impressions" name="max_impressions" value="{{ old('max_impressions', $ad->max_impressions) }}" min="1">
                                    @error('max_impressions')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">Leave empty for unlimited impressions</div>
                                </div>
                            </div>
                        </div>

                        <!-- Statistics -->
                        <div class="admin-card mb-4">
                            <div class="admin-card-header">
                                <h5 class="admin-card-title mb-0">
                                    <i class="fas fa-chart-bar me-2"></i>Statistics
                                </h5>
                            </div>
                            <div class="admin-card-body">
                                <div class="row text-center">
                                    <div class="col-6">
                                        <div class="border-end">
                                            <h4 class="text-primary mb-1">{{ number_format($ad->impressions_count) }}</h4>
                                            <small class="text-muted">Impressions</small>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <h4 class="text-success mb-1">{{ number_format($ad->clicks_count) }}</h4>
                                        <small class="text-muted">Clicks</small>
                                    </div>
                                </div>
                                @if($ad->impressions_count > 0)
                                <div class="mt-3 text-center">
                                    <small class="text-muted">
                                        CTR: {{ number_format(($ad->clicks_count / $ad->impressions_count) * 100, 2) }}%
                                    </small>
                                </div>
                                @endif
                            </div>
                        </div>

                        <!-- Additional Options -->
                        <div class="admin-card mb-4">
                            <div class="admin-card-header">
                                <h5 class="admin-card-title mb-0">
                                    <i class="fas fa-cog me-2"></i>Options
                                </h5>
                            </div>
                            <div class="admin-card-body">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="open_in_new_tab"
                                           name="open_in_new_tab" value="1" {{ old('open_in_new_tab', $ad->open_in_new_tab) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="open_in_new_tab">
                                        Open link in new tab
                                    </label>
                                </div>

                                <div class="mb-3">
                                    <label for="target_pages" class="form-label">Target Pages</label>
                                    <select class="form-select" id="target_pages" name="target_pages[]" multiple>
                                        <option value="all" {{ in_array('all', old('target_pages', $ad->target_pages ?? [])) ? 'selected' : '' }}>All Pages</option>
                                        <option value="home" {{ in_array('home', old('target_pages', $ad->target_pages ?? [])) ? 'selected' : '' }}>Home</option>
                                        <option value="about" {{ in_array('about', old('target_pages', $ad->target_pages ?? [])) ? 'selected' : '' }}>About</option>
                                        <option value="news" {{ in_array('news', old('target_pages', $ad->target_pages ?? [])) ? 'selected' : '' }}>News</option>
                                        <option value="members" {{ in_array('members', old('target_pages', $ad->target_pages ?? [])) ? 'selected' : '' }}>Members</option>
                                        <option value="gallery" {{ in_array('gallery', old('target_pages', $ad->target_pages ?? [])) ? 'selected' : '' }}>Gallery</option>
                                        <option value="contact" {{ in_array('contact', old('target_pages', $ad->target_pages ?? [])) ? 'selected' : '' }}>Contact</option>
                                    </select>
                                    <div class="form-text">Hold Ctrl/Cmd to select multiple pages</div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="admin-card">
                            <div class="admin-card-body">
                                <div class="d-grid gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>Update Ad
                                    </button>
                                    <a href="{{ route('admin.ads.show', $ad) }}" class="btn btn-outline-info">
                                        <i class="fas fa-eye me-2"></i>View Ad
                                    </a>
                                    <a href="{{ route('admin.ads.index') }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-times me-2"></i>Cancel
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Toggle type-specific fields
function toggleTypeFields() {
    const type = document.getElementById('type').value;
    const thumbnailSection = document.getElementById('thumbnail-section');
    const contentLabel = document.getElementById('content-label');
    const contentHelp = document.getElementById('content-help');
    const contentFile = document.getElementById('content_file');

    if (type === 'video') {
        thumbnailSection.style.display = 'block';
        contentLabel.textContent = 'New Video File';
        contentHelp.textContent = 'Leave empty to keep current file. Supported formats: MP4, AVI, MOV, WMV. Max size: 20MB';
        contentFile.accept = 'video/*';
    } else if (type === 'image') {
        thumbnailSection.style.display = 'none';
        contentLabel.textContent = 'New Image File';
        contentHelp.textContent = 'Leave empty to keep current file. Supported formats: JPG, PNG, GIF. Max size: 20MB';
        contentFile.accept = 'image/*';
    } else {
        thumbnailSection.style.display = 'none';
        contentLabel.textContent = 'New Content File';
        contentHelp.textContent = 'Leave empty to keep current file. Supported formats: JPG, PNG, GIF, MP4, AVI, MOV, WMV. Max size: 20MB';
        contentFile.accept = 'image/*,video/*';
    }
}

// Toggle schedule fields
function toggleScheduleFields() {
    const status = document.getElementById('status').value;
    const scheduleFields = document.getElementById('schedule-fields');

    if (status === 'scheduled') {
        scheduleFields.style.display = 'block';
    } else {
        scheduleFields.style.display = 'none';
    }
}

// Preview uploaded file
function previewFile() {
    const file = document.getElementById('content_file').files[0];
    const previewArea = document.getElementById('preview-area');
    const contentPreview = document.getElementById('content-preview');

    if (file) {
        const reader = new FileReader();
        const fileType = file.type;

        reader.onload = function(e) {
            previewArea.style.display = 'block';

            if (fileType.startsWith('image/')) {
                contentPreview.innerHTML = `<img src="${e.target.result}" alt="Preview" style="max-width: 100%; max-height: 200px;">`;
            } else if (fileType.startsWith('video/')) {
                contentPreview.innerHTML = `<video controls style="max-width: 100%; max-height: 200px;">
                    <source src="${e.target.result}" type="${fileType}">
                    Your browser does not support the video tag.
                </video>`;
            }
        };

        reader.readAsDataURL(file);
    } else {
        previewArea.style.display = 'none';
    }
}

// Preview thumbnail
function previewThumbnail() {
    const file = document.getElementById('thumbnail_file').files[0];
    const thumbnailPreview = document.getElementById('thumbnail-preview');

    if (file) {
        const reader = new FileReader();

        reader.onload = function(e) {
            thumbnailPreview.style.display = 'block';
            thumbnailPreview.innerHTML = `
                <div class="mt-2">
                    <label class="form-label">New Thumbnail Preview</label><br>
                    <img src="${e.target.result}" alt="Thumbnail Preview" style="max-width: 100%; max-height: 100px;">
                </div>
            `;
        };

        reader.readAsDataURL(file);
    } else {
        thumbnailPreview.style.display = 'none';
    }
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    toggleTypeFields();
    toggleScheduleFields();
});
</script>
@endpush
