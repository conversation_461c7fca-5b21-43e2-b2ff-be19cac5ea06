@extends('layouts.admin')

@section('title', 'Quotation Management')

@section('content')
<div class="admin-wrapper">
    <div class="admin-main">
        <div class="admin-content">
            <!-- Enhanced Header Section -->
            <div class="d-flex justify-content-between align-items-center mb-5">
                <div>
                    <h1 class="admin-title mb-2 fw-bold text-dark">
                        <i class="fas fa-file-invoice me-3 text-primary"></i>Quotation Management
                    </h1>
                    <p class="text-muted mb-0 fs-6">Review and manage quotation requests from website visitors</p>
                </div>

               
            </div>

            <!-- Enhanced Alert Messages -->
            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show border-0 shadow-sm mb-4" role="alert">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-check-circle me-3 fs-5"></i>
                        <div>
                            <strong>Success!</strong> {{ session('success') }}
                        </div>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-danger alert-dismissible fade show border-0 shadow-sm mb-4" role="alert">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-exclamation-circle me-3 fs-5"></i>
                        <div>
                            <strong>Error!</strong> {{ session('error') }}
                        </div>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <!-- Enhanced Card Design -->
            <div class="admin-card shadow-sm border-0">
                <div class="d-flex justify-content-between align-items-center mb-4 pb-3 border-bottom">
                    <div>
                        <h4 class="admin-card-title mb-1 fw-semibold text-dark">
                            <i class="fas fa-inbox me-2 text-primary"></i>Quotation Requests
                        </h4>
                        <p class="text-muted mb-0 small">Manage incoming quotation requests and their status</p>
                    </div>
                </div>

                @if($quotations->count() > 0)
                    <!-- Enhanced Table with Modern Design -->
                    <div class="admin-table-responsive rounded-3 overflow-hidden shadow-sm">
                        <table class="admin-table mb-0" width="100%">
                            <thead class="bg-gradient-primary">
                                <tr>
                                    <th class="border-0 text-dark fw-semibold">
                                        <i class="fas fa-user me-2"></i>Contact Details
                                    </th>
                                    <th class="border-0 text-dark fw-semibold">
                                        <i class="fas fa-envelope me-2"></i>Email
                                    </th>
                                    <th class="border-0 text-dark fw-semibold">
                                        <i class="fas fa-building me-2"></i>Company
                                    </th>
                                    <th class="border-0 text-dark fw-semibold">
                                        <i class="fas fa-box me-2"></i>Products
                                    </th>
                                    <th class="border-0 text-dark fw-semibold">
                                        <i class="fas fa-circle-dot me-2"></i>Status
                                    </th>
                                    <th class="border-0 text-dark fw-semibold">
                                        <i class="fas fa-calendar me-2"></i>Submitted
                                    </th>
                                    <th class="border-0 text-dark fw-semibold text-center">
                                        <i class="fas fa-cogs me-2"></i>Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($quotations as $quotation)
                                <tr class="border-bottom">
                                    <td class="align-middle">
                                        <div class="d-flex align-items-center">
                                            <div class="bg-gradient-primary text-white rounded-circle d-flex align-items-center justify-content-center  me-3 shadow-sm"
                                                 style="width: 45px; height: 45px; font-size: 16px; font-weight: 600; background:blue">
                                                {{ strtoupper(substr($quotation->full_name, 0, 2)) }}
                                            </div>
                                            <div>
                                                <h6 class="mb-1 fw-semibold text-dark">{{ $quotation->full_name }}</h6>
                                                <div class="d-flex align-items-center text-muted small">
                                                    <i class="fas fa-phone me-2"></i>
                                                    <span>{{ $quotation->phone ?? 'No phone provided' }}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="align-middle">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-envelope text-primary me-2"></i>
                                            <a href="mailto:{{ $quotation->email }}" class="text-decoration-none fw-semibold">
                                                {{ $quotation->email }}
                                            </a>
                                        </div>
                                    </td>
                                    <td class="align-middle">
                                        @if($quotation->company_name)
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-building text-info me-2"></i>
                                                <span class="fw-semibold">{{ $quotation->company_name }}</span>
                                            </div>
                                        @else
                                            <div class="d-flex align-items-center text-muted">
                                                <i class="fas fa-minus-circle me-2"></i>
                                                <span class="small">No company</span>
                                            </div>
                                        @endif
                                    </td>
                                    <td class="align-middle">
                                        <div class="d-flex align-items-start">
                                            <i class="fas fa-box text-warning me-2 mt-1"></i>
                                            <div style="max-width: 200px;">
                                                <p class="mb-0 small text-dark">{{ Str::limit($quotation->products_string, 60) }}</p>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="align-middle">
                                        @if($quotation->processed)
                                            <span class="badge bg-success px-3 py-2 rounded-pill fw-semibold shadow-sm">
                                                <i class="fas fa-check-circle me-1"></i>Processed
                                            </span>
                                        @else
                                            <span class="badge bg-warning px-3 py-2 rounded-pill fw-semibold shadow-sm">
                                                <i class="fas fa-clock me-1"></i>Pending
                                            </span>
                                        @endif
                                    </td>
                                    <td class="align-middle">
                                        <div class="d-flex flex-column">
                                            <span class="fw-semibold text-dark">{{ $quotation->created_at->format('M d, Y') }}</span>
                                            <small class="text-muted">{{ $quotation->created_at->format('g:i A') }}</small>
                                        </div>
                                    </td>
                                    <td class="align-middle text-center">
                                        <div class="btn-group shadow-sm" role="group">
                                            <a href="{{ route('admin.quotations.show', $quotation) }}"
                                               class="btn btn-sm btn-outline-info rounded-start-pill"
                                               data-bs-toggle="tooltip" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <form action="{{ route('admin.quotations.update', $quotation) }}" method="POST" class="d-inline">
                                                @csrf
                                                @method('PUT')
                                                <input type="hidden" name="processed" value="{{ $quotation->processed ? 0 : 1 }}">
                                                <button type="submit"
                                                        class="btn btn-sm {{ $quotation->processed ? 'btn-outline-warning' : 'btn-outline-success' }}"
                                                        data-bs-toggle="tooltip"
                                                        title="{{ $quotation->processed ? 'Mark as pending' : 'Mark as processed' }}">
                                                    <i class="fas fa-{{ $quotation->processed ? 'undo' : 'check' }}"></i>
                                                </button>
                                            </form>
                                            <button type="button"
                                                    class="btn btn-sm btn-outline-danger rounded-end-pill"
                                                    onclick="confirmDelete({{ $quotation->id }})"
                                                    data-bs-toggle="tooltip" title="Delete Request">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- Enhanced Pagination -->
                    <div class="mt-4 d-flex justify-content-center">
                        {{ $quotations->links() }}
                    </div>
                @else
                    <div class="text-center py-5">
                        <div class="d-flex flex-column align-items-center justify-content-center py-4">
                            <div class="bg-light rounded-circle p-4 mb-4">
                                <i class="fas fa-file-invoice fa-3x text-muted"></i>
                            </div>
                            <h5 class="text-muted mb-2">No Quotation Requests Found</h5>
                            <p class="text-muted mb-4">Quotation requests from website visitors will appear here when submitted</p>
                            <div class="d-flex align-items-center text-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <small>Visitors can submit quotation requests through your website's contact form</small>
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow-lg">
            <div class="modal-header bg-danger text-white border-0">
                <h5 class="modal-title fw-bold" id="deleteModalLabel">
                    <i class="fas fa-exclamation-triangle me-2"></i>Confirm Deletion
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-4">
                <div class="d-flex align-items-center mb-3">
                    <div class="bg-danger-subtle rounded-circle p-3 me-3">
                        <i class="fas fa-trash text-danger fs-4"></i>
                    </div>
                    <div>
                        <h6 class="mb-1">Delete Quotation Request</h6>
                        <p class="text-muted mb-0 small">This action cannot be undone</p>
                    </div>
                </div>
                <p class="mb-2">Are you sure you want to delete this quotation request?</p>
                <div class="alert alert-warning border-0 bg-warning-subtle">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <small>This will permanently remove the quotation request and all associated data.</small>
                </div>
            </div>
            <div class="modal-footer border-0 bg-light">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>Cancel
                </button>
                <form id="deleteForm" method="POST" class="d-inline">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-2"></i>Delete Request
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(quotationId) {
    const form = document.getElementById('deleteForm');
    form.action = `/admin/quotations/${quotationId}`;
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}

// Initialize tooltips
document.addEventListener('DOMContentLoaded', function() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
@endsection