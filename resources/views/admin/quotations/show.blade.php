@extends('layouts.admin')

@section('title', 'Quotation Details')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="admin-title mb-1">Quotation Details</h2>
        <p class="text-muted mb-0">View detailed quotation request information</p>
    </div>
    <div>
        <a href="{{ route('admin.quotations.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>Back to Quotations
        </a>
    </div>
</div>

@if(session('success'))
    <div class="admin-alert admin-alert-success">
        <i class="fas fa-check-circle me-2"></i>
        {{ session('success') }}
    </div>
@endif

<div class="row">
    <div class="col-md-8">
        <div class="admin-card">
            <h5 class="admin-card-title">Quotation Request Details</h5>
            
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label fw-bold">Full Name</label>
                        <p class="form-control-plaintext">{{ $quotation->full_name }}</p>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label fw-bold">Email Address</label>
                        <p class="form-control-plaintext">{{ $quotation->email }}</p>
                    </div>
                </div>
            </div>
            
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label fw-bold">Phone Number</label>
                        <p class="form-control-plaintext">{{ $quotation->phone ?? 'Not provided' }}</p>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label fw-bold">Company Name</label>
                        <p class="form-control-plaintext">{{ $quotation->company_name ?? 'Not provided' }}</p>
                    </div>
                </div>
            </div>
            
            <div class="mb-4">
                <label class="form-label fw-bold">Selected Products</label>
                <div class="mt-2">
                    @if(is_array($quotation->products))
                        @foreach($quotation->products as $product)
                            <span class="badge bg-primary me-1 mb-1">{{ $product }}</span>
                        @endforeach
                    @else
                        <span class="badge bg-primary">{{ $quotation->products }}</span>
                    @endif
                </div>
            </div>
            
            <div class="mb-4">
                <label class="form-label fw-bold">Message</label>
                <div class="form-control-plaintext bg-light p-3 rounded">
                    {{ $quotation->message }}
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="admin-card">
            <h5 class="admin-card-title">Request Status</h5>
            
            <div class="mb-4">
                <label class="form-label fw-bold">Status</label>
                <div class="mt-2">
                    @if($quotation->processed)
                        <span class="badge bg-success fs-6">Processed</span>
                    @else
                        <span class="badge bg-warning fs-6">Pending</span>
                    @endif
                </div>
            </div>
            
            <div class="mb-4">
                <label class="form-label fw-bold">Submitted On</label>
                <p class="form-control-plaintext">{{ $quotation->created_at->format('F j, Y \a\t g:i A') }}</p>
            </div>
            
            <div class="mb-4">
                <label class="form-label fw-bold">Last Updated</label>
                <p class="form-control-plaintext">{{ $quotation->updated_at->format('F j, Y \a\t g:i A') }}</p>
            </div>
            
            <div class="mb-4">
                <label class="form-label fw-bold">Terms Accepted</label>
                <p class="form-control-plaintext">
                    @if($quotation->terms_accepted)
                        <i class="fas fa-check-circle text-success me-1"></i> Yes
                    @else
                        <i class="fas fa-times-circle text-danger me-1"></i> No
                    @endif
                </p>
            </div>
            
            <div class="d-grid gap-2">
                <form action="{{ route('admin.quotations.update', $quotation) }}" method="POST">
                    @csrf
                    @method('PUT')
                    <input type="hidden" name="processed" value="{{ $quotation->processed ? 0 : 1 }}">
                    <button type="submit" class="btn {{ $quotation->processed ? 'btn-warning' : 'btn-success' }}">
                        <i class="fas fa-{{ $quotation->processed ? 'undo' : 'check' }} me-2"></i>
                        {{ $quotation->processed ? 'Mark as Pending' : 'Mark as Processed' }}
                    </button>
                </form>
                
                <button type="button" class="btn btn-danger" onclick="confirmDelete({{ $quotation->id }})">
                    <i class="fas fa-trash me-2"></i>Delete Quotation
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this quotation request? This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(quotationId) {
    const form = document.getElementById('deleteForm');
    form.action = `/admin/quotations/${quotationId}`;
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}
</script>
@endsection