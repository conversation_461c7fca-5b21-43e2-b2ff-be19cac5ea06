@extends('layouts.admin')

@section('title', 'View Contact Form')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2 class="admin-title mb-0">Contact Form Details</h2>
    <a href="{{ route('admin.contact-forms.index') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i>Back to Contact Forms
    </a>
</div>

@if(session('success'))
    <div class="admin-alert admin-alert-success">
        <i class="fas fa-check-circle me-2"></i>
        {{ session('success') }}
    </div>
@endif

<div class="row">
    <div class="col-lg-8">
        <div class="admin-card">
            <div class="d-flex justify-content-between align-items-start mb-4">
                <div>
                    <h5 class="admin-card-title mb-2">Message Details</h5>
                    <p class="text-muted mb-0">Subject: {{ $contactForm->subject }}</p>
                </div>
                <div>
                    @if($contactForm->processed)
                        <span class="badge bg-success">Processed</span>
                    @else
                        <span class="badge bg-warning">Pending</span>
                    @endif
                </div>
            </div>
            
            <div class="mb-4">
                <p class="mb-0">{{ $contactForm->message }}</p>
            </div>
            
            <div class="d-flex gap-2">
                <form action="{{ route('admin.contact-forms.update', $contactForm) }}" method="POST" class="d-inline">
                    @csrf
                    @method('PUT')
                    <input type="hidden" name="processed" value="{{ $contactForm->processed ? 0 : 1 }}">
                    <button type="submit" class="btn btn-sm {{ $contactForm->processed ? 'btn-warning' : 'btn-success' }}">
                        <i class="fas fa-{{ $contactForm->processed ? 'undo' : 'check' }} me-2"></i>
                        {{ $contactForm->processed ? 'Mark as Pending' : 'Mark as Processed' }}
                    </button>
                </form>
                
                <button type="button" class="btn btn-sm btn-danger" onclick="confirmDelete({{ $contactForm->id }})">
                    <i class="fas fa-trash me-2"></i>Delete
                </button>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="admin-card">
            <h5 class="admin-card-title mb-4">Contact Information</h5>
            
            <div class="mb-3">
                <label class="form-label text-muted small mb-1">Name</label>
                <div class="fw-bold">{{ $contactForm->full_name }}</div>
            </div>
            
            <div class="mb-3">
                <label class="form-label text-muted small mb-1">Email</label>
                <div>
                    <a href="mailto:{{ $contactForm->email }}" class="text-decoration-none">
                        {{ $contactForm->email }}
                    </a>
                </div>
            </div>
            
            @if($contactForm->phone)
            <div class="mb-3">
                <label class="form-label text-muted small mb-1">Phone</label>
                <div>
                    <a href="tel:{{ $contactForm->phone }}" class="text-decoration-none">
                        {{ $contactForm->phone }}
                    </a>
                </div>
            </div>
            @endif
            
            @if($contactForm->company)
            <div class="mb-3">
                <label class="form-label text-muted small mb-1">Company</label>
                <div>{{ $contactForm->company }}</div>
            </div>
            @endif
            
            <div class="mb-3">
                <label class="form-label text-muted small mb-1">Newsletter</label>
                <div>
                    @if($contactForm->newsletter)
                        <span class="badge bg-success">Subscribed</span>
                    @else
                        <span class="badge bg-secondary">Not Subscribed</span>
                    @endif
                </div>
            </div>
            
            <div class="mb-0">
                <label class="form-label text-muted small mb-1">Submitted</label>
                <div>{{ $contactForm->created_at->format('F j, Y \a\t g:i A') }}</div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this contact form submission? This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form action="{{ route('admin.contact-forms.destroy', $contactForm) }}" method="POST">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(contactFormId) {
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}
</script>
@endsection
