@extends('layouts.admin')

@section('title', 'Dashboard')
@section('page-title', 'Dashboard')

@section('content')
<!-- Loading Overlay -->
<div id="dashboardLoader" class="dashboard-loader">
    <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
    </div>
    <p class="mt-2">Loading dashboard data...</p>
</div>
<!-- Welcome Message -->
@if(session('success'))
    <div class="admin-alert admin-alert-success">
        <i class="fas fa-check-circle me-2"></i>
        {{ session('success') }}
    </div>
@endif

<!-- Enhanced Stats Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="admin-stat-card admin-stat-card-primary">
            <div class="admin-stat-card-icon">
                <i class="fas fa-users"></i>
            </div>
            <div class="admin-stat-card-content">
                <h3 class="counter" data-target="{{ $totalMembers }}">0</h3>
                <p class="admin-stat-card-title">Total Members</p>
                <div class="admin-stat-card-footer">
                    <span class="admin-stat-badge admin-stat-badge-success">
                        <i class="fas fa-check-circle me-1"></i>{{ $activeMembers }} Active
                    </span>
                </div>
            </div>
            <div class="admin-stat-card-overlay"></div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="admin-stat-card admin-stat-card-success">
            <div class="admin-stat-card-icon">
                <i class="fas fa-newspaper"></i>
            </div>
            <div class="admin-stat-card-content">
                <h3 class="counter" data-target="{{ $publishedNews }}">0</h3>
                <p class="admin-stat-card-title">Published News</p>
                <div class="admin-stat-card-footer">
                    <span class="admin-stat-badge admin-stat-badge-info">
                        <i class="fas fa-newspaper me-1"></i>{{ $totalNews }} Total
                    </span>
                </div>
            </div>
            <div class="admin-stat-card-overlay"></div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="admin-stat-card admin-stat-card-warning">
            <div class="admin-stat-card-icon">
                <i class="fas fa-file-invoice"></i>
            </div>
            <div class="admin-stat-card-content">
                <h3 class="counter" data-target="{{ $unprocessedQuotations }}">0</h3>
                <p class="admin-stat-card-title">Pending Quotations</p>
                <div class="admin-stat-card-footer">
                    <span class="admin-stat-badge admin-stat-badge-muted">
                        <i class="fas fa-file-invoice me-1"></i>{{ $totalQuotations }} Total
                    </span>
                </div>
            </div>
            <div class="admin-stat-card-overlay"></div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="admin-stat-card admin-stat-card-info">
            <div class="admin-stat-card-icon">
                <i class="fas fa-envelope"></i>
            </div>
            <div class="admin-stat-card-content">
                <h3 class="counter" data-target="{{ $unprocessedContactForms }}">0</h3>
                <p class="admin-stat-card-title">Pending Messages</p>
                <div class="admin-stat-card-footer">
                    <span class="admin-stat-badge admin-stat-badge-muted">
                        <i class="fas fa-envelope me-1"></i>{{ $totalContactForms }} Total
                    </span>
                </div>
            </div>
            <div class="admin-stat-card-overlay"></div>
        </div>
    </div>
</div>

<!-- Additional Enhanced Stats Row -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="admin-stat-card admin-stat-card-purple">
            <div class="admin-stat-card-icon">
                <i class="fas fa-images"></i>
            </div>
            <div class="admin-stat-card-content">
                <h3 class="counter" data-target="{{ $activeHeroSliders }}">0</h3>
                <p class="admin-stat-card-title">Hero Sliders</p>
                <div class="admin-stat-card-footer">
                    <span class="admin-stat-badge admin-stat-badge-muted">
                        <i class="fas fa-images me-1"></i>{{ $totalHeroSliders }} Total
                    </span>
                </div>
            </div>
            <div class="admin-stat-card-overlay"></div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="admin-stat-card admin-stat-card-secondary">
            <div class="admin-stat-card-icon">
                <i class="fas fa-photo-video"></i>
            </div>
            <div class="admin-stat-card-content">
                <h3 class="counter" data-target="{{ $activeGalleries }}">0</h3>
                <p class="admin-stat-card-title">Gallery Items</p>
                <div class="admin-stat-card-footer">
                    <span class="admin-stat-badge admin-stat-badge-muted">
                        <i class="fas fa-photo-video me-1"></i>{{ $totalGalleries }} Total
                    </span>
                </div>
            </div>
            <div class="admin-stat-card-overlay"></div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="admin-stat-card admin-stat-card-success">
            <div class="admin-stat-card-icon">
                <i class="fas fa-chart-line"></i>
            </div>
            <div class="admin-stat-card-content">
                <h3 class="counter" data-target="{{ $recentMembers->count() + $recentNews->count() }}">0</h3>
                <p class="admin-stat-card-title">This Month</p>
                <div class="admin-stat-card-footer">
                    <span class="admin-stat-badge admin-stat-badge-success">
                        <i class="fas fa-chart-line me-1"></i>New Content
                    </span>
                </div>
            </div>
            <div class="admin-stat-card-overlay"></div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="admin-stat-card admin-stat-card-primary">
            <div class="admin-stat-card-icon">
                <i class="fas fa-server"></i>
            </div>
            <div class="admin-stat-card-content">
                <h3 class="text-success">100%</h3>
                <p class="admin-stat-card-title">System Health</p>
                <div class="admin-stat-card-footer">
                    <span class="admin-stat-badge admin-stat-badge-success">
                        <i class="fas fa-check-circle me-1"></i>All Systems Online
                    </span>
                </div>
            </div>
            <div class="admin-stat-card-overlay"></div>
        </div>
    </div>
</div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row">
    <div class="col-lg-8">
        <div class="admin-card">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="admin-card-title mb-0">Recent Activity</h5>
                <button class="btn btn-sm btn-outline-primary" id="refreshActivity">
                    <i class="fas fa-sync-alt me-1"></i>Refresh
                </button>
            </div>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Activity</th>
                            <th>Details</th>
                            <th>Date</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($recentMembers->take(3) as $member)
                        <tr>
                            <td><i class="fas fa-user-plus text-success me-2"></i>New member added</td>
                            <td>{{ $member->name }}</td>
                            <td>{{ $member->created_at->diffForHumans() }}</td>
                            <td><span class="badge bg-{{ $member->is_active ? 'success' : 'secondary' }}">{{ $member->is_active ? 'Active' : 'Inactive' }}</span></td>
                        </tr>
                        @empty
                        @endforelse

                        @forelse($recentNews->take(2) as $news)
                        <tr>
                            <td><i class="fas fa-newspaper text-info me-2"></i>News article</td>
                            <td>{{ Str::limit($news->title, 40) }}</td>
                            <td>{{ $news->created_at->diffForHumans() }}</td>
                            <td><span class="badge {{ $news->status_badge_class }}">{{ ucfirst($news->status) }}</span></td>
                        </tr>
                        @empty
                        @endforelse

                        @forelse($recentQuotations->take(2) as $quotation)
                        <tr>
                            <td><i class="fas fa-file-invoice text-warning me-2"></i>Quotation request</td>
                            <td>{{ $quotation->company_name ?: $quotation->full_name }}</td>
                            <td>{{ $quotation->created_at->diffForHumans() }}</td>
                            <td><span class="badge bg-{{ $quotation->processed ? 'success' : 'warning' }}">{{ $quotation->processed ? 'Processed' : 'Pending' }}</span></td>
                        </tr>
                        @empty
                        @endforelse

                        @forelse($recentContactForms->take(2) as $contact)
                        <tr>
                            <td><i class="fas fa-envelope text-primary me-2"></i>Contact message</td>
                            <td>{{ $contact->full_name }}</td>
                            <td>{{ $contact->created_at->diffForHumans() }}</td>
                            <td><span class="badge bg-{{ $contact->processed ? 'success' : 'info' }}">{{ $contact->processed ? 'Processed' : 'New' }}</span></td>
                        </tr>
                        @empty
                        @endforelse

                        @if($recentMembers->isEmpty() && $recentNews->isEmpty() && $recentQuotations->isEmpty() && $recentContactForms->isEmpty())
                        <tr>
                            <td colspan="4" class="text-center text-muted py-4">
                                <i class="fas fa-inbox fa-2x mb-2"></i><br>
                                No recent activity found
                            </td>
                        </tr>
                        @endif
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="admin-card">
            <h5 class="admin-card-title">Quick Actions</h5>
            <div class="d-grid gap-2">
                <a href="{{ route('admin.members.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Add New Member
                </a>
                <a href="{{ route('admin.news.create') }}" class="btn btn-success">
                    <i class="fas fa-newspaper me-2"></i>Create News Article
                </a>
                <a href="{{ route('admin.hero-sliders.create') }}" class="btn btn-info">
                    <i class="fas fa-images me-2"></i>Add Hero Slider
                </a>
                <a href="{{ route('admin.galleries.create') }}" class="btn btn-secondary">
                    <i class="fas fa-photo-video me-2"></i>Add Gallery Item
                </a>
                <a href="{{ route('admin.quotations.index') }}" class="btn btn-warning">
                    <i class="fas fa-file-invoice me-2"></i>View Quotations
                    @if($unprocessedQuotations > 0)
                        <span class="badge bg-light text-dark ms-2">{{ $unprocessedQuotations }}</span>
                    @endif
                </a>
                <a href="{{ route('admin.contact-forms.index') }}" class="btn btn-outline-primary">
                    <i class="fas fa-envelope me-2"></i>View Messages
                    @if($unprocessedContactForms > 0)
                        <span class="badge bg-primary ms-2">{{ $unprocessedContactForms }}</span>
                    @endif
                </a>
            </div>
        </div>

        
    </div>
</div>

<!-- Charts Section -->
<div class="row mt-4">
    <div class="col-lg-6">
        <div class="admin-card">
            <h5 class="admin-card-title">Monthly Growth</h5>
            <div class="chart-container" style="position: relative; height: 350px; width: 100%;">
                <canvas id="monthlyGrowthChart"></canvas>
            </div>
        </div>
    </div>
    <div class="col-lg-6">
        <div class="admin-card">
            <h5 class="admin-card-title">Content Distribution</h5>
            <div class="chart-container" style="position: relative; height: 350px; width: 100%;">
                <canvas id="contentDistributionChart"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Activity Timeline -->
<div class="row mt-4">
    <div class="col-12">
        <div class="admin-card">
            <h5 class="admin-card-title">Activity Timeline</h5>
            <div class="chart-container" style="position: relative; height: 250px; width: 100%;">
                <canvas id="activityTimelineChart"></canvas>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Hide loading overlay after page loads
    const loader = document.getElementById('dashboardLoader');
    if (loader) {
        setTimeout(() => {
            loader.classList.add('hidden');
            setTimeout(() => {
                loader.style.display = 'none';
            }, 300);
        }, 500);
    }

    // Add loading states to cards during data fetch
    const cards = document.querySelectorAll('.admin-card');
    cards.forEach(card => {
        card.classList.add('loading');
        setTimeout(() => {
            card.classList.remove('loading');
        }, Math.random() * 1000 + 500);
    });
    // Monthly Growth Chart
    const monthlyGrowthCtx = document.getElementById('monthlyGrowthChart').getContext('2d');
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

    // Prepare data for monthly growth
    const memberData = Array(12).fill(0);
    const newsData = Array(12).fill(0);
    const quotationData = Array(12).fill(0);

    @foreach($membersByMonth as $data)
        memberData[{{ $data->month - 1 }}] = {{ $data->count }};
    @endforeach

    @foreach($newsByMonth as $data)
        newsData[{{ $data->month - 1 }}] = {{ $data->count }};
    @endforeach

    @foreach($quotationsByMonth as $data)
        quotationData[{{ $data->month - 1 }}] = {{ $data->count }};
    @endforeach

    try {
        new Chart(monthlyGrowthCtx, {
            type: 'line',
            data: {
                labels: monthNames,
                datasets: [{
                    label: 'New Members',
                    data: memberData,
                    borderColor: 'rgb(54, 162, 235)',
                    backgroundColor: 'rgba(54, 162, 235, 0.1)',
                    tension: 0.4
                }, {
                    label: 'News Articles',
                    data: newsData,
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.1)',
                    tension: 0.4
                }, {
                    label: 'Quotation Requests',
                    data: quotationData,
                    borderColor: 'rgb(255, 206, 86)',
                    backgroundColor: 'rgba(255, 206, 86, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    mode: 'index',
                    intersect: false,
                },
                scales: {
                    x: {
                        display: true,
                        title: {
                            display: true,
                            text: 'Month'
                        },
                        grid: {
                            display: false
                        }
                    },
                    y: {
                        display: true,
                        title: {
                            display: true,
                            text: 'Count'
                        },
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: 'Monthly Growth Trends',
                        font: {
                            size: 16,
                            weight: 'bold'
                        }
                    },
                    legend: {
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            padding: 20
                        }
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: 'white',
                        bodyColor: 'white',
                        borderColor: 'rgba(255, 255, 255, 0.1)',
                        borderWidth: 1
                    }
                },
                elements: {
                    line: {
                        tension: 0.4
                    },
                    point: {
                        radius: 4,
                        hoverRadius: 6
                    }
                }
            }
        });
    } catch (error) {
        console.error('Error creating monthly growth chart:', error);
        monthlyGrowthCtx.canvas.parentElement.innerHTML = '<div class="alert alert-warning">Chart could not be loaded</div>';
    }

    // Content Distribution Chart
    const contentDistributionCtx = document.getElementById('contentDistributionChart').getContext('2d');
    try {
        new Chart(contentDistributionCtx, {
            type: 'doughnut',
            data: {
                labels: ['Members', 'News Articles', 'Gallery Items', 'Hero Sliders'],
                datasets: [{
                    data: [{{ $totalMembers }}, {{ $totalNews }}, {{ $totalGalleries }}, {{ $totalHeroSliders }}],
                    backgroundColor: [
                        'rgba(54, 162, 235, 0.8)',
                        'rgba(75, 192, 192, 0.8)',
                        'rgba(255, 206, 86, 0.8)',
                        'rgba(153, 102, 255, 0.8)'
                    ],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Content Distribution Overview',
                        font: {
                            size: 16,
                            weight: 'bold'
                        }
                    },
                    legend: {
                        position: 'bottom',
                        labels: {
                            usePointStyle: true,
                            padding: 15,
                            font: {
                                size: 12
                            }
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: 'white',
                        bodyColor: 'white',
                        borderColor: 'rgba(255, 255, 255, 0.1)',
                        borderWidth: 1,
                        callbacks: {
                            label: function(context) {
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((context.parsed / total) * 100).toFixed(1);
                                return context.label + ': ' + context.parsed + ' (' + percentage + '%)';
                            }
                        }
                    }
                },
                layout: {
                    padding: {
                        top: 20,
                        bottom: 20
                    }
                }
            }
        });
    } catch (error) {
        console.error('Error creating content distribution chart:', error);
        contentDistributionCtx.canvas.parentElement.innerHTML = '<div class="alert alert-warning">Chart could not be loaded</div>';
    }

    // Activity Timeline Chart
    const activityTimelineCtx = document.getElementById('activityTimelineChart').getContext('2d');
    try {
        new Chart(activityTimelineCtx, {
            type: 'bar',
            data: {
                labels: ['Pending Quotations', 'Pending Messages', 'Active Members', 'Published News'],
                datasets: [{
                    label: 'Count',
                    data: [{{ $unprocessedQuotations }}, {{ $unprocessedContactForms }}, {{ $activeMembers }}, {{ $publishedNews }}],
                    backgroundColor: [
                        'rgba(255, 193, 7, 0.8)',
                        'rgba(23, 162, 184, 0.8)',
                        'rgba(40, 167, 69, 0.8)',
                        'rgba(108, 117, 125, 0.8)'
                    ],
                    borderColor: [
                        'rgb(255, 193, 7)',
                        'rgb(23, 162, 184)',
                        'rgb(40, 167, 69)',
                        'rgb(108, 117, 125)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        display: true,
                        grid: {
                            display: false
                        },
                        ticks: {
                            maxRotation: 45,
                            minRotation: 0
                        }
                    },
                    y: {
                        display: true,
                        title: {
                            display: true,
                            text: 'Count'
                        },
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: 'Current System Status',
                        font: {
                            size: 16,
                            weight: 'bold'
                        }
                    },
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: 'white',
                        bodyColor: 'white',
                        borderColor: 'rgba(255, 255, 255, 0.1)',
                        borderWidth: 1,
                        callbacks: {
                            title: function(context) {
                                return context[0].label;
                            },
                            label: function(context) {
                                return 'Count: ' + context.parsed.y;
                            }
                        }
                    }
                },
                layout: {
                    padding: {
                        top: 20,
                        bottom: 10
                    }
                }
            }
        });
    } catch (error) {
        console.error('Error creating activity timeline chart:', error);
        activityTimelineCtx.canvas.parentElement.innerHTML = '<div class="alert alert-warning">Chart could not be loaded</div>';
    }

    // Refresh activity functionality
    const refreshBtn = document.getElementById('refreshActivity');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function() {
            const icon = this.querySelector('i');
            icon.classList.add('fa-spin');
            this.disabled = true;

            // Simulate refresh (in real implementation, you'd make an AJAX call)
            setTimeout(() => {
                icon.classList.remove('fa-spin');
                this.disabled = false;

                // Show success message
                const alert = document.createElement('div');
                alert.className = 'alert alert-success alert-dismissible fade show mt-2';
                alert.innerHTML = `
                    <i class="fas fa-check-circle me-2"></i>Activity feed refreshed successfully!
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;

                const activityCard = document.querySelector('.admin-card');
                activityCard.insertBefore(alert, activityCard.firstChild);

                // Auto-hide after 3 seconds
                setTimeout(() => {
                    alert.remove();
                }, 3000);
            }, 1000);
        });
    }

    // Auto-refresh activity every 5 minutes
    setInterval(() => {
        if (refreshBtn && !refreshBtn.disabled) {
            refreshBtn.click();
        }
    }, 300000); // 5 minutes
});
</script>
@endpush

