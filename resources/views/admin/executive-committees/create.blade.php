@extends('layouts.admin')

@section('title', 'Add Executive Committee')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2 class="admin-title mb-0">Add Executive Committee</h2>
    <a href="{{ route('admin.executive-committees.index') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i>Back to List
    </a>
</div>

@if(session('success'))
    <div class="admin-alert admin-alert-success">
        <i class="fas fa-check-circle me-2"></i>
        {{ session('success') }}
    </div>
@endif

@if(session('error'))
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <strong>Whoops!</strong> There were some problems with your input.
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        <ul class="mb-0 mt-2">
            @foreach($errors->all() as $error)
                <li>{{ $error }}</li>
            @endforeach
        </ul>
    </div>
@endif

<div class="admin-card">
    <h5 class="admin-card-title mb-4">Executive Committee Details</h5>
    
    <form action="{{ route('admin.executive-committees.store') }}" method="POST" enctype="multipart/form-data">
        @csrf
        
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="year" class="form-label">Year *</label>
                    <input type="text" class="form-control" id="year" name="year" value="{{ old('year') }}" required>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="title" class="form-label">Title *</label>
                    <input type="text" class="form-control" id="title" name="title" value="{{ old('title') }}" required>
                </div>
            </div>
        </div>
        
        <div class="mb-3">
            <label for="description" class="form-label">Description</label>
            <textarea class="form-control" id="description" name="description" rows="4">{{ old('description') }}</textarea>
        </div>
        
        <div class="mb-3">
            <label for="pdf" class="form-label">PDF Document</label>
            <input type="file" class="form-control" id="pdf" name="pdf" accept="application/pdf">
            <div class="form-text">Upload a PDF document (max 2MB)</div>
        </div>
        
        <div class="mb-3 form-check">
            <input type="checkbox" class="form-check-input" id="is_active" name="is_active" {{ old('is_active') ? 'checked' : '' }}>
            <label class="form-check-label" for="is_active">Active</label>
        </div>
        
        <div class="d-flex gap-2">
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save me-2"></i>Save Executive Committee
            </button>
            <a href="{{ route('admin.executive-committees.index') }}" class="btn btn-secondary">
                <i class="fas fa-times me-2"></i>Cancel
            </a>
        </div>
    </form>
</div>
@endsection
