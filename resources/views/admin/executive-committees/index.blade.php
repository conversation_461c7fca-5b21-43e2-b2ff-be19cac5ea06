@extends('layouts.admin')

@section('title', 'Executive Committee Management')

@section('content')
<div class="admin-wrapper">
    <div class="admin-main">
        <div class="admin-content">
            <!-- Enhanced Header Section -->
            <div class="d-flex justify-content-between align-items-center mb-5">
                <div>
                    <h1 class="admin-title mb-2 fw-bold text-dark">
                        <i class="fas fa-users-cog me-3 text-primary"></i>Executive Committee Management
                    </h1>
                    <p class="text-muted mb-0 fs-6">Manage executive committee documents and information</p>
                </div>

                <div class="d-flex gap-2">
                   
                   
                    <a href="{{ route('admin.executive-committees.create') }}" class="btn btn-primary btn-sm shadow-sm">
                        <i class="fas fa-plus me-2"></i>Add New Document
                    </a>
                </div>
            </div>

            <!-- Enhanced Alert Messages -->
            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show border-0 shadow-sm mb-4" role="alert">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-check-circle me-3 fs-5"></i>
                        <div>
                            <strong>Success!</strong> {{ session('success') }}
                        </div>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-danger alert-dismissible fade show border-0 shadow-sm mb-4" role="alert">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-exclamation-circle me-3 fs-5"></i>
                        <div>
                            <strong>Error!</strong> {{ session('error') }}
                        </div>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <!-- Enhanced Card Design -->
            <div class="admin-card shadow-sm border-0">
                <div class="d-flex justify-content-between align-items-center mb-4 pb-3 border-bottom">
                    <div>
                        <h4 class="admin-card-title mb-1 fw-semibold text-dark">
                            <i class="fas fa-folder-open me-2 text-primary"></i>Executive Committee Documents
                        </h4>
                        <p class="text-muted mb-0 small">Manage executive committee documents and their status</p>
                    </div>
                </div>
    
                @if($executiveCommittees->count() > 0)
                    <!-- Enhanced Table with Modern Design -->
                    <div class="admin-table-responsive rounded-3 overflow-hidden shadow-sm">
                        <table class="admin-table mb-0" width="100%">
                            <thead class="bg-gradient-primary" >
                                <tr>
                                    <th class="border-0 text-dark fw-semibold">
                                        <i class="fas fa-heading me-2"></i>Title
                                    </th>
                                    <th class="border-0 text-dark fw-semibold">
                                        <i class="fas fa-calendar me-2"></i>Year
                                    </th>
                                    <th class="border-0 text-dark fw-semibold">
                                        <i class="fas fa-file-pdf me-2"></i>Document
                                    </th>
                                    <th class="border-0 text-dark fw-semibold">
                                        <i class="fas fa-circle-dot me-2"></i>Status
                                    </th>
                                    <th class="border-0 text-dark fw-semibold">
                                        <i class="fas fa-calendar-plus me-2"></i>Created
                                    </th>
                                    <th class="border-0 text-dark fw-semibold text-center">
                                        <i class="fas fa-cogs me-2"></i>Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($executiveCommittees as $executiveCommittee)
                                <tr class="border-bottom">
                                    <td class="align-middle">
                                        <div class="d-flex align-items-center">
                                            <div class="bg-gradient-primary text-dark rounded-circle d-flex align-items-center justify-content-center me-3 shadow-sm"
                                                 style="width: 45px; height: 45px; font-size: 16px; font-weight: 600;">
                                                {{ strtoupper(substr($executiveCommittee->title, 0, 2)) }}
                                            </div>
                                            <div>
                                                <h6 class="mb-0 fw-semibold text-dark">{{ $executiveCommittee->title }}</h6>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="align-middle">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-calendar text-info me-2"></i>
                                            <span class="fw-semibold">{{ $executiveCommittee->year }}</span>
                                        </div>
                                    </td>
                                    <td class="align-middle">
                                        @if($executiveCommittee->pdf_path)
                                            <a href="{{ $executiveCommittee->pdf_url }}" target="_blank" class="btn btn-sm btn-outline-primary shadow-sm">
                                                <i class="fas fa-file-pdf me-2"></i>View PDF
                                            </a>
                                        @else
                                            <div class="d-flex align-items-center text-muted">
                                                <i class="fas fa-minus-circle me-2"></i>
                                                <span class="small">No PDF</span>
                                            </div>
                                        @endif
                                    </td>
                                    <td class="align-middle">
                                        @if($executiveCommittee->is_active)
                                            <span class="badge bg-success px-3 py-2 rounded-pill fw-semibold shadow-sm">
                                                <i class="fas fa-check-circle me-1"></i>Active
                                            </span>
                                        @else
                                            <span class="badge bg-secondary px-3 py-2 rounded-pill fw-semibold shadow-sm">
                                                <i class="fas fa-pause-circle me-1"></i>Inactive
                                            </span>
                                        @endif
                                    </td>
                                    <td class="align-middle">
                                        <div class="d-flex flex-column">
                                            <span class="fw-semibold text-dark">{{ $executiveCommittee->created_at->format('M d, Y') }}</span>
                                            <small class="text-muted">{{ $executiveCommittee->created_at->format('g:i A') }}</small>
                                        </div>
                                    </td>
                                    <td class="align-middle text-center">
                                        <div class="btn-group shadow-sm" role="group">
                                            <a href="{{ route('admin.executive-committees.show', $executiveCommittee) }}"
                                               class="btn btn-sm btn-outline-info rounded-start-pill"
                                               data-bs-toggle="tooltip" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.executive-committees.edit', $executiveCommittee) }}"
                                               class="btn btn-sm btn-outline-warning"
                                               data-bs-toggle="tooltip" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <form action="{{ route('admin.executive-committees.toggle-active', $executiveCommittee) }}" method="POST" class="d-inline">
                                                @csrf
                                                @method('PATCH')
                                                <button type="submit"
                                                        class="btn btn-sm {{ $executiveCommittee->is_active ? 'btn-outline-secondary' : 'btn-outline-success' }}"
                                                        data-bs-toggle="tooltip"
                                                        title="{{ $executiveCommittee->is_active ? 'Deactivate' : 'Activate' }}">
                                                    <i class="fas fa-{{ $executiveCommittee->is_active ? 'times' : 'check' }}"></i>
                                                </button>
                                            </form>
                                            <button type="button"
                                                    class="btn btn-sm btn-outline-danger rounded-end-pill"
                                                    onclick="confirmDelete({{ $executiveCommittee->id }})"
                                                    data-bs-toggle="tooltip" title="Delete Document">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    <!-- Enhanced Pagination -->
                    <div class="mt-4 d-flex justify-content-center">
                        {{ $executiveCommittees->links() }}
                    </div>
                @else
                    <div class="text-center py-5">
                        <div class="d-flex flex-column align-items-center justify-content-center py-4">
                            <div class="bg-light rounded-circle p-4 mb-4">
                                <i class="fas fa-users-cog fa-3x text-muted"></i>
                            </div>
                            <h5 class="text-muted mb-2">No Executive Committee Documents Found</h5>
                            <p class="text-muted mb-4">Executive committee documents will appear here when added</p>
                            <a href="{{ route('admin.executive-committees.create') }}" class="btn btn-primary shadow-sm">
                                <i class="fas fa-plus me-2"></i>Add New Document
                            </a>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow-lg">
            <div class="modal-header bg-danger text-white border-0">
                <h5 class="modal-title fw-bold" id="deleteModalLabel">
                    <i class="fas fa-exclamation-triangle me-2"></i>Confirm Deletion
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-4">
                <div class="d-flex align-items-center mb-3">
                    <div class="bg-danger-subtle rounded-circle p-3 me-3">
                        <i class="fas fa-trash text-danger fs-4"></i>
                    </div>
                    <div>
                        <h6 class="mb-1">Delete Executive Committee Document</h6>
                        <p class="text-muted mb-0 small">This action cannot be undone</p>
                    </div>
                </div>
                <p class="mb-2">Are you sure you want to delete this executive committee document?</p>
                <div class="alert alert-warning border-0 bg-warning-subtle">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <small>This will permanently remove the executive committee document and all associated data.</small>
                </div>
            </div>
            <div class="modal-footer border-0 bg-light">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>Cancel
                </button>
                <form id="deleteForm" method="POST" class="d-inline">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-2"></i>Delete Document
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(executiveCommitteeId) {
    const form = document.getElementById('deleteForm');
    form.action = `/admin/executive-committees/${executiveCommitteeId}`;
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}

// Initialize tooltips
document.addEventListener('DOMContentLoaded', function() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
@endsection
