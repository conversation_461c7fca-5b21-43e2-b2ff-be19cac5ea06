@extends('layouts.admin')

@section('title', 'View Executive Committee')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2 class="admin-title mb-0">Executive Committee Details</h2>
    <a href="{{ route('admin.executive-committees.index') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i>Back to List
    </a>
</div>

@if(session('success'))
    <div class="admin-alert admin-alert-success">
        <i class="fas fa-check-circle me-2"></i>
        {{ session('success') }}
    </div>
@endif

<div class="row">
    <div class="col-lg-8">
        <div class="admin-card">
            <div class="d-flex justify-content-between align-items-start mb-4">
                <div>
                    <h5 class="admin-card-title mb-2">{{ $executiveCommittee->title }}</h5>
                    <p class="text-muted mb-0">Year: {{ $executiveCommittee->year }}</p>
                </div>
                <div>
                    @if($executiveCommittee->is_active)
                        <span class="badge bg-success">Active</span>
                    @else
                        <span class="badge bg-secondary">Inactive</span>
                    @endif
                </div>
            </div>
            
            <div class="mb-4">
                <p class="mb-0">{{ $executiveCommittee->description ?? 'No description provided.' }}</p>
            </div>
            
            @if($executiveCommittee->pdf_path)
                <div class="mb-4">
                    <a href="{{ $executiveCommittee->pdf_url }}" target="_blank" class="btn btn-primary">
                        <i class="fas fa-file-pdf me-2"></i>View PDF Document
                    </a>
                </div>
            @endif
            
            <div class="d-flex gap-2">
                <a href="{{ route('admin.executive-committees.edit', $executiveCommittee) }}" class="btn btn-warning">
                    <i class="fas fa-edit me-2"></i>Edit
                </a>
                
                <form action="{{ route('admin.executive-committees.toggle-active', $executiveCommittee) }}" method="POST" class="d-inline">
                    @csrf
                    @method('PATCH')
                    <button type="submit" class="btn {{ $executiveCommittee->is_active ? 'btn-secondary' : 'btn-success' }}">
                        <i class="fas fa-{{ $executiveCommittee->is_active ? 'times' : 'check' }} me-2"></i>
                        {{ $executiveCommittee->is_active ? 'Deactivate' : 'Activate' }}
                    </button>
                </form>
                
                <button type="button" class="btn btn-danger" onclick="confirmDelete({{ $executiveCommittee->id }})">
                    <i class="fas fa-trash me-2"></i>Delete
                </button>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="admin-card">
            <h5 class="admin-card-title mb-4">Document Information</h5>
            
            <div class="mb-3">
                <label class="form-label text-muted small mb-1">Created</label>
                <div>{{ $executiveCommittee->created_at->format('F j, Y \a\t g:i A') }}</div>
            </div>
            
            <div class="mb-3">
                <label class="form-label text-muted small mb-1">Last Updated</label>
                <div>{{ $executiveCommittee->updated_at->format('F j, Y \a\t g:i A') }}</div>
            </div>
            
            @if($executiveCommittee->pdf_path)
            <div class="mb-0">
                <label class="form-label text-muted small mb-1">PDF Document</label>
                <div class="d-flex align-items-center">
                    <i class="fas fa-file-pdf text-danger me-2"></i>
                    <div>
                        <div>Document uploaded</div>
                        <small class="text-muted">{{ basename($executiveCommittee->pdf_path) }}</small>
                    </div>
                </div>
            </div>
            @else
            <div class="mb-0">
                <label class="form-label text-muted small mb-1">PDF Document</label>
                <div>
                    <span class="badge bg-warning">Not uploaded</span>
                </div>
            </div>
            @endif
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this executive committee document? This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form action="{{ route('admin.executive-committees.destroy', $executiveCommittee) }}" method="POST">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(executiveCommitteeId) {
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}
</script>
@endsection
