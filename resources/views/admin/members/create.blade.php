@extends('layouts.admin')

@section('title', 'Add New Member')

@section('content')
<div class="admin-wrapper">
    <div class="admin-main">
        <div class="admin-content">
            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i>{{ session('error') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if($errors->any())
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Whoops!</strong> There were some problems with your input.
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    <ul class="mb-0 mt-2">
                        @foreach($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <div class="admin-card">
                <h5 class="admin-card-title mb-0">Member Details</h5>
                
                <form action="{{ route('admin.members.store') }}" method="POST">
                    @csrf
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="admin-form-group">
                                <label for="name" class="admin-form-label">Company Name *</label>
                                <input type="text" class="admin-form-control" id="name" name="name"
                                       value="{{ old('name') }}" placeholder="Enter company name" required>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="admin-form-group">
                                <label for="address" class="admin-form-label">Address *</label>
                                <input type="text" class="admin-form-control" id="address" name="address"
                                       value="{{ old('address') }}" placeholder="Enter address" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="admin-form-group">
                        <label for="full_address" class="admin-form-label">Full Address</label>
                        <textarea class="admin-form-control" id="full_address" name="full_address" rows="3"
                                  placeholder="Enter full address with line breaks">{{ old('full_address') }}</textarea>
                    </div>
                    
                    <div class="admin-form-group">
                        <label for="description" class="admin-form-label">Description</label>
                        <textarea class="admin-form-control" id="description" name="description" rows="3"
                                  placeholder="Enter company description">{{ old('description') }}</textarea>
                    </div>


                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="admin-form-group">
                                <label class="admin-form-label">Email Addresses</label>
                                <div id="emails-container">
                                    <div class="email-row mb-2">
                                        <div class="input-group">
                                            <input type="email" class="admin-form-control" name="emails[0]"
                                                   value="{{ old('emails.0') }}" placeholder="Enter email address">
                                            <button class="btn btn-outline-danger remove-email" type="button" style="display: none;">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <button type="button" class="btn btn-outline-primary btn-sm mt-2" id="add-email">
                                    <i class="fas fa-plus me-1"></i>Add Email
                                </button>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="admin-form-group">
                                <label class="admin-form-label">Phone Numbers</label>
                                <div id="phones-container">
                                    <div class="phone-row mb-2">
                                        <div class="input-group">
                                            <input type="text" class="admin-form-control" name="phones[0]"
                                                   value="{{ old('phones.0') }}" placeholder="Enter phone number">
                                            <button class="btn btn-outline-danger remove-phone" type="button" style="display: none;">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <button type="button" class="btn btn-outline-primary btn-sm mt-2" id="add-phone">
                                    <i class="fas fa-plus me-1"></i>Add Phone
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="admin-form-group">
                        <label for="website" class="admin-form-label">Website</label>
                        <input type="url" class="admin-form-control" id="website" name="website"
                               value="{{ old('website') }}" placeholder="https://example.com">
                    </div>
                    
                    
                    
                    <div class="admin-form-group">
                        <label class="admin-form-label">Contact Persons</label>
                        <div id="contact-persons-container">
                            <div class="contact-person-row mb-3">
                                <div class="row">
                                    <div class="col-md-5">
                                        <input type="text" class="admin-form-control mb-2" name="contact_persons[0][name]"
                                               placeholder="Contact person name">
                                    </div>
                                    <div class="col-md-5">
                                        <input type="text" class="admin-form-control mb-2" name="contact_persons[0][phone]"
                                               placeholder="Phone number">
                                    </div>
                                    <div class="col-md-2">
                                        <button class="btn btn-outline-danger remove-contact-person" type="button" style="display: none;">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <button type="button" class="btn btn-outline-primary btn-sm mt-2" id="add-contact-person">
                            <i class="fas fa-plus me-1"></i>Add Contact Person
                        </button>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="admin-form-group">
                                <label for="sort_order" class="admin-form-label">Sort Order</label>
                                <input type="number" class="admin-form-control" id="sort_order" name="sort_order"
                                       value="{{ old('sort_order', 0) }}" min="0">
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="admin-form-group">
                                <label for="is_active" class="admin-form-label">Status</label>
                                <select class="form-select" id="is_active" name="is_active">
                                    <option value="1" {{ old('is_active', true) ? 'selected' : '' }}>Active</option>
                                    <option value="0" {{ old('is_active', true) ? '' : 'selected' }}>Inactive</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between mt-4">
                        <a href="{{ route('admin.members.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Save Member
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>


// Email management
document.getElementById('add-email').addEventListener('click', function() {
    const container = document.getElementById('emails-container');
    const index = container.querySelectorAll('.email-row').length;

    const newEmailRow = document.createElement('div');
    newEmailRow.className = 'email-row mb-2';
    newEmailRow.innerHTML = `
        <div class="input-group">
            <input type="email" class="admin-form-control" name="emails[${index}]"
                   placeholder="Enter email address">
            <button class="btn btn-outline-danger remove-email" type="button">
                <i class="fas fa-trash"></i>
            </button>
        </div>
    `;

    container.appendChild(newEmailRow);

    // Show remove button for all emails except the first one
    if (index > 0) {
        container.querySelectorAll('.remove-email').forEach(btn => {
            btn.style.display = 'block';
        });
    }
});

// Remove email
document.getElementById('emails-container').addEventListener('click', function(e) {
    if (e.target.closest('.remove-email')) {
        e.target.closest('.email-row').remove();

        // Hide remove button if only one email left
        const emailRows = this.querySelectorAll('.email-row');
        if (emailRows.length === 1) {
            emailRows[0].querySelector('.remove-email').style.display = 'none';
        }
    }
});

// Phone management
document.getElementById('add-phone').addEventListener('click', function() {
    const container = document.getElementById('phones-container');
    const index = container.querySelectorAll('.phone-row').length;

    const newPhoneRow = document.createElement('div');
    newPhoneRow.className = 'phone-row mb-2';
    newPhoneRow.innerHTML = `
        <div class="input-group">
            <input type="text" class="admin-form-control" name="phones[${index}]"
                   placeholder="Enter phone number">
            <button class="btn btn-outline-danger remove-phone" type="button">
                <i class="fas fa-trash"></i>
            </button>
        </div>
    `;

    container.appendChild(newPhoneRow);

    // Show remove button for all phones except the first one
    if (index > 0) {
        container.querySelectorAll('.remove-phone').forEach(btn => {
            btn.style.display = 'block';
        });
    }
});

// Remove phone
document.getElementById('phones-container').addEventListener('click', function(e) {
    if (e.target.closest('.remove-phone')) {
        e.target.closest('.phone-row').remove();

        // Hide remove button if only one phone left
        const phoneRows = this.querySelectorAll('.phone-row');
        if (phoneRows.length === 1) {
            phoneRows[0].querySelector('.remove-phone').style.display = 'none';
        }
    }
});

// Contact person management
document.getElementById('add-contact-person').addEventListener('click', function() {
    const container = document.getElementById('contact-persons-container');
    const index = container.querySelectorAll('.contact-person-row').length;
    
    const newContactPerson = document.createElement('div');
    newContactPerson.className = 'contact-person-row mb-3';
    newContactPerson.innerHTML = `
        <div class="row">
            <div class="col-md-5">
                <input type="text" class="form-control mb-2" name="contact_persons[${index}][name]" 
                       placeholder="Contact person name">
            </div>
            <div class="col-md-5">
                <input type="text" class="form-control mb-2" name="contact_persons[${index}][phone]" 
                       placeholder="Phone number">
            </div>
            <div class="col-md-2">
                <button class="btn btn-outline-danger remove-contact-person" type="button">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    `;
    
    container.appendChild(newContactPerson);
    
    // Show remove button for all contact persons except the first one
    if (index > 0) {
        container.querySelectorAll('.remove-contact-person').forEach(btn => {
            btn.style.display = 'block';
        });
    }
});

// Remove contact person
document.getElementById('contact-persons-container').addEventListener('click', function(e) {
    if (e.target.closest('.remove-contact-person')) {
        e.target.closest('.contact-person-row').remove();
        
        // Hide remove button if only one contact person left
        const contactPersons = this.querySelectorAll('.contact-person-row');
        if (contactPersons.length === 1) {
            contactPersons[0].querySelector('.remove-contact-person').style.display = 'none';
        }
    }
});
</script>
@endsection