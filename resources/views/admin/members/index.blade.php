@extends('layouts.admin')

@section('title', 'Member Management')

@push('styles')
<style>
.bulk-actions {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: #fff;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px 20px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    z-index: 1050;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.bulk-actions.show {
    opacity: 1;
    visibility: visible;
}

.member-avatar {
    position: relative;
}

.member-avatar .bg-primary {
    background: linear-gradient(135deg, #007bff, #0056b3) !important;
}

.stats-card {
    background: #fff;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
}

.stats-card .stats-icon {
    width: 50px;
    height: 50px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: #fff;
    margin-bottom: 15px;
}

.stats-card-primary .stats-icon { background: linear-gradient(135deg, #007bff, #0056b3); }
.stats-card-success .stats-icon { background: linear-gradient(135deg, #28a745, #1e7e34); }
.stats-card-warning .stats-icon { background: linear-gradient(135deg, #ffc107, #e0a800); }
.stats-card-info .stats-icon { background: linear-gradient(135deg, #17a2b8, #138496); }

.enhanced-filters {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    margin-bottom: 20px;
}

.filter-content {
    padding: 20px;
}

.admin-table-responsive {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.admin-table {
    margin-bottom: 0;
}

.admin-table thead th {
    background: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
}

.admin-table tbody tr:hover {
    background-color: #f8f9fa;
}

.btn-enhanced {
    border-radius: 8px;
    font-weight: 500;
    padding: 8px 16px;
}
</style>
@endpush

@section('content')
<!-- Enhanced Header Section -->
<div class="admin-page-header">
    <div class="d-flex justify-content-between align-items-start mb-4">
        <div class="page-header-content">
            <div class="d-flex align-items-center mb-2">
                <div class="page-icon me-3">
                    <i class="fas fa-users"></i>
                </div>
                <div>
                    <h2 class="admin-title mb-1">Member Management</h2>
                    <p class="text-muted mb-0">Manage UIA member companies and their information</p>
                </div>
            </div>
            <div class="page-breadcrumb d-none">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item active">Members</li>
                    </ol>
                </nav>
            </div>
        </div>

        <div class="page-actions">
            <div class="d-flex gap-2">
                <button class="btn btn-outline-secondary" id="toggleFilters">
                    <i class="fas fa-filter me-2"></i>Advanced Filters
                </button>
                <button class="btn btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#bulkActionsModal">
                    <i class="fas fa-tasks me-2"></i>Bulk Actions
                </button>
               
                <button class="btn btn-outline-success" data-bs-toggle="modal" data-bs-target="#importModal">
                    <i class="fas fa-file-import me-2"></i>Import Excel
                </button>
                <a href="{{ route('admin.members.create') }}" class="btn btn-primary btn-enhanced">
                    <i class="fas fa-plus me-2"></i>Add New Member
                </a>
            </div>
        </div>
    </div>

    <!-- Enhanced Stats Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stats-card stats-card-primary">
                <div class="stats-icon">
                    <i class="fas fa-building"></i>
                </div>
                <div class="stats-content">
                    <h3>{{ $members->count() }}</h3>
                    <p>Total Members</p>
                    <small class="stats-change text-success">
                        <i class="fas fa-arrow-up"></i> {{ $members->where('created_at', '>=', now()->subDays(30))->count() }} this month
                    </small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card stats-card-success">
                <div class="stats-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stats-content">
                    <h3>{{ $members->where('is_active', true)->count() }}</h3>
                    <p>Active Members</p>
                    <small class="stats-change">
                        {{ number_format(($members->where('is_active', true)->count() / max($members->count(), 1)) * 100, 1) }}% of total
                    </small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card stats-card-warning">
                <div class="stats-icon">
                    <i class="fas fa-pause-circle"></i>
                </div>
                <div class="stats-content">
                    <h3>{{ $members->where('is_active', false)->count() }}</h3>
                    <p>Inactive Members</p>
                    <small class="stats-change">
                        {{ number_format(($members->where('is_active', false)->count() / max($members->count(), 1)) * 100, 1) }}% of total
                    </small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card stats-card-info">
                <div class="stats-icon">
                    <i class="fas fa-envelope"></i>
                </div>
                <div class="stats-content">
                    <h3>{{ $members->filter(function($member) { return $member->emails && count($member->emails) > 0; })->count() }}</h3>
                    <p>With Email</p>
                    <small class="stats-change">
                        {{ number_format(($members->filter(function($member) { return $member->emails && count($member->emails) > 0; })->count() / max($members->count(), 1)) * 100, 1) }}% contactable
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Filters -->
<div class="admin-card enhanced-filters" id="tableFilters" style="display: none;">
    <div class="card-header-enhanced">
        <div class="d-flex justify-content-between align-items-center">
            <h6 class="mb-0">
                <i class="fas fa-filter me-2"></i>Advanced Filters
            </h6>
            <button class="btn btn-sm btn-outline-secondary" id="resetFilters">
                <i class="fas fa-undo me-1"></i>Reset All
            </button>
        </div>
    </div>

    <div class="filter-content">
        <div class="row g-3">
            <div class="col-md-3">
                <div class="filter-group">
                    <label class="form-label">
                        <i class="fas fa-toggle-on me-1"></i>Status
                    </label>
                    <select class="form-select enhanced-select" id="statusFilter">
                        <option value="">All Status</option>
                        <option value="active">Active Members</option>
                        <option value="inactive">Inactive Members</option>
                    </select>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="filter-group">
                    <label class="form-label">
                        <i class="fas fa-envelope me-1"></i>Contact Info
                    </label>
                    <select class="form-select enhanced-select" id="emailFilter">
                        <option value="">All Members</option>
                        <option value="yes">Has Email</option>
                        <option value="no">Missing Email</option>
                    </select>
                </div>
            </div>
            <div class="col-md-3">
                <div class="filter-group">
                    <label class="form-label">
                        <i class="fas fa-calendar me-1"></i>Date Range
                    </label>
                    <select class="form-select enhanced-select" id="dateFilter">
                        <option value="">All Time</option>
                        <option value="today">Today</option>
                        <option value="week">This Week</option>
                        <option value="month">This Month</option>
                        <option value="year">This Year</option>
                    </select>
                </div>
            </div>
        </div>

        <div class="row g-3 mt-2">
            <div class="col-md-6">
                <div class="filter-group">
                    <label class="form-label">
                        <i class="fas fa-search me-1"></i>Search
                    </label>
                    <input type="text" class="form-control enhanced-input" id="searchFilter"
                           placeholder="Search by company name, contact person, or description">
                </div>
            </div>
            <div class="col-md-3">
                <div class="filter-group">
                    <label class="form-label">
                        <i class="fas fa-map-marker-alt me-1"></i>Location
                    </label>
                    <input type="text" class="form-control enhanced-input" id="locationFilter"
                           placeholder="Search by city or state">
                </div>
            </div>
            <div class="col-md-3">
                <div class="filter-group">
                    <label class="form-label">Actions</label>
                    <div class="d-flex gap-2">
                        <button class="btn btn-primary flex-fill" id="applyFilters">
                            <i class="fas fa-search me-1"></i>Apply
                        </button>
                        <button class="btn btn-outline-secondary" id="clearFilters">
                            <i class="fas fa-times me-1"></i>Clear
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Actions -->
<div class="bulk-actions" id="bulkActions">
    <div class="d-flex justify-content-between align-items-center">
        <span><strong id="selectedCount">0</strong> members selected</span>
        <div class="d-flex gap-2">
            <button class="btn btn-success btn-sm" id="bulkActivate">
                <i class="fas fa-check me-1"></i>Activate
            </button>
            <button class="btn btn-warning btn-sm" id="bulkDeactivate">
                <i class="fas fa-times me-1"></i>Deactivate
            </button>
            <button class="btn btn-danger btn-sm" id="bulkDelete">
                <i class="fas fa-trash me-1"></i>Delete
            </button>
        </div>
    </div>
</div>
@if(session('success'))
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
@endif

@if(session('error'))
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle me-2"></i>{{ session('error') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
@endif

<div class="admin-card">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h5 class="admin-card-title mb-0">All Members</h5>
        <div class="d-flex gap-2">
            <span class="badge bg-primary">{{ $members->count() }} Total</span>
            <span class="badge bg-success">{{ $members->where('is_active', true)->count() }} Active</span>
        </div>
    </div>

    @if($members->count() > 0)
        <div class="admin-table-responsive">
            <table class="admin-table data-table members-table" width="100%">
                <thead>
                    <tr>
                        <th class="no-export">
                            <input type="checkbox" id="selectAll" class="form-check-input">
                        </th>
                        <th>Name</th>
                        <th>Address</th>
                        <th>Contact</th>
                        <th>Status</th>
                        <th>Sort Order</th>
                        <th class="no-export">Toggle</th>
                        <th class="no-export">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($members as $member)
                        <tr data-member-id="{{ $member->id }}">
                            <td>
                                <input type="checkbox" class="form-check-input row-checkbox" value="{{ $member->id }}">
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="member-avatar me-2">
                                        <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center"
                                             style="width: 40px; height: 40px; font-size: 14px; font-weight: 600;">
                                            {{ strtoupper(substr($member->name, 0, 2)) }}
                                        </div>
                                    </div>
                                    <div>
                                        <strong class="d-block">{{ Str::limit($member->name, 25) }}</strong>
                                        @if($member->website)
                                            <small class="text-muted">
                                                <i class="fas fa-globe me-1"></i>
                                                <a href="{{ $member->website }}" target="_blank" class="text-decoration-none">
                                                    {{ Str::limit(str_replace(['http://', 'https://'], '', $member->website), 20) }}
                                                </a>
                                            </small>
                                        @endif
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div>
                                    <span class="d-block">{{ Str::limit($member->address, 30) }}</span>
                                    @if($member->full_address && $member->full_address !== $member->address)
                                        <small class="text-muted" title="{{ $member->full_address }}">
                                            <i class="fas fa-info-circle"></i> Full address available
                                        </small>
                                    @endif
                                </div>
                            </td>
                            
                            <td>
                                <div>
                                    @if($member->emails && count($member->emails) > 0)
                                        <div class="mb-1">
                                            <i class="fas fa-envelope text-primary me-1"></i>
                                            <a href="mailto:{{ $member->emails[0] }}" class="text-decoration-none">
                                                {{ Str::limit($member->emails[0], 20) }}
                                            </a>
                                            @if(count($member->emails) > 1)
                                                <span class="badge bg-secondary ms-1" title="{{ implode(', ', array_slice($member->emails, 1)) }}">
                                                    +{{ count($member->emails) - 1 }}
                                                </span>
                                            @endif
                                        </div>
                                    @endif
                                    @if($member->phones && count($member->phones) > 0)
                                        <div>
                                            <i class="fas fa-phone text-success me-1"></i>
                                            <a href="tel:{{ $member->phones[0] }}" class="text-decoration-none">
                                                {{ $member->phones[0] }}
                                            </a>
                                            @if(count($member->phones) > 1)
                                                <span class="badge bg-secondary ms-1" title="{{ implode(', ', array_slice($member->phones, 1)) }}">
                                                    +{{ count($member->phones) - 1 }}
                                                </span>
                                            @endif
                                        </div>
                                    @endif
                                    @if((!$member->emails || count($member->emails) === 0) && (!$member->phones || count($member->phones) === 0))
                                        <span class="text-muted">No contact info</span>
                                    @endif
                                </div>
                            </td>
                            <td>
                                <span class="badge {{ $member->is_active ? 'bg-success' : 'bg-secondary' }}">
                                    {{ $member->is_active ? 'Active' : 'Inactive' }}
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-dark">{{ $member->sort_order }}</span>
                            </td>
                            <td>
                                <form action="{{ route('admin.members.toggle-active', $member) }}" method="POST" class="d-inline">
                                    @csrf
                                    @method('PATCH')
                                    <button type="submit"
                                            class="btn btn-sm {{ $member->is_active ? 'btn-success' : 'btn-outline-success' }}"
                                            data-bs-toggle="tooltip"
                                            title="{{ $member->is_active ? 'Deactivate' : 'Activate' }} member">
                                        <i class="fas fa-{{ $member->is_active ? 'eye' : 'eye-slash' }}"></i>
                                    </button>
                                </form>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ route('admin.members.show', $member) }}"
                                       class="btn btn-sm btn-outline-info"
                                       data-bs-toggle="tooltip" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('admin.members.edit', $member) }}"
                                       class="btn btn-sm btn-outline-primary"
                                       data-bs-toggle="tooltip" title="Edit Member">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button"
                                            class="btn btn-sm btn-outline-danger"
                                            onclick="confirmDelete('{{ $member->id }}', '{{ $member->name }}')"
                                            data-bs-toggle="tooltip" title="Delete Member">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="9" class="table-empty-state">
                                <i class="fas fa-users"></i>
                                <h5>No Members Found</h5>
                                <p>Start by adding your first member to the system.</p>
                                <a href="{{ route('admin.members.create') }}" class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i>Add First Member
                                </a>
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    @else
        <div class="table-empty-state">
            <i class="fas fa-users"></i>
            <h5>No Members Found</h5>
            <p>Start by adding your first member to the system.</p>
            <a href="{{ route('admin.members.create') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>Add First Member
            </a>
        </div>
    @endif
</div>

<!-- Bulk Actions Modal -->
<div class="modal fade" id="bulkActionsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Bulk Actions</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Select an action to perform on selected members:</p>
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-success" id="modalBulkActivate">
                        <i class="fas fa-check me-2"></i>Activate Selected Members
                    </button>
                    <button type="button" class="btn btn-warning" id="modalBulkDeactivate">
                        <i class="fas fa-times me-2"></i>Deactivate Selected Members
                    </button>
                    <button type="button" class="btn btn-danger" id="modalBulkDelete">
                        <i class="fas fa-trash me-2"></i>Delete Selected Members
                    </button>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>

<!-- Import Modal -->
<div class="modal fade" id="importModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Import Members</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="{{ route('admin.members.import') }}" method="POST" enctype="multipart/form-data">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="importFile" class="form-label">Select Excel File</label>
                        <input type="file" class="form-control" id="importFile" name="file" accept=".xlsx,.xls,.csv" required>
                        <div class="form-text">Supported formats: XLSX, XLS, CSV (Max size: 2MB)</div>
                        <div class="mt-2">
                            <a href="{{ asset('storage/samples/members_sample.csv') }}" download class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-download me-1"></i>Download Sample CSV
                            </a>
                        </div>
                    </div>
                    <div class="alert alert-info">
                        <h6 class="alert-heading">Import Format</h6>
                        <p class="mb-0">The Excel file should have the following columns:</p>
                        <ul class="mb-0">
                            <li>Name</li>
                            <li>Address</li>
                            <li>Full Address</li>
                            <li>Description</li>
                            <li>Email</li>
                            <li>Phone</li>
                            <li>Website</li>
                            <li>Contact Persons (name,phone;name,phone)</li>
                            <li>Is Active (1 or 0)</li>
                            <li>Sort Order</li>
                        </ul>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-file-import me-2"></i>Import
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the member "<span id="deleteItemName"></span>"?</p>
                <p class="text-danger"><small>This action cannot be undone.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" class="d-inline">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
$(document).ready(function() {
    // Initialize DataTable
    const table = $('.members-table').DataTable();

    // Toggle filters
    $('#toggleFilters').click(function() {
        $('#tableFilters').slideToggle();
        $(this).find('i').toggleClass('fa-filter fa-filter-circle-xmark');
    });

    // Select all checkbox
    $('#selectAll').change(function() {
        $('.row-checkbox').prop('checked', this.checked);
        updateBulkActions();
    });

    // Individual checkbox change
    $(document).on('change', '.row-checkbox', function() {
        updateBulkActions();

        // Update select all checkbox
        const totalCheckboxes = $('.row-checkbox').length;
        const checkedCheckboxes = $('.row-checkbox:checked').length;
        $('#selectAll').prop('indeterminate', checkedCheckboxes > 0 && checkedCheckboxes < totalCheckboxes);
        $('#selectAll').prop('checked', checkedCheckboxes === totalCheckboxes);
    });

    // Update bulk actions visibility
    function updateBulkActions() {
        const selectedCount = $('.row-checkbox:checked').length;
        $('#selectedCount').text(selectedCount);

        if (selectedCount > 0) {
            $('#bulkActions').addClass('show');
        } else {
            $('#bulkActions').removeClass('show');
        }
    }

    // Bulk actions from modal
    $('#modalBulkActivate, #modalBulkDeactivate, #modalBulkDelete').click(function() {
        const selectedIds = $('.row-checkbox:checked').map(function() {
            return this.value;
        }).get();

        if (selectedIds.length === 0) {
            alert('Please select at least one member.');
            return;
        }

        const action = $(this).attr('id').replace('modalBulk', '').toLowerCase();

        if (action === 'delete') {
            if (!confirm(`Are you sure you want to delete ${selectedIds.length} members? This action cannot be undone.`)) {
                return;
            }
        }

        bulkAction(action, selectedIds);
        $('#bulkActionsModal').modal('hide');
    });

    // Legacy bulk actions (for backward compatibility)
    $('#bulkActivate').click(function() {
        const selectedIds = $('.row-checkbox:checked').map(function() {
            return this.value;
        }).get();

        if (selectedIds.length > 0) {
            bulkAction('activate', selectedIds);
        }
    });

    $('#bulkDeactivate').click(function() {
        const selectedIds = $('.row-checkbox:checked').map(function() {
            return this.value;
        }).get();

        if (selectedIds.length > 0) {
            bulkAction('deactivate', selectedIds);
        }
    });

    $('#bulkDelete').click(function() {
        const selectedIds = $('.row-checkbox:checked').map(function() {
            return this.value;
        }).get();

        if (selectedIds.length > 0 && confirm(`Are you sure you want to delete ${selectedIds.length} members?`)) {
            bulkAction('delete', selectedIds);
        }
    });

    // Export functionality
    $('#exportBtn').click(function() {
        window.location.href = '{{ route("admin.members.export") }}';
    });

    // Bulk action function
    function bulkAction(action, ids) {
        const formData = new FormData();
        formData.append('_token', '{{ csrf_token() }}');
        formData.append('action', action);
        formData.append('ids', JSON.stringify(ids));

        fetch('{{ route("admin.members.bulk-action") }}', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || 'An error occurred');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while processing the request');
        });
    }

    // Apply filters
    $('#applyFilters').click(function() {
        const statusFilter = $('#statusFilter').val();
        const categoryFilter = $('#categoryFilter').val();
        const emailFilter = $('#emailFilter').val();

        // Apply DataTable column filters
        table.columns().search('');

        if (statusFilter) {
            table.column(5).search(statusFilter === 'active' ? 'Active' : 'Inactive');
        }

       

        if (emailFilter) {
            if (emailFilter === 'yes') {
                table.column(4).search('@', true, false);
            } else if (emailFilter === 'no') {
                table.column(4).search('^((?!@).)*$', true, false);
            }
        }

        table.draw();
    });

    // Clear filters
    $('#clearFilters').click(function() {
        $('#statusFilter, #emailFilter, #dateFilter, #searchFilter, #locationFilter').val('');
        table.columns().search('').draw();
    });
});

function confirmDelete(id, name) {
    document.getElementById('deleteItemName').textContent = name;
    document.getElementById('deleteForm').action = `/admin/members/${id}`;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
@endpush
@endsection