@extends('layouts.admin')

@section('title', 'Edit Member')

@section('content')
<div class="admin-wrapper">
    <div class="admin-main">
        <div class="admin-content">
            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i>{{ session('error') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if($errors->any())
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Whoops!</strong> There were some problems with your input.
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    <ul class="mb-0 mt-2">
                        @foreach($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <div class="admin-card">
                <div class="admin-card-header">
                    <h5 class="admin-card-title mb-0">Member Details</h5>
                </div>
                
                <div class="admin-card-body">
                    <form action="{{ route('admin.members.update', $member) }}" method="POST">
                        @csrf
                        @method('PUT')
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-4">
                                    <label for="name" class="form-label">Company Name *</label>
                                    <input type="text" class="form-control" id="name" name="name" 
                                           value="{{ old('name', $member->name) }}" placeholder="Enter company name" required>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-4">
                                    <label for="address" class="form-label">Address *</label>
                                    <input type="text" class="form-control" id="address" name="address" 
                                           value="{{ old('address', $member->address) }}" placeholder="Enter address" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <label for="full_address" class="form-label">Full Address</label>
                            <textarea class="form-control" id="full_address" name="full_address" rows="3" 
                                      placeholder="Enter full address with line breaks">{{ old('full_address', $member->full_address) }}</textarea>
                        </div>
                        
                        <div class="mb-4">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="3"
                                      placeholder="Enter company description">{{ old('description', $member->description) }}</textarea>
                        </div>


                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-4">
                                    <label class="form-label">Email Addresses</label>
                                    <div id="emails-container">
                                        @if(old('emails') || $member->emails)
                                            @php
                                                $emails = old('emails', $member->emails ?? []);
                                                if (!is_array($emails)) {
                                                    $emails = [];
                                                }
                                            @endphp
                                            @foreach($emails as $index => $email)
                                                <div class="email-row mb-2">
                                                    <div class="input-group">
                                                        <input type="email" class="form-control" name="emails[{{ $index }}]"
                                                               value="{{ $email }}" placeholder="Enter email address">
                                                        <button class="btn btn-outline-danger remove-email" type="button"{{ $index === 0 && count($emails) === 1 ? ' style="display: none;"' : '' }}>
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            @endforeach
                                        @else
                                            <div class="email-row mb-2">
                                                <div class="input-group">
                                                    <input type="email" class="form-control" name="emails[0]"
                                                           placeholder="Enter email address">
                                                    <button class="btn btn-outline-danger remove-email" type="button" style="display: none;">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        @endif
                                    </div>
                                    <button type="button" class="btn btn-outline-primary btn-sm mt-2" id="add-email">
                                        <i class="fas fa-plus me-1"></i>Add Email
                                    </button>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-4">
                                    <label class="form-label">Phone Numbers</label>
                                    <div id="phones-container">
                                        @if(old('phones') || $member->phones)
                                            @php
                                                $phones = old('phones', $member->phones ?? []);
                                                if (!is_array($phones)) {
                                                    $phones = [];
                                                }
                                            @endphp
                                            @foreach($phones as $index => $phone)
                                                <div class="phone-row mb-2">
                                                    <div class="input-group">
                                                        <input type="text" class="form-control" name="phones[{{ $index }}]"
                                                               value="{{ $phone }}" placeholder="Enter phone number">
                                                        <button class="btn btn-outline-danger remove-phone" type="button"{{ $index === 0 && count($phones) === 1 ? ' style="display: none;"' : '' }}>
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            @endforeach
                                        @else
                                            <div class="phone-row mb-2">
                                                <div class="input-group">
                                                    <input type="text" class="form-control" name="phones[0]"
                                                           placeholder="Enter phone number">
                                                    <button class="btn btn-outline-danger remove-phone" type="button" style="display: none;">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        @endif
                                    </div>
                                    <button type="button" class="btn btn-outline-primary btn-sm mt-2" id="add-phone">
                                        <i class="fas fa-plus me-1"></i>Add Phone
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <label for="website" class="form-label">Website</label>
                            <input type="url" class="form-control" id="website" name="website" 
                                   value="{{ old('website', $member->website) }}" placeholder="https://example.com">
                        </div>
                        
                      
                        
                        <div class="mb-4">
                            <label class="form-label">Contact Persons</label>
                            <div id="contact-persons-container">
                                @if(old('contact_persons') || $member->contact_persons)
                                    @php
                                        $contactPersons = old('contact_persons', $member->contact_persons ?? []);
                                        if (!is_array($contactPersons)) {
                                            $contactPersons = [];
                                        }
                                    @endphp
                                    @foreach($contactPersons as $index => $contact)
                                        <div class="contact-person-row mb-3">
                                            <div class="row">
                                                <div class="col-md-5">
                                                    <input type="text" class="form-control mb-2" name="contact_persons[{{ $index }}][name]" 
                                                           value="{{ $contact['name'] ?? '' }}" placeholder="Contact person name">
                                                </div>
                                                <div class="col-md-5">
                                                    <input type="text" class="form-control mb-2" name="contact_persons[{{ $index }}][phone]" 
                                                           value="{{ $contact['phone'] ?? '' }}" placeholder="Phone number">
                                                </div>
                                                <div class="col-md-2">
                                                    <button class="btn btn-outline-danger remove-contact-person" type="button"{{ $index === 0 && count($contactPersons) === 1 ? ' style="display: none;"' : '' }}>
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                    @if(empty($contactPersons))
                                        <div class="contact-person-row mb-3">
                                            <div class="row">
                                                <div class="col-md-5">
                                                    <input type="text" class="form-control mb-2" name="contact_persons[0][name]" placeholder="Contact person name">
                                                </div>
                                                <div class="col-md-5">
                                                    <input type="text" class="form-control mb-2" name="contact_persons[0][phone]" placeholder="Phone number">
                                                </div>
                                                <div class="col-md-2">
                                                    <button class="btn btn-outline-danger remove-contact-person" type="button" style="display: none;">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    @endif
                                @else
                                    <div class="contact-person-row mb-3">
                                        <div class="row">
                                            <div class="col-md-5">
                                                <input type="text" class="form-control mb-2" name="contact_persons[0][name]" placeholder="Contact person name">
                                            </div>
                                            <div class="col-md-5">
                                                <input type="text" class="form-control mb-2" name="contact_persons[0][phone]" placeholder="Phone number">
                                            </div>
                                            <div class="col-md-2">
                                                <button class="btn btn-outline-danger remove-contact-person" type="button" style="display: none;">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                @endif
                            </div>
                            <button type="button" class="btn btn-outline-primary btn-sm mt-2" id="add-contact-person">
                                <i class="fas fa-plus me-1"></i>Add Contact Person
                            </button>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-4">
                                    <label for="sort_order" class="form-label">Sort Order</label>
                                    <input type="number" class="form-control" id="sort_order" name="sort_order" 
                                           value="{{ old('sort_order', $member->sort_order) }}" min="0">
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-4">
                                    <label for="is_active" class="form-label">Status</label>
                                    <select class="form-select" id="is_active" name="is_active">
                                        <option value="1" {{ old('is_active', $member->is_active) ? 'selected' : '' }}>Active</option>
                                        <option value="0" {{ old('is_active', $member->is_active) ? '' : 'selected' }}>Inactive</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{{ route('admin.members.index') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Update Member
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>



// Email management
document.getElementById('add-email').addEventListener('click', function() {
    const container = document.getElementById('emails-container');
    const index = container.querySelectorAll('.email-row').length;

    const newEmailRow = document.createElement('div');
    newEmailRow.className = 'email-row mb-2';
    newEmailRow.innerHTML = `
        <div class="input-group">
            <input type="email" class="form-control" name="emails[${index}]"
                   placeholder="Enter email address">
            <button class="btn btn-outline-danger remove-email" type="button">
                <i class="fas fa-trash"></i>
            </button>
        </div>
    `;

    container.appendChild(newEmailRow);

    // Show remove button for all emails except the first one
    if (index > 0) {
        container.querySelectorAll('.remove-email').forEach(btn => {
            btn.style.display = 'block';
        });
    }
});

// Remove email
document.getElementById('emails-container').addEventListener('click', function(e) {
    if (e.target.closest('.remove-email')) {
        e.target.closest('.email-row').remove();

        // Hide remove button if only one email left
        const emailRows = this.querySelectorAll('.email-row');
        if (emailRows.length === 1) {
            emailRows[0].querySelector('.remove-email').style.display = 'none';
        }
    }
});

// Phone management
document.getElementById('add-phone').addEventListener('click', function() {
    const container = document.getElementById('phones-container');
    const index = container.querySelectorAll('.phone-row').length;

    const newPhoneRow = document.createElement('div');
    newPhoneRow.className = 'phone-row mb-2';
    newPhoneRow.innerHTML = `
        <div class="input-group">
            <input type="text" class="form-control" name="phones[${index}]"
                   placeholder="Enter phone number">
            <button class="btn btn-outline-danger remove-phone" type="button">
                <i class="fas fa-trash"></i>
            </button>
        </div>
    `;

    container.appendChild(newPhoneRow);

    // Show remove button for all phones except the first one
    if (index > 0) {
        container.querySelectorAll('.remove-phone').forEach(btn => {
            btn.style.display = 'block';
        });
    }
});

// Remove phone
document.getElementById('phones-container').addEventListener('click', function(e) {
    if (e.target.closest('.remove-phone')) {
        e.target.closest('.phone-row').remove();

        // Hide remove button if only one phone left
        const phoneRows = this.querySelectorAll('.phone-row');
        if (phoneRows.length === 1) {
            phoneRows[0].querySelector('.remove-phone').style.display = 'none';
        }
    }
});

// Contact person management
document.getElementById('add-contact-person').addEventListener('click', function() {
    const container = document.getElementById('contact-persons-container');
    const contactPersons = container.querySelectorAll('.contact-person-row');
    const index = contactPersons.length;
    
    const newContactPerson = document.createElement('div');
    newContactPerson.className = 'contact-person-row mb-3';
    newContactPerson.innerHTML = `
        <div class="row">
            <div class="col-md-5">
                <input type="text" class="form-control mb-2" name="contact_persons[${index}][name]" 
                       placeholder="Contact person name">
            </div>
            <div class="col-md-5">
                <input type="text" class="form-control mb-2" name="contact_persons[${index}][phone]" 
                       placeholder="Phone number">
            </div>
            <div class="col-md-2">
                <button class="btn btn-outline-danger remove-contact-person" type="button">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    `;
    
    container.appendChild(newContactPerson);
    
    // Show remove button for all contact persons except the first one
    if (index > 0) {
        container.querySelectorAll('.remove-contact-person').forEach(btn => {
            btn.style.display = 'block';
        });
    }
});

// Remove contact person
document.getElementById('contact-persons-container').addEventListener('click', function(e) {
    if (e.target.closest('.remove-contact-person')) {
        e.target.closest('.contact-person-row').remove();
        
        // Hide remove button if only one contact person left
        const contactPersons = this.querySelectorAll('.contact-person-row');
        if (contactPersons.length === 1) {
            contactPersons[0].querySelector('.remove-contact-person').style.display = 'none';
        }
    }
});
</script>
@endsection