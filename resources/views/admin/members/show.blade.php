@extends('layouts.admin')

@section('title', 'Member Details')

@section('content')
<div class="admin-wrapper">
    <div class="admin-main">
        <div class="admin-content">
            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i>{{ session('error') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <div class="row">
                <div class="col-lg-8">
                    <div class="admin-card mb-4">
                        <div class="admin-card-header">
                            <h5 class="admin-card-title mb-0">Company Information</h5>
                        </div>
                        <div class="admin-card-body">
                            <table class="table table-borderless">
                                <tbody>
                                    <tr>
                                        <td><strong>Company Name:</strong></td>
                                        <td>{{ $member->name }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Address:</strong></td>
                                        <td>{{ $member->address }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Full Address:</strong></td>
                                        <td>{!! nl2br(e($member->full_address ?? 'N/A')) !!}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Description:</strong></td>
                                        <td>{{ $member->description ?? 'N/A' }}</td>
                                    </tr>

                                    <tr>
                                        <td><strong>Categories:</strong></td>
                                        <td>
                                            @if($member->categories && count($member->categories) > 0)
                                                @foreach($member->categories as $category)
                                                    <span class="badge bg-primary me-1">{{ $category }}</span>
                                                @endforeach
                                            @else
                                                N/A
                                            @endif
                                        </td>
                                    </tr>

                                    <tr>
                                        <td><strong>Email Addresses:</strong></td>
                                        <td>
                                            @if($member->emails && count($member->emails) > 0)
                                                @foreach($member->emails as $email)
                                                    <div class="mb-1">
                                                        <i class="fas fa-envelope text-primary me-2"></i>
                                                        <a href="mailto:{{ $email }}" class="text-decoration-none">{{ $email }}</a>
                                                    </div>
                                                @endforeach
                                            @else
                                                N/A
                                            @endif
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Phone Numbers:</strong></td>
                                        <td>
                                            @if($member->phones && count($member->phones) > 0)
                                                @foreach($member->phones as $phone)
                                                    <div class="mb-1">
                                                        <i class="fas fa-phone text-success me-2"></i>
                                                        <a href="tel:{{ $phone }}" class="text-decoration-none">{{ $phone }}</a>
                                                    </div>
                                                @endforeach
                                            @else
                                                N/A
                                            @endif
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Website:</strong></td>
                                        <td>
                                            @if($member->website)
                                                <a href="{{ $member->website }}" target="_blank">{{ $member->website }}</a>
                                            @else
                                                N/A
                                            @endif
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Status:</strong></td>
                                        <td>
                                            <span class="badge {{ $member->is_active ? 'bg-success' : 'bg-secondary' }}">
                                                {{ $member->is_active ? 'Active' : 'Inactive' }}
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Sort Order:</strong></td>
                                        <td>{{ $member->sort_order }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Created At:</strong></td>
                                        <td>{{ $member->created_at->format('M j, Y H:i') }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Updated At:</strong></td>
                                        <td>{{ $member->updated_at->format('M j, Y H:i') }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4">
                    <div class="admin-card mb-4">
                        <div class="admin-card-header">
                            <h5 class="admin-card-title mb-0">Contact Persons</h5>
                        </div>
                        <div class="admin-card-body">
                            @if($member->contact_persons)
                                @foreach($member->contact_persons as $contact)
                                    <div class="mb-3 pb-3 border-bottom">
                                        <h6 class="mb-2">{{ $contact['name'] ?? 'N/A' }}</h6>
                                        <p class="mb-1">
                                            <i class="fas fa-phone me-2 text-muted"></i>
                                            {{ $contact['phone'] ?? 'N/A' }}
                                        </p>
                                    </div>
                                @endforeach
                            @else
                                <p class="text-muted">No contact persons available.</p>
                            @endif
                            
                            <div class="d-flex gap-2 mt-4">
                                <a href="{{ route('admin.members.edit', $member) }}" class="btn btn-primary flex-grow-1">
                                    <i class="fas fa-edit me-2"></i>Edit
                                </a>
                                <button type="button" class="btn btn-danger flex-grow-1" 
                                        onclick="confirmDelete('{{ $member->id }}', '{{ $member->name }}')">
                                    <i class="fas fa-trash me-2"></i>Delete
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the member "<span id="deleteItemName"></span>"?</p>
                <p class="text-danger"><small>This action cannot be undone.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" class="d-inline">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(id, name) {
    document.getElementById('deleteItemName').textContent = name;
    document.getElementById('deleteForm').action = `/admin/members/${id}`;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
@endsection