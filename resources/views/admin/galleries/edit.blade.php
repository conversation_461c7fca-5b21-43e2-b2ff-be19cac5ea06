@extends('layouts.admin')

@section('title', 'Edit Gallery Image')
@section('page-title', 'Edit Gallery Image')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <p class="text-muted mb-0">Update gallery image details</p>
    </div>
    
    <div>
        <a href="{{ route('admin.galleries.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>Back to Gallery
        </a>
    </div>
</div>

@if(session('success'))
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
@endif

@if(session('error'))
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle me-2"></i>{{ session('error') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
@endif

@if($errors->any())
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <strong>Whoops!</strong> There were some problems with your input.
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        <ul class="mb-0 mt-2">
            @foreach($errors->all() as $error)
                <li>{{ $error }}</li>
            @endforeach
        </ul>
    </div>
@endif

<div class="admin-card">
    <div class="admin-card-header">
        <h5 class="admin-card-title mb-0">Image Details</h5>
    </div>
    
    <div class="admin-card-body">
        <form action="{{ route('admin.galleries.update', $gallery) }}" method="POST" enctype="multipart/form-data">
            @csrf
            @method('PUT')
            
            <div class="row">
                <div class="col-md-8">
                    <div class="mb-4">
                        <label for="title" class="form-label">Title</label>
                        <input type="text" class="form-control" id="title" name="title"
                               value="{{ old('title', $gallery->title) }}" placeholder="Enter image title">
                        <div class="form-text">Optional title for the gallery image</div>
                    </div>
                    
                    <div class="mb-4">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3"
                                  placeholder="Enter image description">{{ old('description', $gallery->description) }}</textarea>
                        <div class="form-text">Optional description for the gallery image</div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-4">
                                <label for="sort_order" class="form-label">Sort Order</label>
                                <input type="number" class="form-control" id="sort_order" name="sort_order"
                                       value="{{ old('sort_order', $gallery->sort_order) }}" min="0">
                                <div class="form-text">Determines the order of images in the gallery</div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-4">
                                <label for="is_active" class="form-label">Status</label>
                                <select class="form-select" id="is_active" name="is_active">
                                    <option value="1" {{ old('is_active', $gallery->is_active) ? 'selected' : '' }}>Active</option>
                                    <option value="0" {{ old('is_active', $gallery->is_active) ? '' : 'selected' }}>Inactive</option>
                                </select>
                                <div class="form-text">Inactive images won't be displayed in the gallery</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="mb-4">
                        <label for="image" class="form-label">Image</label>
                        <input type="file" class="form-control" id="image" name="image" accept="image/*">
                        <div class="form-text">Upload an image (JPG, PNG, GIF) - Max 2MB</div>
                        <div class="mt-3">
                            @if($gallery->image_path)
                                <p class="text-muted mb-2">Current Image:</p>
                                <img src="{{ asset('storage/' . $gallery->image_path) }}"
                                     alt="{{ $gallery->title ?? 'Gallery Image' }}"
                                     class="img-fluid border rounded" style="max-height: 200px;">
                            @else
                                <p class="text-muted">No image uploaded yet.</p>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="d-flex justify-content-between">
                <a href="{{ route('admin.galleries.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Cancel
                </a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-2"></i>Update Image
                </button>
            </div>
        </form>
    </div>
</div>
@endsection