@extends('layouts.admin')

@section('title', 'Gallery Image Details')
@section('page-title', 'Gallery Image Details')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <p class="text-muted mb-0">View detailed information about the gallery image</p>
    </div>
    
    <div>
        <a href="{{ route('admin.galleries.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>Back to Gallery
        </a>
    </div>
</div>

@if(session('success'))
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
@endif

@if(session('error'))
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle me-2"></i>{{ session('error') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
@endif

<div class="row">
    <div class="col-lg-8">
        <div class="admin-card mb-4">
            <div class="admin-card-header">
                <h5 class="admin-card-title mb-0">Image Preview</h5>
            </div>
            <div class="admin-card-body text-center">
                @if($gallery->image_path)
                    <img src="{{ asset('storage/' . $gallery->image_path) }}"
                         alt="{{ $gallery->title ?? 'Gallery Image' }}"
                         class="img-fluid rounded border" style="max-height: 400px;">
                @else
                    <div class="bg-light d-flex align-items-center justify-content-center"
                         style="height: 400px;">
                        <i class="fas fa-image fa-3x text-muted"></i>
                    </div>
                @endif
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="admin-card mb-4">
            <div class="admin-card-header">
                <h5 class="admin-card-title mb-0">Image Details</h5>
            </div>
            <div class="admin-card-body">
                <table class="table table-borderless">
                    <tbody>
                        <tr>
                            <td><strong>Title:</strong></td>
                            <td>{{ $gallery->title ?? 'N/A' }}</td>
                        </tr>
                        <tr>
                            <td><strong>Description:</strong></td>
                            <td>{{ $gallery->description ?? 'N/A' }}</td>
                        </tr>
                        <tr>
                            <td><strong>Status:</strong></td>
                            <td>
                                <span class="badge {{ $gallery->is_active ? 'bg-success' : 'bg-secondary' }}">
                                    {{ $gallery->is_active ? 'Active' : 'Inactive' }}
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>Sort Order:</strong></td>
                            <td>{{ $gallery->sort_order }}</td>
                        </tr>
                        <tr>
                            <td><strong>Created At:</strong></td>
                            <td>{{ $gallery->created_at->format('M j, Y H:i') }}</td>
                        </tr>
                        <tr>
                            <td><strong>Updated At:</strong></td>
                            <td>{{ $gallery->updated_at->format('M j, Y H:i') }}</td>
                        </tr>
                    </tbody>
                </table>
                
                <div class="d-flex gap-2">
                    <a href="{{ route('admin.galleries.edit', $gallery) }}" class="btn btn-primary flex-grow-1">
                        <i class="fas fa-edit me-2"></i>Edit
                    </a>
                    <button type="button" class="btn btn-danger flex-grow-1"
                            onclick="confirmDelete('{{ $gallery->id }}', '{{ $gallery->title ?? 'Gallery Image' }}')">
                        <i class="fas fa-trash me-2"></i>Delete
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the gallery image "<span id="deleteItemName"></span>"?</p>
                <p class="text-danger"><small>This action cannot be undone.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" class="d-inline">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(id, name) {
    document.getElementById('deleteItemName').textContent = name;
    document.getElementById('deleteForm').action = `/admin/galleries/${id}`;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
@endsection