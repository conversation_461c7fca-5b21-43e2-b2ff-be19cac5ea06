@extends('layouts.admin')

@section('title', 'Gallery Management')
@section('page-title', 'Gallery Management')

@section('content')
<div class="admin-wrapper">
    <div class="admin-main">
        <div class="admin-content">
            <!-- Enhanced Header Section -->
            <div class="d-flex justify-content-between align-items-center mb-5">
                <div>
                    <h1 class="admin-title mb-2 fw-bold text-dark">
                        <i class="fas fa-images me-3 text-primary"></i>Gallery Management
                    </h1>
                    <p class="text-muted mb-0 fs-6">Upload, organize and manage your image gallery</p>
                </div>

                <div class="d-flex gap-2">
                   
                    
                    <a href="{{ route('admin.galleries.create') }}" class="btn btn-primary btn-lg shadow-sm">
                        <i class="fas fa-plus me-2"></i>Add New Image
                    </a>
                </div>
            </div>

            <!-- Enhanced Alert Messages -->
            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show border-0 shadow-sm mb-4" role="alert">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-check-circle me-3 fs-5"></i>
                        <div>
                            <strong>Success!</strong> {{ session('success') }}
                        </div>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-danger alert-dismissible fade show border-0 shadow-sm mb-4" role="alert">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-exclamation-circle me-3 fs-5"></i>
                        <div>
                            <strong>Error!</strong> {{ session('error') }}
                        </div>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <!-- Enhanced Card Design -->
            <div class="admin-card shadow-sm border-0">
                <div class="d-flex justify-content-between align-items-center mb-4 pb-3 border-bottom">
                    <div>
                        <h4 class="admin-card-title mb-1 fw-semibold text-dark">
                            <i class="fas fa-photo-video me-2 text-primary"></i>Gallery Collection
                        </h4>
                        <p class="text-muted mb-0 small">Manage your image gallery and display settings</p>
                    </div>
                </div>

                <!-- Enhanced Table with Modern Design -->
                <div class="admin-table-responsive rounded-3 overflow-hidden shadow-sm">
                    <table class="admin-table mb-0" width="100%">
                        <thead class="bg-gradient-primary">
                            <tr>
                                <th class="border-0 text-dark fw-semibold">
                                    <i class="fas fa-image me-2"></i>Preview
                                </th>
                                <th class="border-0 text-dark fw-semibold">
                                    <i class="fas fa-heading me-2"></i>Title & Details
                                </th>
                                <th class="border-0 text-dark fw-semibold">
                                    <i class="fas fa-align-left me-2"></i>Description
                                </th>
                                <th class="border-0 text-dark fw-semibold">
                                    <i class="fas fa-circle-dot me-2"></i>Status
                                </th>
                                <th class="border-0 text-dark fw-semibold">
                                    <i class="fas fa-sort-numeric-up me-2"></i>Order
                                </th>
                                <th class="border-0 text-dark fw-semibold">
                                    <i class="fas fa-calendar me-2"></i>Created
                                </th>
                                <th class="border-0 text-dark fw-semibold text-center">
                                    <i class="fas fa-eye me-2"></i>Active
                                </th>
                                <th class="border-0 text-dark fw-semibold text-center">
                                    <i class="fas fa-cogs me-2"></i>Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($galleries as $gallery)
                                <tr class="border-bottom">
                                    <td class="align-middle">
                                        @if($gallery->image_path)
                                            <div class="position-relative">
                                                <img src="{{ asset('storage/' . $gallery->image_path) }}"
                                                     alt="{{ $gallery->title ?? 'Gallery Image' }}"
                                                     class="rounded-3 shadow-sm border"
                                                     style="width: 100px; height: 80px; object-fit: cover; cursor: pointer; transition: transform 0.2s ease;"
                                                     onclick="showImagePreview('{{ asset('storage/' . $gallery->image_path) }}', '{{ $gallery->title ?? 'Gallery Image' }}')"
                                                     onmouseover="this.style.transform='scale(1.05)'"
                                                     onmouseout="this.style.transform='scale(1)'">
                                            </div>
                                        @else
                                            <div class="bg-light d-flex align-items-center justify-content-center border rounded-3 shadow-sm"
                                                 style="width: 100px; height: 80px;">
                                                <i class="fas fa-image text-muted fs-3"></i>
                                            </div>
                                        @endif
                                    </td>
                                    <td class="align-middle">
                                        <div class="d-flex flex-column">
                                            <h6 class="mb-1 fw-semibold text-dark">
                                                {{ $gallery->title ? Str::limit($gallery->title, 35) : 'Untitled Image' }}
                                            </h6>
                                            <div class="d-flex align-items-center text-info small">
                                                <i class="fas fa-calendar-alt me-2"></i>
                                                <span>Added {{ $gallery->created_at->format('M j, Y') }}</span>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="align-middle">
                                        @if($gallery->description)
                                            <p class="text-muted mb-0 small">{{ Str::limit($gallery->description, 60) }}</p>
                                        @else
                                            <div class="d-flex align-items-center text-muted">
                                                <i class="fas fa-minus-circle me-2"></i>
                                                <span class="small">No description</span>
                                            </div>
                                        @endif
                                    </td>
                                    <td class="align-middle">
                                        <span class="badge {{ $gallery->is_active ? 'bg-success' : 'bg-secondary' }} px-3 py-2 rounded-pill fw-semibold shadow-sm">
                                            <i class="fas fa-circle me-1" style="font-size: 0.6rem;"></i>
                                            {{ $gallery->is_active ? 'Active' : 'Inactive' }}
                                        </span>
                                    </td>
                                    <td class="align-middle">
                                        <div class="d-flex align-items-center">
                                            <div class="bg-primary-subtle rounded-circle p-2 me-2">
                                                <i class="fas fa-sort-numeric-up text-primary"></i>
                                            </div>
                                            <span class="fw-semibold">{{ $gallery->sort_order }}</span>
                                        </div>
                                    </td>
                                    <td class="align-middle">
                                        <div class="d-flex flex-column">
                                            <span class="fw-semibold text-dark">{{ $gallery->created_at->format('M j, Y') }}</span>
                                            <small class="text-muted">{{ $gallery->created_at->format('g:i A') }}</small>
                                        </div>
                                    </td>
                                    <td class="align-middle text-center">
                                        <form action="{{ route('admin.galleries.toggle-active', $gallery) }}" method="POST" class="d-inline">
                                            @csrf
                                            @method('PATCH')
                                            <button type="submit"
                                                    class="btn btn-sm {{ $gallery->is_active ? 'btn-success shadow-sm' : 'btn-outline-success' }} rounded-pill"
                                                    data-bs-toggle="tooltip"
                                                    title="{{ $gallery->is_active ? 'Deactivate image' : 'Activate image' }}">
                                                <i class="fas fa-{{ $gallery->is_active ? 'eye' : 'eye-slash' }}"></i>
                                            </button>
                                        </form>
                                    </td>
                                    <td class="align-middle text-center">
                                        <div class="btn-group shadow-sm" role="group">
                                            <a href="{{ route('admin.galleries.show', $gallery) }}"
                                               class="btn btn-sm btn-outline-info rounded-start-pill"
                                               data-bs-toggle="tooltip" title="View Image">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.galleries.edit', $gallery) }}"
                                               class="btn btn-sm btn-outline-primary"
                                               data-bs-toggle="tooltip" title="Edit Image">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button"
                                                    class="btn btn-sm btn-outline-danger rounded-end-pill"
                                                    onclick="confirmDelete('{{ $gallery->id }}', '{{ $gallery->title ?? 'Gallery Image' }}')"
                                                    data-bs-toggle="tooltip" title="Delete Image">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="8" class="text-center py-5">
                                        <div class="d-flex flex-column align-items-center justify-content-center py-4">
                                            <div class="bg-light rounded-circle p-4 mb-4">
                                                <i class="fas fa-images fa-3x text-muted"></i>
                                            </div>
                                            <h5 class="text-muted mb-2">No Gallery Images Found</h5>
                                            <p class="text-muted mb-4">Start building your image gallery to showcase your content</p>
                                            <a href="{{ route('admin.galleries.create') }}" class="btn btn-primary btn-lg shadow-sm">
                                                <i class="fas fa-plus me-2"></i>Add Your First Image
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow-lg">
            <div class="modal-header bg-danger text-white border-0">
                <h5 class="modal-title fw-bold" id="deleteModalLabel">
                    <i class="fas fa-exclamation-triangle me-2"></i>Confirm Deletion
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-4">
                <div class="d-flex align-items-center mb-3">
                    <div class="bg-danger-subtle rounded-circle p-3 me-3">
                        <i class="fas fa-trash text-danger fs-4"></i>
                    </div>
                    <div>
                        <h6 class="mb-1">Delete Gallery Image</h6>
                        <p class="text-muted mb-0 small">This action cannot be undone</p>
                    </div>
                </div>
                <p class="mb-2">Are you sure you want to delete the gallery image:</p>
                <p class="fw-bold text-dark">"<span id="deleteItemName"></span>"</p>
                <div class="alert alert-warning border-0 bg-warning-subtle">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <small>This will permanently remove the image and all associated data.</small>
                </div>
            </div>
            <div class="modal-footer border-0 bg-light">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>Cancel
                </button>
                <form id="deleteForm" method="POST" class="d-inline">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-2"></i>Delete Image
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(id, name) {
    document.getElementById('deleteItemName').textContent = name;
    document.getElementById('deleteForm').action = `/admin/galleries/${id}`;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

function showImagePreview(imageSrc, imageTitle) {
    // Create a modal for image preview
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content border-0 shadow-lg">
                <div class="modal-header border-0">
                    <h5 class="modal-title fw-bold">${imageTitle}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body p-0">
                    <img src="${imageSrc}" alt="${imageTitle}" class="img-fluid w-100" style="max-height: 70vh; object-fit: contain;">
                </div>
            </div>
        </div>
    `;
    document.body.appendChild(modal);
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();
    modal.addEventListener('hidden.bs.modal', () => modal.remove());
}

// Initialize tooltips
document.addEventListener('DOMContentLoaded', function() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
@endsection