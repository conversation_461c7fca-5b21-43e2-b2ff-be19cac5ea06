@extends('layouts.admin')

@section('title', 'View Office Bearer')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2 class="admin-title mb-0">Office Bearer Details</h2>
    <a href="{{ route('admin.office-bearers.index') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i>Back to Office Bearers
    </a>
</div>

@if(session('success'))
    <div class="admin-alert admin-alert-success">
        <i class="fas fa-check-circle me-2"></i>
        {{ session('success') }}
    </div>
@endif

<div class="row">
    <div class="col-lg-8">
        <div class="admin-card">
            <div class="d-flex justify-content-between align-items-start mb-4">
                <div>
                    <h5 class="admin-card-title mb-2">{{ $officeBearer->title }}</h5>
                    <p class="text-muted mb-0">Year: {{ $officeBearer->year }}</p>
                </div>
                <div>
                    @if($officeBearer->is_active)
                        <span class="badge bg-success">Active</span>
                    @else
                        <span class="badge bg-secondary">Inactive</span>
                    @endif
                </div>
            </div>
            
            <div class="mb-4">
                <label class="form-label text-muted small mb-1">Description</label>
                <p class="mb-0">{{ $officeBearer->description ?? 'No description provided.' }}</p>
            </div>
            
            <div class="d-flex gap-2">
                <a href="{{ route('admin.office-bearers.edit', $officeBearer) }}" class="btn btn-warning">
                    <i class="fas fa-edit me-2"></i>Edit
                </a>
                
                <form action="{{ route('admin.office-bearers.toggle-active', $officeBearer) }}" method="POST" class="d-inline">
                    @csrf
                    @method('PATCH')
                    <button type="submit" class="btn {{ $officeBearer->is_active ? 'btn-secondary' : 'btn-success' }}">
                        <i class="fas fa-{{ $officeBearer->is_active ? 'times' : 'check' }} me-2"></i>
                        {{ $officeBearer->is_active ? 'Deactivate' : 'Activate' }}
                    </button>
                </form>
                
                <button type="button" class="btn btn-danger" onclick="confirmDelete({{ $officeBearer->id }})">
                    <i class="fas fa-trash me-2"></i>Delete
                </button>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="admin-card">
            <h5 class="admin-card-title mb-4">Document Information</h5>
            
            @if($officeBearer->pdf_path)
                <div class="text-center mb-4">
                    <i class="fas fa-file-pdf fa-4x text-danger mb-3"></i>
                    <h6 class="mb-3">PDF Document</h6>
                    <a href="{{ $officeBearer->pdf_url }}" target="_blank" class="btn btn-primary">
                        <i class="fas fa-download me-2"></i>Download PDF
                    </a>
                </div>
            @else
                <div class="text-center mb-4">
                    <i class="fas fa-file fa-4x text-muted mb-3"></i>
                    <h6 class="mb-3">No PDF Document</h6>
                    <p class="text-muted">No PDF document has been uploaded for this office bearer.</p>
                </div>
            @endif
            
            <div class="mb-3">
                <label class="form-label text-muted small mb-1">Created</label>
                <div>{{ $officeBearer->created_at->format('F j, Y \a\t g:i A') }}</div>
            </div>
            
            <div class="mb-0">
                <label class="form-label text-muted small mb-1">Last Updated</label>
                <div>{{ $officeBearer->updated_at->format('F j, Y \a\t g:i A') }}</div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this office bearer record? This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form action="{{ route('admin.office-bearers.destroy', $officeBearer) }}" method="POST">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(officeBearerId) {
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}
</script>
@endsection
