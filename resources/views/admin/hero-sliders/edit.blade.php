@extends('layouts.admin')

@section('title', 'Edit Hero Slider')

@section('content')

            @if($errors->any())
                <div class="admin-alert admin-alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <strong>Please fix the following errors:</strong>
                    <ul class="mb-0 mt-2">
                        @foreach($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif
            
            <div class="admin-card">
                <h5 class="admin-card-title">Edit Hero Slider Information</h5>
                
                <form action="{{ route('admin.hero-sliders.update', $heroSlider) }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    @method('PUT')
                    
                    <div class="row">
                        <div class="col-md-8">
                            <!-- Basic Information -->
                            <div class="mb-3">
                                <label for="title" class="form-label">Title <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                       id="title" name="title" value="{{ old('title', $heroSlider->title) }}" required>
                                @error('title')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="mb-3">
                                <label for="subtitle" class="form-label">Subtitle</label>
                                <input type="text" class="form-control @error('subtitle') is-invalid @enderror" 
                                       id="subtitle" name="subtitle" value="{{ old('subtitle', $heroSlider->subtitle) }}">
                                @error('subtitle')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="mb-3">
                                <label for="description" class="form-label">Description <span class="text-danger">*</span></label>
                                <textarea class="form-control @error('description') is-invalid @enderror" 
                                          id="description" name="description" rows="4" required>{{ old('description', $heroSlider->description) }}</textarea>
                                @error('description')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <!-- Badge Information -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="badge_text" class="form-label">Badge Text</label>
                                        <input type="text" class="form-control @error('badge_text') is-invalid @enderror" 
                                               id="badge_text" name="badge_text" value="{{ old('badge_text', $heroSlider->badge_text) }}">
                                        @error('badge_text')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="badge_icon" class="form-label">Badge Icon (FontAwesome class)</label>
                                        <input type="text" class="form-control @error('badge_icon') is-invalid @enderror" 
                                               id="badge_icon" name="badge_icon" value="{{ old('badge_icon', $heroSlider->badge_icon) }}" 
                                               placeholder="e.g., fas fa-star">
                                        @error('badge_icon')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Button Information -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="primary_button_text" class="form-label">Primary Button Text</label>
                                        <input type="text" class="form-control @error('primary_button_text') is-invalid @enderror" 
                                               id="primary_button_text" name="primary_button_text" value="{{ old('primary_button_text', $heroSlider->primary_button_text) }}">
                                        @error('primary_button_text')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="primary_button_link" class="form-label">Primary Button Link</label>
                                        <input type="text" class="form-control @error('primary_button_link') is-invalid @enderror" 
                                               id="primary_button_link" name="primary_button_link" value="{{ old('primary_button_link', $heroSlider->primary_button_link) }}">
                                        @error('primary_button_link')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="secondary_button_text" class="form-label">Secondary Button Text</label>
                                        <input type="text" class="form-control @error('secondary_button_text') is-invalid @enderror" 
                                               id="secondary_button_text" name="secondary_button_text" value="{{ old('secondary_button_text', $heroSlider->secondary_button_text) }}">
                                        @error('secondary_button_text')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="secondary_button_link" class="form-label">Secondary Button Link</label>
                                        <input type="text" class="form-control @error('secondary_button_link') is-invalid @enderror" 
                                               id="secondary_button_link" name="secondary_button_link" value="{{ old('secondary_button_link', $heroSlider->secondary_button_link) }}">
                                        @error('secondary_button_link')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <!-- Current Image -->
                            <div class="mb-3">
                                <label class="form-label">Current Image</label>
                                <div>
                                    <img src="{{ $heroSlider->image_url }}" alt="{{ $heroSlider->title }}" 
                                         class="img-fluid rounded" style="max-height: 200px;">
                                </div>
                            </div>
                            
                            <!-- Image Upload -->
                            <div class="mb-3">
                                <label for="image" class="form-label">New Background Image</label>
                                <input type="file" class="form-control @error('image') is-invalid @enderror" 
                                       id="image" name="image" accept="image/*">
                                <div class="form-text">Leave empty to keep current image. Recommended size: 1920x1080px. Max size: 2MB</div>
                                @error('image')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <!-- Preview -->
                            <div class="mb-3">
                                <img id="imagePreview" src="#" alt="Preview" class="img-fluid rounded" style="display: none; max-height: 200px;">
                            </div>
                            
                            <!-- Settings -->
                            <div class="mb-3">
                                <label for="sort_order" class="form-label">Sort Order <span class="text-danger">*</span></label>
                                <input type="number" class="form-control @error('sort_order') is-invalid @enderror" 
                                       id="sort_order" name="sort_order" value="{{ old('sort_order', $heroSlider->sort_order) }}" min="0" required>
                                @error('sort_order')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" 
                                           {{ old('is_active', $heroSlider->is_active) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="is_active">
                                        Active
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-end gap-2">
                        <a href="{{ route('admin.hero-sliders.index') }}" class="btn btn-secondary">Cancel</a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Update Slider
                        </button>
                    </div>
                </form>
            </div>

<!-- Logout Form -->
<form id="logout-form" action="{{ route('admin.logout') }}" method="POST" class="d-none">
    @csrf
</form>

<script>
document.getElementById('image').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const preview = document.getElementById('imagePreview');
            preview.src = e.target.result;
            preview.style.display = 'block';
        };
        reader.readAsDataURL(file);
    }
});
</script>
@endsection
