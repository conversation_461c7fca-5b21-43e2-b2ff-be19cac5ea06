@extends('layouts.admin')

@section('title', 'Hero Sliders Management')

@section('content')
<!-- Enhanced Header Section -->
<div class="admin-page-header">
    <div class="d-flex justify-content-between align-items-start mb-4">
        <div class="page-header-content">
            <div class="d-flex align-items-center mb-2">
                <div class="page-icon me-3">
                    <i class="fas fa-images"></i>
                </div>
                <div>
                    <h2 class="admin-title mb-1">Hero Sliders Management</h2>
                    <p class="text-muted mb-0">Create and manage stunning hero slider images for your website</p>
                </div>
            </div>
            <div class="page-breadcrumb d-none">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item active">Hero Sliders</li>
                    </ol>
                </nav>
            </div>
        </div>

        <div class="page-actions d-none">
            <div class="d-flex gap-2">
                <button class="btn btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#previewModal">
                    <i class="fas fa-eye me-2"></i>Preview Sliders
                </button>
                <a href="{{ route('admin.hero-sliders.create') }}" class="btn btn-primary btn-enhanced">
                    <i class="fas fa-plus me-2"></i>Add New Slider
                </a>
            </div>
        </div>
    </div>

    <!-- Enhanced Stats Cards -->
    
</div>

@if(session('success'))
    <div class="admin-alert admin-alert-success">
        <i class="fas fa-check-circle me-2"></i>
        {{ session('success') }}
    </div>
@endif

@if(session('error'))
    <div class="admin-alert admin-alert-danger">
        <i class="fas fa-exclamation-circle me-2"></i>
        {{ session('error') }}
    </div>
@endif

<div class="admin-card enhanced-card">
    <div class="card-header-enhanced">
        <div class="d-flex justify-content-between align-items-center">
            <div class="card-title-section">
                <h5 class="admin-card-title mb-1">
                    <i class="fas fa-list me-2"></i>Hero Sliders Collection
                </h5>
                <p class="card-subtitle">Manage your website's hero slider images and content</p>
            </div>
            <div class="card-actions">
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-sm btn-outline-secondary" id="gridViewBtn">
                        <i class="fas fa-th-large"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary active" id="listViewBtn">
                        <i class="fas fa-list"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    @if($sliders->count() > 0)
        <!-- List View -->
        <div id="listView" class="view-container">
            <div class="admin-table-responsive enhanced-table">
                <table class="admin-table">
                    <thead>
                        <tr>
                            <th class="sortable" data-sort="image">
                                <i class="fas fa-image me-2"></i>Preview
                            </th>
                            <th class="sortable" data-sort="title">
                                <i class="fas fa-heading me-2"></i>Content
                            </th>
                            <th class="sortable" data-sort="order">
                                <i class="fas fa-sort-numeric-up me-2"></i>Order
                            </th>
                            <th class="sortable" data-sort="status">
                                <i class="fas fa-toggle-on me-2"></i>Status
                            </th>
                            <th class="sortable" data-sort="created">
                                <i class="fas fa-calendar me-2"></i>Created
                            </th>
                            <th class="text-center">
                                <i class="fas fa-cogs me-2"></i>Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($sliders as $slider)
                        <tr class="table-row-enhanced">
                            <td>
                                <div class="image-preview-container">
                                    <img src="{{ $slider->image_url }}" alt="{{ $slider->title }}"
                                         class="slider-preview-img"
                                         data-bs-toggle="modal"
                                         data-bs-target="#imageModal{{ $slider->id }}" height="100px;">
                                    <div class="image-overlay">
                                        <i class="fas fa-search-plus"></i>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="content-preview">
                                    <h6 class="content-title">{{ $slider->title }}</h6>
                                    @if($slider->subtitle)
                                        <p class="content-subtitle">{{ Str::limit($slider->subtitle, 40) }}</p>
                                    @endif
                                    @if($slider->badge_text)
                                        <span class="content-badge">
                                            <i class="{{ $slider->badge_icon_formatted ?? 'fas fa-tag' }} me-1"></i>
                                            {{ $slider->badge_text }}
                                        </span>
                                    @endif
                                    <div class="content-meta">
                                        <small class="text-muted">
                                            {{ Str::limit(strip_tags($slider->description), 60) }}
                                        </small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="order-badge-container">
                                    <span class="order-badge">{{ $slider->sort_order }}</span>
                                </div>
                            </td>
                            <td>
                                <div class="status-container">
                                    @if($slider->is_active)
                                        <span class="status-badge status-active">
                                            <i class="fas fa-check-circle me-1"></i>Active
                                        </span>
                                    @else
                                        <span class="status-badge status-inactive">
                                            <i class="fas fa-pause-circle me-1"></i>Inactive
                                        </span>
                                    @endif
                                </div>
                            </td>
                            <td>
                                <div class="date-container">
                                    <span class="date-primary">{{ $slider->created_at->format('M d, Y') }}</span>
                                    <small class="date-secondary">{{ $slider->created_at->format('g:i A') }}</small>
                                </div>
                            </td>
                            <td>
                                <div class="action-buttons">
                                    <a href="{{ route('admin.hero-sliders.show', $slider) }}"
                                       class="btn btn-sm btn-action btn-view"
                                       title="View Details"
                                       data-bs-toggle="tooltip">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('admin.hero-sliders.edit', $slider) }}"
                                       class="btn btn-sm btn-action btn-edit"
                                       title="Edit Slider"
                                       data-bs-toggle="tooltip">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button"
                                            class="btn btn-sm btn-action btn-delete"
                                            onclick="confirmDelete({{ $slider->id }})"
                                            title="Delete Slider"
                                            data-bs-toggle="tooltip">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Grid View -->
        <div id="gridView" class="view-container d-none">
            <div class="row">
                @foreach($sliders as $slider)
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="slider-card">
                        <div class="slider-card-image">
                            <img src="{{ $slider->image_url }}" alt="{{ $slider->title }}" class="card-img">
                            <div class="slider-card-overlay">
                                <div class="overlay-content">
                                    @if($slider->is_active)
                                        <span class="overlay-status status-active">
                                            <i class="fas fa-check-circle"></i> Active
                                        </span>
                                    @else
                                        <span class="overlay-status status-inactive">
                                            <i class="fas fa-pause-circle"></i> Inactive
                                        </span>
                                    @endif
                                    <div class="overlay-actions">
                                        <a href="{{ route('admin.hero-sliders.show', $slider) }}"
                                           class="btn btn-sm btn-light" title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('admin.hero-sliders.edit', $slider) }}"
                                           class="btn btn-sm btn-primary" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-danger"
                                                onclick="confirmDelete({{ $slider->id }})" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="slider-order-badge">{{ $slider->sort_order }}</div>
                        </div>
                        <div class="slider-card-body">
                            <h6 class="slider-card-title">{{ $slider->title }}</h6>
                            @if($slider->subtitle)
                                <p class="slider-card-subtitle">{{ Str::limit($slider->subtitle, 50) }}</p>
                            @endif
                            @if($slider->badge_text)
                                <span class="slider-badge">
                                    <i class="{{ $slider->badge_icon_formatted ?? 'fas fa-tag' }} me-1"></i>
                                    {{ $slider->badge_text }}
                                </span>
                            @endif
                            <div class="slider-card-meta">
                                <small class="text-muted">Created {{ $slider->created_at->format('M d, Y') }}</small>
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    @else
        <div class="empty-state">
            <div class="empty-state-content">
                <div class="empty-state-icon">
                    <i class="fas fa-images"></i>
                </div>
                <h4 class="empty-state-title">No Hero Sliders Found</h4>
                <p class="empty-state-description">
                    Create stunning hero sliders to showcase your content and engage your visitors.
                    Hero sliders are perfect for highlighting important announcements, events, or featured content.
                </p>
                <div class="empty-state-actions">
                    <a href="{{ route('admin.hero-sliders.create') }}" class="btn btn-primary btn-enhanced">
                        <i class="fas fa-plus me-2"></i>Create Your First Slider
                    </a>
                    <button class="btn btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#helpModal">
                        <i class="fas fa-question-circle me-2"></i>Learn More
                    </button>
                </div>
            </div>
        </div>
    @endif
</div>

<!-- Image Preview Modals -->
@foreach($sliders as $slider)
<div class="modal fade" id="imageModal{{ $slider->id }}" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ $slider->title }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body p-0">
                <img src="{{ $slider->image_url }}" alt="{{ $slider->title }}" class="img-fluid w-100">
                <div class="p-3">
                    @if($slider->subtitle)
                        <p class="text-muted mb-2">{{ $slider->subtitle }}</p>
                    @endif
                    <p class="mb-0">{{ $slider->description }}</p>
                </div>
            </div>
        </div>
    </div>
</div>
@endforeach

<!-- Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-eye me-2"></i>Hero Sliders Preview
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body p-0">
                @if($sliders->where('is_active', true)->count() > 0)
                    <div id="previewCarousel" class="carousel slide" data-bs-ride="carousel">
                        <div class="carousel-inner">
                            @foreach($sliders->where('is_active', true) as $index => $slider)
                            <div class="carousel-item {{ $index === 0 ? 'active' : '' }}">
                                <img src="{{ $slider->image_url }}" class="d-block w-100" alt="{{ $slider->title }}" style="height: 400px; object-fit: cover;">
                                <div class="carousel-caption d-none d-md-block">
                                    <h5>{{ $slider->title }}</h5>
                                    @if($slider->subtitle)
                                        <p>{{ $slider->subtitle }}</p>
                                    @endif
                                </div>
                            </div>
                            @endforeach
                        </div>
                        <button class="carousel-control-prev" type="button" data-bs-target="#previewCarousel" data-bs-slide="prev">
                            <span class="carousel-control-prev-icon"></span>
                        </button>
                        <button class="carousel-control-next" type="button" data-bs-target="#previewCarousel" data-bs-slide="next">
                            <span class="carousel-control-next-icon"></span>
                        </button>
                    </div>
                @else
                    <div class="text-center py-5">
                        <i class="fas fa-eye-slash fa-3x text-muted mb-3"></i>
                        <h5>No Active Sliders</h5>
                        <p class="text-muted">Activate some sliders to see the preview</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Help Modal -->
<div class="modal fade" id="helpModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-question-circle me-2"></i>Hero Sliders Help
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <h6>What are Hero Sliders?</h6>
                <p>Hero sliders are large, prominent images or content blocks that appear at the top of your website. They're perfect for:</p>
                <ul>
                    <li>Showcasing important announcements</li>
                    <li>Highlighting featured content or events</li>
                    <li>Creating visual impact for visitors</li>
                    <li>Promoting key messages or calls-to-action</li>
                </ul>
                <h6>Best Practices:</h6>
                <ul>
                    <li>Use high-quality images (recommended: 1920x1080px)</li>
                    <li>Keep text concise and readable</li>
                    <li>Limit to 3-5 active sliders for best performance</li>
                    <li>Use clear call-to-action buttons</li>
                </ul>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <a href="{{ route('admin.hero-sliders.create') }}" class="btn btn-primary">Create Slider</a>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header border-0">
                <h5 class="modal-title text-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>Confirm Delete
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <div class="mb-3">
                        <i class="fas fa-trash-alt fa-3x text-danger"></i>
                    </div>
                    <h6>Are you sure you want to delete this hero slider?</h6>
                    <p class="text-muted">This action cannot be undone. The slider will be permanently removed from your website.</p>
                </div>
            </div>
            <div class="modal-footer border-0">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>Cancel
                </button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-2"></i>Delete Slider
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
// View Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const gridViewBtn = document.getElementById('gridViewBtn');
    const listViewBtn = document.getElementById('listViewBtn');
    const gridView = document.getElementById('gridView');
    const listView = document.getElementById('listView');

    if (gridViewBtn && listViewBtn) {
        gridViewBtn.addEventListener('click', function() {
            gridView.classList.remove('d-none');
            listView.classList.add('d-none');
            gridViewBtn.classList.add('active');
            listViewBtn.classList.remove('active');
        });

        listViewBtn.addEventListener('click', function() {
            listView.classList.remove('d-none');
            gridView.classList.add('d-none');
            listViewBtn.classList.add('active');
            gridViewBtn.classList.remove('active');
        });
    }

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

function confirmDelete(id) {
    const form = document.getElementById('deleteForm');
    form.action = `/admin/hero-sliders/${id}`;
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}
</script>
@endpush
@endsection
