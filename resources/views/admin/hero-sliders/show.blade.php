@extends('layouts.admin')

@section('title', 'View Hero Slider')

@section('content')

    
  
        
       
            <div class="row">
                <div class="col-lg-8">
                    <div class="admin-card">
                        <h5 class="admin-card-title">Hero Slider Details</h5>
                        
                        <div class="row mb-4">
                            <div class="col-sm-3"><strong>Title:</strong></div>
                            <div class="col-sm-9">{{ $heroSlider->title }}</div>
                        </div>
                        
                        @if($heroSlider->subtitle)
                        <div class="row mb-4">
                            <div class="col-sm-3"><strong>Subtitle:</strong></div>
                            <div class="col-sm-9">{{ $heroSlider->subtitle }}</div>
                        </div>
                        @endif
                        
                        <div class="row mb-4">
                            <div class="col-sm-3"><strong>Description:</strong></div>
                            <div class="col-sm-9">{{ $heroSlider->description }}</div>
                        </div>
                        
                        @if($heroSlider->badge_text)
                        <div class="row mb-4">
                            <div class="col-sm-3"><strong>Badge:</strong></div>
                            <div class="col-sm-9">
                                <span class="badge bg-primary">
                                    <i class="{{ $heroSlider->badge_icon_formatted }} me-2"></i>
                                    {{ $heroSlider->badge_text }}
                                </span>
                            </div>
                        </div>
                        @endif
                        
                        @if($heroSlider->primary_button_text)
                        <div class="row mb-4">
                            <div class="col-sm-3"><strong>Primary Button:</strong></div>
                            <div class="col-sm-9">
                                <a href="{{ $heroSlider->primary_button_link ?: '#' }}" class="btn btn-primary btn-sm">
                                    {{ $heroSlider->primary_button_text }}
                                </a>
                                @if($heroSlider->primary_button_link)
                                    <small class="text-muted d-block">Link: {{ $heroSlider->primary_button_link }}</small>
                                @endif
                            </div>
                        </div>
                        @endif
                        
                        @if($heroSlider->secondary_button_text)
                        <div class="row mb-4">
                            <div class="col-sm-3"><strong>Secondary Button:</strong></div>
                            <div class="col-sm-9">
                                <a href="{{ $heroSlider->secondary_button_link ?: '#' }}" class="btn btn-outline-light btn-sm">
                                    {{ $heroSlider->secondary_button_text }}
                                </a>
                                @if($heroSlider->secondary_button_link)
                                    <small class="text-muted d-block">Link: {{ $heroSlider->secondary_button_link }}</small>
                                @endif
                            </div>
                        </div>
                        @endif
                        
                        <div class="row mb-4">
                            <div class="col-sm-3"><strong>Sort Order:</strong></div>
                            <div class="col-sm-9">
                                <span class="badge bg-secondary">{{ $heroSlider->sort_order }}</span>
                            </div>
                        </div>
                        
                        <div class="row mb-4">
                            <div class="col-sm-3"><strong>Status:</strong></div>
                            <div class="col-sm-9">
                                @if($heroSlider->is_active)
                                    <span class="badge bg-success">Active</span>
                                @else
                                    <span class="badge bg-danger">Inactive</span>
                                @endif
                            </div>
                        </div>
                        
                        <div class="row mb-4">
                            <div class="col-sm-3"><strong>Created:</strong></div>
                            <div class="col-sm-9">{{ $heroSlider->created_at->format('F d, Y \a\t g:i A') }}</div>
                        </div>
                        
                        <div class="row mb-4">
                            <div class="col-sm-3"><strong>Last Updated:</strong></div>
                            <div class="col-sm-9">{{ $heroSlider->updated_at->format('F d, Y \a\t g:i A') }}</div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4">
                    <div class="admin-card">
                        <h5 class="admin-card-title">Background Image</h5>
                        <img src="{{ $heroSlider->image_url }}" alt="{{ $heroSlider->title }}" 
                             class="img-fluid rounded">
                    </div>
                    
                    <div class="admin-card mt-4">
                        <h5 class="admin-card-title">Preview</h5>
                        <div class="hero-preview" style="background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('{{ $heroSlider->image_url }}'); background-size: cover; background-position: center; min-height: 200px; border-radius: 8px; color: white; padding: 20px; position: relative;">
                            @if($heroSlider->badge_text)
                            <div class="mb-2">
                                <span class="badge bg-light text-primary px-2 py-1">
                                    <i class="{{ $heroSlider->badge_icon_formatted }} me-1"></i>
                                    {{ $heroSlider->badge_text }}
                                </span>
                            </div>
                            @endif
                            
                            <h6 class="fw-bold mb-2">{{ Str::limit($heroSlider->title, 30) }}</h6>
                            
                            @if($heroSlider->subtitle)
                            <small class="text-light mb-2 d-block">{{ Str::limit($heroSlider->subtitle, 40) }}</small>
                            @endif
                            
                            <p class="small mb-3">{{ Str::limit($heroSlider->description, 80) }}</p>
                            
                            <div class="d-flex gap-2">
                                @if($heroSlider->primary_button_text)
                                <button class="btn btn-primary btn-sm">{{ Str::limit($heroSlider->primary_button_text, 15) }}</button>
                                @endif
                                
                                @if($heroSlider->secondary_button_text)
                                <button class="btn btn-outline-light btn-sm">{{ Str::limit($heroSlider->secondary_button_text, 15) }}</button>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>


<!-- Logout Form -->
<form id="logout-form" action="{{ route('admin.logout') }}" method="POST" class="d-none">
    @csrf
</form>
@endsection
