@extends('layouts.admin')

@section('title', 'Settings Management')

@section('content')
<!-- Enhanced Header Section -->
<div class="admin-page-header">
    <div class="d-flex justify-content-between align-items-start mb-4">
        <div class="page-header-content">
            <div class="d-flex align-items-center mb-2">
                <div class="page-icon me-3">
                    <i class="fas fa-cog"></i>
                </div>
                <div>
                    <h2 class="admin-title mb-1">Settings Management</h2>
                    <p class="text-muted mb-0">Configure your website settings and preferences</p>
                </div>
            </div>
        </div>
    </div>
</div>

@if(session('success'))
    <div class="admin-alert admin-alert-success">
        <i class="fas fa-check-circle me-2"></i>
        {{ session('success') }}
    </div>
@endif

@if(session('error'))
    <div class="admin-alert admin-alert-danger">
        <i class="fas fa-exclamation-circle me-2"></i>
        {{ session('error') }}
    </div>
@endif

@if($errors->any())
    <div class="admin-alert admin-alert-danger">
        <i class="fas fa-exclamation-circle me-2"></i>
        <ul class="mb-0">
            @foreach($errors->all() as $error)
                <li>{{ $error }}</li>
            @endforeach
        </ul>
    </div>
@endif

<form action="{{ route('admin.settings.update') }}" method="POST" id="settingsForm">
    @csrf
    @method('PUT')
    
    <div class="row">
        <!-- Settings Navigation -->
        <div class="col-lg-3 mb-4">
            <div class="admin-card">
                <div class="card-header-enhanced">
                    <h5 class="admin-card-title mb-0">
                        <i class="fas fa-list me-2"></i>Settings Categories
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="nav flex-column nav-pills settings-nav" id="v-pills-tab" role="tablist" aria-orientation="vertical">
                        <button class="nav-link active" id="v-pills-general-tab" data-bs-toggle="pill" data-bs-target="#v-pills-general" type="button" role="tab">
                            <i class="fas fa-globe me-2"></i>General
                        </button>
                        <button class="nav-link" id="v-pills-contact-tab" data-bs-toggle="pill" data-bs-target="#v-pills-contact" type="button" role="tab">
                            <i class="fas fa-address-book me-2"></i>Contact Info
                        </button>
                        <button class="nav-link" id="v-pills-social-tab" data-bs-toggle="pill" data-bs-target="#v-pills-social" type="button" role="tab">
                            <i class="fas fa-share-alt me-2"></i>Social Media
                        </button>
                        <button class="nav-link" id="v-pills-appearance-tab" data-bs-toggle="pill" data-bs-target="#v-pills-appearance" type="button" role="tab">
                            <i class="fas fa-palette me-2"></i>Appearance
                        </button>
                        <button class="nav-link" id="v-pills-seo-tab" data-bs-toggle="pill" data-bs-target="#v-pills-seo" type="button" role="tab">
                            <i class="fas fa-search me-2"></i>SEO
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Settings Content -->
        <div class="col-lg-9">
            <div class="tab-content" id="v-pills-tabContent">
                <!-- General Settings -->
                <div class="tab-pane fade show active" id="v-pills-general" role="tabpanel">
                    <div class="admin-card">
                        <div class="card-header-enhanced">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="admin-card-title mb-0">
                                    <i class="fas fa-globe me-2"></i>General Settings
                                </h5>
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="resetGroup('general')">
                                    <i class="fas fa-undo me-1"></i>Reset to Default
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            @if(isset($settings['general']))
                                @foreach($settings['general'] as $setting)
                                    <div class="mb-3">
                                        <label for="{{ $setting->key }}" class="form-label">
                                            {{ $setting->label }}
                                            @if($setting->description)
                                                <i class="fas fa-info-circle text-muted ms-1" data-bs-toggle="tooltip" title="{{ $setting->description }}"></i>
                                            @endif
                                        </label>
                                        
                                        @if($setting->type === 'boolean')
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="{{ $setting->key }}" 
                                                       name="settings[{{ $setting->key }}]" value="1"
                                                       {{ $setting->value ? 'checked' : '' }}>
                                                <label class="form-check-label" for="{{ $setting->key }}">
                                                    {{ $setting->label }}
                                                </label>
                                            </div>
                                        @elseif($setting->type === 'text')
                                            <textarea class="form-control" id="{{ $setting->key }}" 
                                                      name="settings[{{ $setting->key }}]" rows="3">{{ old('settings.'.$setting->key, $setting->value) }}</textarea>
                                        @else
                                            <input type="text" class="form-control" id="{{ $setting->key }}" 
                                                   name="settings[{{ $setting->key }}]" 
                                                   value="{{ old('settings.'.$setting->key, $setting->value) }}">
                                        @endif
                                        
                                        @if($setting->description && $setting->type !== 'boolean')
                                            <div class="form-text">{{ $setting->description }}</div>
                                        @endif
                                    </div>
                                @endforeach
                            @else
                                <div class="text-center py-4">
                                    <i class="fas fa-cog fa-3x text-muted mb-3"></i>
                                    <h6>No General Settings Found</h6>
                                    <p class="text-muted">Click "Reset to Default" to create default general settings.</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Contact Settings -->
                <div class="tab-pane fade" id="v-pills-contact" role="tabpanel">
                    <div class="admin-card">
                        <div class="card-header-enhanced">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="admin-card-title mb-0">
                                    <i class="fas fa-address-book me-2"></i>Contact Information
                                </h5>
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="resetGroup('contact')">
                                    <i class="fas fa-undo me-1"></i>Reset to Default
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            @if(isset($settings['contact']))
                                @foreach($settings['contact'] as $setting)
                                    <div class="mb-3">
                                        <label for="{{ $setting->key }}" class="form-label">
                                            {{ $setting->label }}
                                            @if($setting->description)
                                                <i class="fas fa-info-circle text-muted ms-1" data-bs-toggle="tooltip" title="{{ $setting->description }}"></i>
                                            @endif
                                        </label>
                                        
                                        @if($setting->type === 'text')
                                            <textarea class="form-control" id="{{ $setting->key }}" 
                                                      name="settings[{{ $setting->key }}]" rows="3">{{ old('settings.'.$setting->key, $setting->value) }}</textarea>
                                        @else
                                            <input type="text" class="form-control" id="{{ $setting->key }}" 
                                                   name="settings[{{ $setting->key }}]" 
                                                   value="{{ old('settings.'.$setting->key, $setting->value) }}">
                                        @endif
                                        
                                        @if($setting->description)
                                            <div class="form-text">{{ $setting->description }}</div>
                                        @endif
                                    </div>
                                @endforeach
                            @else
                                <div class="text-center py-4">
                                    <i class="fas fa-address-book fa-3x text-muted mb-3"></i>
                                    <h6>No Contact Settings Found</h6>
                                    <p class="text-muted">Click "Reset to Default" to create default contact settings.</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Social Media Settings -->
                <div class="tab-pane fade" id="v-pills-social" role="tabpanel">
                    <div class="admin-card">
                        <div class="card-header-enhanced">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="admin-card-title mb-0">
                                    <i class="fas fa-share-alt me-2"></i>Social Media Links
                                </h5>
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="resetGroup('social')">
                                    <i class="fas fa-undo me-1"></i>Reset to Default
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            @if(isset($settings['social']))
                                @foreach($settings['social'] as $setting)
                                    <div class="mb-3">
                                        <label for="{{ $setting->key }}" class="form-label">
                                            {{ $setting->label }}
                                            @if($setting->description)
                                                <i class="fas fa-info-circle text-muted ms-1" data-bs-toggle="tooltip" title="{{ $setting->description }}"></i>
                                            @endif
                                        </label>
                                        <input type="url" class="form-control" id="{{ $setting->key }}" 
                                               name="settings[{{ $setting->key }}]" 
                                               value="{{ old('settings.'.$setting->key, $setting->value) }}"
                                               placeholder="https://...">
                                        @if($setting->description)
                                            <div class="form-text">{{ $setting->description }}</div>
                                        @endif
                                    </div>
                                @endforeach
                            @else
                                <div class="text-center py-4">
                                    <i class="fas fa-share-alt fa-3x text-muted mb-3"></i>
                                    <h6>No Social Media Settings Found</h6>
                                    <p class="text-muted">Click "Reset to Default" to create default social media settings.</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Appearance Settings -->
                <div class="tab-pane fade" id="v-pills-appearance" role="tabpanel">
                    <div class="admin-card">
                        <div class="card-header-enhanced">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="admin-card-title mb-0">
                                    <i class="fas fa-palette me-2"></i>Appearance Settings
                                </h5>
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="resetGroup('appearance')">
                                    <i class="fas fa-undo me-1"></i>Reset to Default
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="text-center py-4">
                                <i class="fas fa-palette fa-3x text-muted mb-3"></i>
                                <h6>Appearance Settings</h6>
                                <p class="text-muted">Appearance settings will be available in future updates.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- SEO Settings -->
                <div class="tab-pane fade" id="v-pills-seo" role="tabpanel">
                    <div class="admin-card">
                        <div class="card-header-enhanced">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="admin-card-title mb-0">
                                    <i class="fas fa-search me-2"></i>SEO Settings
                                </h5>
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="resetGroup('seo')">
                                    <i class="fas fa-undo me-1"></i>Reset to Default
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="text-center py-4">
                                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                                <h6>SEO Settings</h6>
                                <p class="text-muted">SEO settings will be available in future updates.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Save Button -->
            <div class="d-flex justify-content-end mt-4">
                <button type="submit" class="btn btn-primary btn-enhanced">
                    <i class="fas fa-save me-2"></i>Save Settings
                </button>
            </div>
        </div>
    </div>
</form>

<!-- Reset Confirmation Modal -->
<div class="modal fade" id="resetModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header border-0">
                <h5 class="modal-title text-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>Reset Settings
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <div class="mb-3">
                        <i class="fas fa-undo fa-3x text-warning"></i>
                    </div>
                    <h6>Are you sure you want to reset these settings?</h6>
                    <p class="text-muted">This will restore all settings in this category to their default values. Any custom changes will be lost.</p>
                </div>
            </div>
            <div class="modal-footer border-0">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>Cancel
                </button>
                <form id="resetForm" method="POST" action="{{ route('admin.settings.reset') }}" style="display: inline;">
                    @csrf
                    <input type="hidden" name="group" id="resetGroup">
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-undo me-2"></i>Reset Settings
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

function resetGroup(group) {
    document.getElementById('resetGroup').value = group;
    const modal = new bootstrap.Modal(document.getElementById('resetModal'));
    modal.show();
}
</script>
@endpush
@endsection
