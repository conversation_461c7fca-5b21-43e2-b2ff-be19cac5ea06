@extends('layouts.admin-login')

@section('title', 'Admin Login')

@section('content')
<div class="admin-login-container">
    <div class="row g-0 h-100">
        <!-- Left Side - Branding -->
        <div class="col-lg-6 d-none d-lg-block">
            <div class="admin-login-left">
                <div class="admin-logo">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <h2 class="mb-3">UIA Admin Panel</h2>
                <p class="mb-4">Urla Industries Association</p>
                <p class="opacity-75">Secure access to manage your industrial association platform. Monitor members, manage content, and oversee operations.</p>
                
                <div class="mt-5">
                    <div class="d-flex align-items-center mb-3">
                        <i class="fas fa-check-circle me-3"></i>
                        <span>Secure Authentication</span>
                    </div>
                    <div class="d-flex align-items-center mb-3">
                        <i class="fas fa-check-circle me-3"></i>
                        <span>Member Management</span>
                    </div>
                    <div class="d-flex align-items-center">
                        <i class="fas fa-check-circle me-3"></i>
                        <span>Content Control</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Right Side - Login Form -->
        <div class="col-lg-6">
            <div class="admin-login-right">
                <!-- Mobile Logo -->
                <div class="text-center d-lg-none mb-4">
                    <div class="admin-logo mx-auto">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                </div>
                
                <h1 class="admin-form-title">Welcome Back</h1>
                <p class="admin-form-subtitle">Please sign in to your admin account</p>
                
                <!-- Display Success Messages -->
                @if(session('success'))
                    <div class="admin-alert admin-alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        {{ session('success') }}
                    </div>
                @endif
                
                <!-- Display Error Messages -->
                @if($errors->any())
                    <div class="admin-alert admin-alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        @foreach($errors->all() as $error)
                            {{ $error }}
                        @endforeach
                    </div>
                @endif
                
                <form id="adminLoginForm" method="POST" action="{{ route('login') }}" class="needs-validation" novalidate>
                    @csrf
                    
                    <!-- Email Field -->
                    <div class="admin-form-group">
                        <label for="email" class="admin-form-label">
                            <i class="fas fa-envelope me-2"></i>Email Address
                        </label>
                        <input
                            type="email"
                            id="email"
                            name="email"
                            class="admin-form-control @error('email') is-invalid @enderror"
                            value="{{ old('email') }}"
                            placeholder="Enter your email address"
                            required
                            autocomplete="email"
                            autofocus
                        >
                        @error('email')
                            <div class="admin-invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <!-- Password Field -->
                    <div class="admin-form-group">
                        <label for="password" class="admin-form-label">
                            <i class="fas fa-lock me-2"></i>Password
                        </label>
                        <div class="position-relative">
                            <input
                                type="password"
                                id="password"
                                name="password"
                                class="admin-form-control @error('password') is-invalid @enderror"
                                placeholder="Enter your password"
                                required
                                autocomplete="current-password"
                                minlength="6"
                            >
                            <button
                                type="button"
                                id="passwordToggle"
                                class="btn btn-link position-absolute end-0 top-50 translate-middle-y pe-3"
                                style="border: none; background: none; color: #6c757d;"
                            >
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        @error('password')
                            <div class="admin-invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <!-- Remember Me -->
                    <div class="admin-form-check">
                        <input
                            type="checkbox"
                            id="remember"
                            name="remember"
                            class="admin-form-check-input"
                            {{ old('remember') ? 'checked' : '' }}
                        >
                        <label for="remember" class="admin-form-check-label">
                            Remember me for 30 days
                        </label>
                    </div>
                    
                    <!-- Submit Button -->
                    <button type="submit" class="admin-btn-primary">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        Sign In
                    </button>
                </form>
                
                <!-- Additional Links -->
                <div class="text-center mt-4">
                    <p class="text-muted small">
                        <i class="fas fa-shield-alt me-1"></i>
                        Your connection is secure and encrypted
                    </p>
                </div>
                
                <!-- Back to Website Link -->
                <div class="text-center mt-3">
                    <a href="{{ route('home') }}" class="text-decoration-none" style="color: var(--admin-primary);">
                        <i class="fas fa-arrow-left me-2"></i>
                        Back to Website
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
    body {
        margin: 0;
        padding: 0;
        overflow-x: hidden;
    }
</style>
@endpush
@endsection
