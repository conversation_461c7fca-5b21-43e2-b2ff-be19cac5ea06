@extends('layouts.admin')
@section('title', 'Committee Management')

@section('content')
<div class="container-fluid">
    <!-- Enhanced Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800 d-flex align-items-center">
                <i class="fas fa-users-cog text-primary me-3"></i>
               Current Office Bearers Management
            </h1>
            <p class="text-muted mb-0">Manage Office Bearers members and their positions</p>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ route('admin.committees.create') }}" class="btn btn-primary shadow-sm">
                <i class="fas fa-plus me-2"></i>Add New Office Bearers Member
            </a>
        </div>
    </div>

    <!-- Enhanced Statistics Cards -->
    <div class="row mb-4 d-none">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Office Bearers Members
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $committees->total() }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Active Members
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $committees->where('is_active', true)->count() }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Alert Messages -->
    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show shadow-sm" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger alert-dismissible fade show shadow-sm" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            {{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <!-- Enhanced Main Content Card -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-list me-2"></i>Current Office Bearers Members List
            </h6>
            <div class="dropdown no-arrow">
                <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink"
                   data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                </a>
                <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in"
                     aria-labelledby="dropdownMenuLink">
                    <div class="dropdown-header">Actions:</div>
                    <a class="dropdown-item" href="{{ route('admin.committees.create') }}">
                        <i class="fas fa-plus fa-sm fa-fw mr-2 text-gray-400"></i>
                        Add New Member
                    </a>
                </div>
            </div>
        </div>
        
        <div class="card-body">
            @if($committees->count() > 0)
                <!-- Enhanced Table with Modern Design -->
                <div class="admin-table-responsive rounded-3 overflow-hidden shadow-sm">
                    <table class="admin-table mb-0" width="100%">
                        <thead class="bg-gradient-primary">
                            <tr>
                                <th class="border-0 text-white fw-semibold">
                                    <i class="fas fa-image me-2"></i>Image
                                </th>
                                <th class="border-0 text-white fw-semibold">
                                    <i class="fas fa-user me-2"></i>Name
                                </th>
                                <th class="border-0 text-white fw-semibold">
                                    <i class="fas fa-briefcase me-2"></i>Position
                                </th>
                                <th class="border-0 text-white fw-semibold">
                                    <i class="fas fa-sort-numeric-up me-2"></i>Order
                                </th>
                                <th class="border-0 text-white fw-semibold">
                                    <i class="fas fa-circle-dot me-2"></i>Status
                                </th>
                                <th class="border-0 text-white fw-semibold">
                                    <i class="fas fa-calendar-plus me-2"></i>Created
                                </th>
                                <th class="border-0 text-white fw-semibold text-center">
                                    <i class="fas fa-cogs me-2"></i>Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($committees as $committee)
                            <tr class="border-bottom">
                                <td class="align-middle">
                                    <div class="d-flex align-items-center">
                                        @if($committee->image_path)
                                            <img src="{{ $committee->image_url }}" 
                                                 alt="{{ $committee->name }}" 
                                                 class="rounded-circle shadow-sm"
                                                 style="width: 50px; height: 50px; object-fit: cover;">
                                        @else
                                            <div class="bg-gradient-primary text-white rounded-circle d-flex align-items-center justify-content-center shadow-sm"
                                                 style="width: 50px; height: 50px; font-size: 18px; font-weight: 600;">
                                                {{ strtoupper(substr($committee->name, 0, 2)) }}
                                            </div>
                                        @endif
                                    </div>
                                </td>
                                <td class="align-middle">
                                    <div>
                                        <h6 class="mb-0 fw-semibold text-dark">{{ $committee->name }}</h6>
                                    </div>
                                </td>
                                <td class="align-middle">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-briefcase text-info me-2"></i>
                                        <span class="text-dark">{{ $committee->position }}</span>
                                    </div>
                                </td>
                                <td class="align-middle">
                                    <span class="badge bg-secondary">{{ $committee->sort_order }}</span>
                                </td>
                                <td class="align-middle">
                                    @if($committee->is_active)
                                        <span class="badge bg-success shadow-sm">
                                            <i class="fas fa-check-circle me-1"></i>Active
                                        </span>
                                    @else
                                        <span class="badge bg-secondary shadow-sm">
                                            <i class="fas fa-pause-circle me-1"></i>Inactive
                                        </span>
                                    @endif
                                </td>
                                <td class="align-middle">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-calendar text-info me-2"></i>
                                        <small class="text-muted">{{ $committee->created_at->format('M d, Y') }}</small>
                                    </div>
                                </td>
                                <td class="align-middle text-center">
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('admin.committees.show', $committee) }}" 
                                           class="btn btn-sm btn-outline-info" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('admin.committees.edit', $committee) }}" 
                                           class="btn btn-sm btn-outline-primary" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form action="{{ route('admin.committees.toggle-active', $committee) }}" 
                                              method="POST" class="d-inline">
                                            @csrf
                                            @method('PATCH')
                                            <button type="submit" 
                                                    class="btn btn-sm btn-outline-{{ $committee->is_active ? 'warning' : 'success' }}" 
                                                    title="{{ $committee->is_active ? 'Deactivate' : 'Activate' }}">
                                                <i class="fas fa-{{ $committee->is_active ? 'pause' : 'play' }}"></i>
                                            </button>
                                        </form>
                                        <form action="{{ route('admin.committees.destroy', $committee) }}" 
                                              method="POST" class="d-inline"
                                              onsubmit="return confirm('Are you sure you want to delete this committee member?')">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                <!-- Enhanced Pagination -->
                <div class="mt-4 d-flex justify-content-center">
                    {{ $committees->links() }}
                </div>
            @else
                <div class="text-center py-5">
                    <div class="d-flex flex-column align-items-center justify-content-center py-4">
                        <div class="bg-light rounded-circle p-4 mb-4">
                            <i class="fas fa-users-cog fa-3x text-muted"></i>
                        </div>
                        <h5 class="text-muted mb-2">No Committee Members Found</h5>
                        <p class="text-muted mb-4">Committee members will appear here when added</p>
                        <a href="{{ route('admin.committees.create') }}" class="btn btn-primary shadow-sm">
                            <i class="fas fa-plus me-2"></i>Add First Committee Member
                        </a>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
