@extends('layouts.admin')

@section('title', 'View Committee Member')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2 class="admin-title mb-0">Current Office Bearers Member Details</h2>
    <a href="{{ route('admin.committees.index') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i>Back to List
    </a>
</div>

@if(session('success'))
    <div class="admin-alert admin-alert-success">
        <i class="fas fa-check-circle me-2"></i>
        {{ session('success') }}
    </div>
@endif

<div class="row">
    <div class="col-lg-8">
        <div class="admin-card">
            <div class="d-flex justify-content-between align-items-start mb-4">
                <div>
                    <h5 class="admin-card-title mb-2">{{ $committee->name }}</h5>
                    <p class="text-muted mb-0">{{ $committee->position }}</p>
                </div>
                <div>
                    @if($committee->is_active)
                        <span class="badge bg-success">Active</span>
                    @else
                        <span class="badge bg-secondary">Inactive</span>
                    @endif
                </div>
            </div>
            
            @if($committee->image_path)
                <div class="mb-4">
                    <img src="{{ $committee->image_url }}" alt="{{ $committee->name }}" 
                         class="img-fluid rounded shadow-sm" style="max-width: 300px; max-height: 300px; object-fit: cover;">
                </div>
            @endif
            
            <div class="row mb-4">
                <div class="col-md-6">
                    <h6 class="text-muted">Sort Order</h6>
                    <p class="mb-0">{{ $committee->sort_order }}</p>
                </div>
                <div class="col-md-6">
                    <h6 class="text-muted">Status</h6>
                    <p class="mb-0">
                        @if($committee->is_active)
                            <span class="text-success"><i class="fas fa-check-circle me-1"></i>Active</span>
                        @else
                            <span class="text-secondary"><i class="fas fa-pause-circle me-1"></i>Inactive</span>
                        @endif
                    </p>
                </div>
            </div>
            
            <div class="d-flex gap-2">
                <a href="{{ route('admin.committees.edit', $committee) }}" class="btn btn-warning">
                    <i class="fas fa-edit me-2"></i>Edit
                </a>
                
                <form action="{{ route('admin.committees.toggle-active', $committee) }}" method="POST" class="d-inline">
                    @csrf
                    @method('PATCH')
                    <button type="submit" class="btn {{ $committee->is_active ? 'btn-secondary' : 'btn-success' }}">
                        <i class="fas fa-{{ $committee->is_active ? 'times' : 'check' }} me-2"></i>
                        {{ $committee->is_active ? 'Deactivate' : 'Activate' }}
                    </button>
                </form>
                
                <button type="button" class="btn btn-danger" onclick="confirmDelete({{ $committee->id }})">
                    <i class="fas fa-trash me-2"></i>Delete
                </button>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="admin-card">
            <h6 class="admin-card-title">Information</h6>
            
            <div class="mb-3">
                <small class="text-muted">Created</small>
                <p class="mb-0">{{ $committee->created_at->format('M d, Y g:i A') }}</p>
            </div>
            
            <div class="mb-3">
                <small class="text-muted">Last Updated</small>
                <p class="mb-0">{{ $committee->updated_at->format('M d, Y g:i A') }}</p>
            </div>
            
            @if($committee->image_path)
                <div class="mb-3">
                    <small class="text-muted">Image File</small>
                    <p class="mb-0 text-break">{{ basename($committee->image_path) }}</p>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this Office Bearers member? This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" class="d-inline">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(committeeId) {
    const deleteForm = document.getElementById('deleteForm');
    deleteForm.action = `/admin/committees/${committeeId}`;
    
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}
</script>
@endsection
