@extends('layouts.app')

@section('title', 'News & Updates - Urla Industries Association')

@section('content')
    <!-- Content Ads -->
    @if(isset($ads['content']) && $ads['content']->count() > 0)
        <div class="content-ads-container">
            @include('partials.ads', ['ads' => $ads['content'], 'position' => 'content'])
        </div>
    @endif
@php
use Illuminate\Support\Str;
@endphp
    <!-- Page Header -->
   <section class="page-header" style="background: url('image/header.jpg'); background-size: cover; background-position: center; background-attachment: fixed;">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center" data-aos="fade-up">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb justify-content-center">
                            <li class="breadcrumb-item"><a href="{{ route('home') }}">Home</a></li>
                            <li class="breadcrumb-item active" aria-current="page">News & Updates</li>
                        </ol>
                    </nav>
                    <h1 class="display-4 fw-bold mb-3">News & Updates</h1>
                    <p class="lead">Stay informed with the latest developments from UIA and the industrial community</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Latest News Section -->
    <section id="latest-news" class="py-5">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5" data-aos="fade-up">
                    <span class="section-badge">Latest Updates</span>
                    <h2 class="section-title">Recent News & Announcements</h2>
                    <p class="lead text-muted">Stay informed with the latest developments from UIA and the industrial sector</p>
                </div>
            </div>

            @if($news->count() > 0)
            <!-- Featured News -->
            @php
                $featuredNews = $news->where('is_featured', true)->first();
                $highlights = $news->where('is_featured', false)->take(3);
            @endphp

            @if($featuredNews)
            <div class="row mb-5">
                <div class="col-lg-8" data-aos="fade-right">
                    <div class="featured-news-card">
                        <div class="card border-0 shadow-lg overflow-hidden">
                            <div class="row g-0">
                                <div class="col-md-6">
                                    <img src="{{ $featuredNews->image_url }}" class="img-fluid h-100 object-cover" alt="{{ $featuredNews->title }}" style="height: 300px; object-fit: cover;">
                                </div>
                                <div class="col-md-6">
                                    <div class="card-body p-4 h-100 d-flex flex-column">
                                        <div class="news-meta mb-3">
                                            <span class="badge bg-primary me-2">Featured</span>
                                            <span class="badge bg-{{ $featuredNews->category === 'policy' ? 'success' : ($featuredNews->category === 'event' ? 'info' : 'warning') }} me-2">{{ ucfirst($featuredNews->category) }}</span>
                                            <span class="text-muted small">
                                                <i class="fas fa-calendar-alt me-1"></i>{{ $featuredNews->formatted_published_date }}
                                            </span>
                                        </div>
                                        <h3 class="card-title fw-bold mb-3">{{ $featuredNews->title }}</h3>
                                        <p class="card-text text-muted mb-4">
                                            {{ $featuredNews->excerpt ?: Str::limit(strip_tags($featuredNews->content), 150) }}
                                        </p>
                                        <div class="mt-auto">
                                            <a href="{{ route('news-detail', $featuredNews) }}" class="btn btn-primary">
                                                <i class="fas fa-arrow-right me-2"></i>Read More
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                @if($highlights->count() > 0)
                <div class="col-lg-4" data-aos="fade-left">
                    <div class="news-highlights">
                        <h4 class="mb-4 fw-bold text-primary">
                            <i class="fas fa-star me-2"></i>News Highlights
                        </h4>
                        @foreach($highlights as $highlight)
                        <div class="highlight-item mb-3 p-3 bg-light rounded">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-newspaper text-primary me-3 fa-lg"></i>
                                <div>
                                    <h6 class="mb-1 fw-semibold">{{ Str::limit($highlight->title, 50) }}</h6>
                                    <small class="text-muted">{{ $highlight->formatted_published_date }}</small>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
                @endif
            </div>
            @endif

            <!-- News Grid -->
            <div class="row g-4">
                @foreach($news as $index => $newsItem)
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="{{ ($index % 3 + 1) * 100 }}">
                    <div class="news-card">
                        <div class="card border-0 shadow-sm h-100">
                            <div class="news-image-wrapper">
                                <img src="{{ $newsItem->image_url }}" class="card-img-top" alt="{{ $newsItem->title }}" style="height: 200px; object-fit: cover;">
                                <div class="news-category-badge">
                                    <span class="badge bg-{{ $newsItem->category === 'policy' ? 'success' : ($newsItem->category === 'infrastructure' ? 'info' : ($newsItem->category === 'achievement' ? 'warning' : ($newsItem->category === 'event' ? 'primary' : 'secondary'))) }}">
                                        {{ ucfirst($newsItem->category) }}
                                    </span>
                                </div>
                            </div>
                            <div class="card-body d-flex flex-column">
                                <div class="news-meta mb-2">
                                    <small class="text-muted">
                                        <i class="fas fa-calendar-alt me-1"></i>{{ $newsItem->formatted_published_date }}
                                        <span class="mx-2">•</span>
                                        <i class="fas fa-user me-1"></i>{{ $newsItem->author ?: 'UIA Admin' }}
                                    </small>
                                </div>
                                <h5 class="card-title fw-bold mb-3">{{ $newsItem->title }}</h5>
                                <p class="card-text text-muted mb-4">
                                    {{ $newsItem->excerpt ?: Str::limit(strip_tags($newsItem->content), 120) }}
                                </p>
                                <div class="mt-auto">
                                    <a href="{{ route('news-detail', $newsItem) }}" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-arrow-right me-1"></i>Read More
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>

            <!-- Pagination -->
            @if($news->hasPages())
            <div class="row mt-5">
                <div class="col-12">
                    {{ $news->links('pagination.custom') }}
                </div>
            </div>
            @endif

            @else
            <!-- No News Available -->
            <div class="row">
                <div class="col-12 text-center py-5">
                    <div class="no-news-placeholder">
                        <i class="fas fa-newspaper fa-4x text-muted mb-4"></i>
                        <h4 class="text-muted mb-3">No News Available</h4>
                        <p class="text-muted">Check back later for the latest updates and announcements.</p>
                    </div>
                </div>
            </div>
            @endif
        </div>
    </section>
@endsection
