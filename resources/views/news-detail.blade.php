@extends('layouts.app')

@section('title', $news->meta_title ?: $news->title . ' - Urla Industries Association')

@section('meta')
    @if($news->meta_description)
        <meta name="description" content="{{ $news->meta_description }}">
    @endif
    <meta property="og:title" content="{{ $news->title }}">
    <meta property="og:description" content="{{ $news->excerpt ?: Str::limit(strip_tags($news->content), 160) }}">
    <meta property="og:image" content="{{ $news->image_url }}">
    <meta property="og:url" content="{{ route('news-detail', $news) }}">
    <meta property="og:type" content="article">
@endsection

@section('content')


    <!-- Page Header -->
    <section class="page-header"  style="background: url('{{ $news->image_url }}'); background-size: cover; background-position: center; background-attachment: fixed;">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center" >
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb justify-content-center">
                            <li class="breadcrumb-item active"><a href="{{ route('home') }}" class="text-decoration-none">Home</a></li>
                            <li class="breadcrumb-item active" aria-current="page"><a href="{{ route('news') }}" class="text-decoration-none">News</a></li>
                            <li class="breadcrumb-item active" aria-current="page">{{ Str::limit($news->title, 50) }}</li>
                        </ol>
                    </nav>
                    <h1 class="display-4 fw-bold mb-3">News & Updates</h1>
                    <p class="lead">{{ Str::limit($news->title, 50) }}</p>
                </div>
            </div>
        </div>
    </section>



    <!-- News Detail Content -->
    <section class="news-detail-section py-5">
        <div class="container">
            <div class="row">
                <!-- Main Content -->
                <div class="col-lg-8">
                    <article class="news-article">
                        <!-- Article Header -->
                        <header class="article-header mb-5">
                            <div class="article-meta mb-3">
                                @if($news->is_featured)
                                    <span class="badge bg-primary me-2">Featured</span>
                                @endif
                                <span class="badge bg-success me-2">{{ ucfirst($news->category) }}</span>
                                <span class="text-muted">
                                    <i class="fas fa-calendar-alt me-1"></i>{{ $news->formatted_published_date }}
                                    <span class="mx-2">•</span>
                                    <i class="fas fa-user me-1"></i>{{ $news->author ?: 'UIA Admin' }}
                                    <span class="mx-2">•</span>
                                    <i class="fas fa-clock me-1"></i>{{ ceil(str_word_count(strip_tags($news->content)) / 200) }} min read
                                </span>
                            </div>
                            <h1 class="article-title display-5 fw-bold mb-4">
                                {{ $news->title }}
                            </h1>
                            @if($news->excerpt)
                                <p class="article-subtitle lead text-muted mb-4">
                                    {{ $news->excerpt }}
                                </p>
                            @endif
                        </header>

                        <!-- Featured Image -->
                        <div class="article-image mb-5">
                            <img src="{{ $news->image_url }}" class="img-fluid rounded shadow-lg" alt="{{ $news->title }}">
                            <figcaption class="text-muted text-center mt-2 small">
                                <i class="fas fa-camera me-1"></i>{{ $news->title }}
                            </figcaption>
                        </div>

                        <!-- Article Content -->
                        <div class="article-content">
                            <div class="content-wrapper">
                                {!! $news->content !!}
                            </div>
                        </div>

                        <!-- Article Footer -->
                        <footer class="article-footer mt-5 pt-4 border-top">
                            <div class="row align-items-center">
                                <div class="col-md-6">
                                    @if($news->tags && count(json_decode($news->tags, true)) > 0)
                                        <div class="article-tags">
                                            <span class="me-2 fw-bold">Tags:</span>
                                            @foreach(json_decode($news->tags, true) as $tag)
                                                <span class="badge bg-outline-primary me-1">#{{ $tag }}</span>
                                            @endforeach
                                        </div>
                                    @endif
                                </div>
                                <div class="col-md-6 text-md-end mt-3 mt-md-0">
                                    <div class="share-buttons">
                                        <span class="me-2 fw-bold">Share:</span>
                                        <a href="https://www.facebook.com/sharer/sharer.php?u={{ urlencode(route('news-detail', $news)) }}" target="_blank" class="btn btn-outline-primary btn-sm me-1">
                                            <i class="fab fa-facebook-f"></i>
                                        </a>
                                        <a href="https://twitter.com/intent/tweet?url={{ urlencode(route('news-detail', $news)) }}&text={{ urlencode($news->title) }}" target="_blank" class="btn btn-outline-info btn-sm me-1">
                                            <i class="fab fa-twitter"></i>
                                        </a>
                                        <a href="https://www.linkedin.com/sharing/share-offsite/?url={{ urlencode(route('news-detail', $news)) }}" target="_blank" class="btn btn-outline-primary btn-sm me-1">
                                            <i class="fab fa-linkedin-in"></i>
                                        </a>
                                        <a href="https://wa.me/?text={{ urlencode($news->title . ' - ' . route('news-detail', $news)) }}" target="_blank" class="btn btn-outline-success btn-sm">
                                            <i class="fab fa-whatsapp"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </footer>
                    </article>
                </div>

                <!-- Sidebar -->
                <div class="col-lg-4" >
                    <aside class="news-sidebar">
                        <!-- Related News -->
                        <div class="sidebar-widget mb-5">
                            <h4 class="widget-title fw-bold mb-4">
                                <i class="fas fa-newspaper text-primary me-2"></i>Related News
                            </h4>
                            <div class="related-news">
                                @if($relatedNews->count() > 0)
                                    @foreach($relatedNews as $relatedItem)
                                        <div class="related-item mb-4">
                                            <div class="row g-3">
                                                <div class="col-4">
                                                    <img src="{{ $relatedItem->image_url }}" class="img-fluid rounded" alt="{{ $relatedItem->title }}" style="height: 60px; object-fit: cover;">
                                                </div>
                                                <div class="col-8">
                                                    <h6 class="fw-bold mb-1">
                                                        <a href="{{ route('news-detail', $relatedItem) }}" class="text-decoration-none text-dark">
                                                            {{ Str::limit($relatedItem->title, 40) }}
                                                        </a>
                                                    </h6>
                                                    <small class="text-muted">
                                                        <i class="fas fa-calendar-alt me-1"></i>{{ $relatedItem->formatted_published_date }}
                                                    </small>
                                                    @if($relatedItem->category)
                                                        <br>
                                                        <span class="badge bg-light text-dark mt-1" style="font-size: 0.7rem;">
                                                            {{ ucfirst($relatedItem->category) }}
                                                        </span>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                @else
                                    <div class="text-center py-4">
                                        <i class="fas fa-newspaper text-muted mb-2" style="font-size: 2rem;"></i>
                                        <p class="text-muted mb-0">No related news found</p>
                                    </div>
                                @endif
                            </div>
                            <div class="text-center mt-4">
                                <a href="{{ route('news') }}" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-arrow-right me-1"></i>View All News
                                </a>
                            </div>
                        </div>

                        <!-- Newsletter Signup -->
                        <div class="sidebar-widget mb-5 d-none">
                            <div class="newsletter-widget bg-gradient-primary text-white p-4 rounded">
                                <h5 class="fw-bold mb-3">
                                    <i class="fas fa-envelope me-2"></i>Stay Updated
                                </h5>
                                <p class="mb-3 small">Subscribe to our newsletter for latest news and updates.</p>
                                <form class="newsletter-form">
                                    <div class="mb-3">
                                        <input type="email" class="form-control" placeholder="Your email address" required>
                                    </div>
                                    <button type="submit" class="btn btn-light btn-sm w-100">
                                        <i class="fas fa-paper-plane me-1"></i>Subscribe
                                    </button>
                                </form>
                            </div>
                        </div>

                        <!-- Quick Links -->
                        <div class="sidebar-widget mb-5">
                            <h4 class="widget-title fw-bold mb-4">
                                <i class="fas fa-link text-primary me-2"></i>Quick Links
                            </h4>
                            <ul class="list-unstyled quick-links">
                                <li class="mb-2">
                                    <a href="{{ route('about-us') }}" class="text-decoration-none d-flex align-items-center">
                                        <i class="fas fa-chevron-right text-primary me-2 small"></i>About UIA
                                    </a>
                                </li>
                                <li class="mb-2">
                                    <a href="{{ route('uia-members') }}" class="text-decoration-none d-flex align-items-center">
                                        <i class="fas fa-chevron-right text-primary me-2 small"></i>UIA Members
                                    </a>
                                </li>
                                <li class="mb-2">
                                    <a href="{{ route('get-quotation') }}" class="text-decoration-none d-flex align-items-center">
                                        <i class="fas fa-chevron-right text-primary me-2 small"></i>Get Quotation
                                    </a>
                                </li>
                                <li class="mb-2">
                                    <a href="{{ route('contact') }}" class="text-decoration-none d-flex align-items-center">
                                        <i class="fas fa-chevron-right text-primary me-2 small"></i>Contact Us
                                    </a>
                                </li>
                            </ul>
                        </div>

                        <!-- Contact Info -->
                        <div class="sidebar-widget">
                            <div class="contact-widget bg-light p-4 rounded">
                                <h5 class="fw-bold mb-3">
                                    <i class="fas fa-phone text-primary me-2"></i>Contact UIA
                                </h5>
                                <div class="contact-info">
                                    <p class="mb-2 small">
                                        <i class="fas fa-map-marker-alt text-muted me-2"></i>
                                        Urla Industrial Area, Raipur, Chhattisgarh
                                    </p>
                                    <p class="mb-2 small">
                                        <i class="fas fa-phone text-muted me-2"></i>
                                        +91 ************
                                    </p>
                                    <p class="mb-3 small">
                                        <i class="fas fa-envelope text-muted me-2"></i>
                                        <EMAIL>
                                    </p>
                                    <a href="{{ route('about-us') }}" class="btn btn-primary btn-sm w-100">
                                        <i class="fas fa-envelope me-1"></i>Get in Touch
                                    </a>
                                </div>
                            </div>
                        </div>
                    </aside>
                </div>
            </div>
        </div>
    </section>

    <!-- Navigation Between Articles -->
    <section class="article-navigation py-4 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <a href="#" class="nav-link-prev d-flex align-items-center text-decoration-none">
                        <div class="nav-icon me-3">
                            <i class="fas fa-chevron-left fa-2x text-primary"></i>
                        </div>
                        <div>
                            <small class="text-muted">Previous Article</small>
                            <h6 class="mb-0 fw-bold">Annual General Meeting 2024</h6>
                        </div>
                    </a>
                </div>
                <div class="col-md-6 text-md-end mt-3 mt-md-0">
                    <a href="#" class="nav-link-next d-flex align-items-center text-decoration-none justify-content-md-end">
                        <div class="order-md-2">
                            <div class="nav-icon ms-3">
                                <i class="fas fa-chevron-right fa-2x text-primary"></i>
                            </div>
                        </div>
                        <div class="order-md-1">
                            <small class="text-muted">Next Article</small>
                            <h6 class="mb-0 fw-bold">Environmental Compliance Workshop</h6>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </section>

   


    <script>
        // Initialize AOS
        AOS.init({
            duration: 1000,
            once: true
        });

     
        // Share buttons functionality
        document.querySelectorAll('.share-buttons a').forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const platform = this.querySelector('i').classList[1].split('-')[1];
                alert(`Sharing on ${platform}... This feature will be implemented with actual social media integration.`);
            });
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Reading progress indicator
        window.addEventListener('scroll', function() {
            const article = document.querySelector('.news-article');
            if (article) {
                const articleTop = article.offsetTop;
                const articleHeight = article.offsetHeight;
                const windowHeight = window.innerHeight;
                const scrollTop = window.pageYOffset;

                const progress = Math.min(100, Math.max(0,
                    ((scrollTop - articleTop + windowHeight) / articleHeight) * 100
                ));

                // You can use this progress value to show a reading progress bar
                // For now, we'll just log it
                if (progress > 0 && progress < 100) {
                    console.log(`Reading progress: ${Math.round(progress)}%`);
                }
            }
        });
    </script>
@endsection