@extends('layouts.app')

@section('title', $member->name . ' - Urla Industries Association')

@section('content')
    <!-- Page Header -->
    <section class="page-header" style="background: url('image/header.jpg'); background-size: cover; background-position: center; background-attachment: fixed;">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <h1 class="display-4 fw-bold mb-3">{{ $member->name }}</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ url('/') }}">Home</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('uia-members') }}">UIA Members</a></li>
                            <li class="breadcrumb-item active" aria-current="page">{{ $member->name }}</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </section>

    <!-- Member Details Section -->
    <section class="py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mb-4">
                    <div class="profile-card">
                        <div class="company-details">
                            <div class="detail-item mb-4">
                                <h5><i class="fas fa-map-marker-alt me-2 text-primary"></i>Address:</h5>
                                <p class="text-muted">
                                    @if($member->full_address)
                                        {!! nl2br(e($member->full_address)) !!}
                                    @else
                                        {{ $member->address }}
                                    @endif
                                </p>
                            </div>

                            @if($member->description)
                                <div class="detail-item mb-4">
                                    <h5><i class="fas fa-info-circle me-2 text-primary"></i>Description:</h5>
                                    <p class="text-muted">
                                        {{ $member->description }}
                                    </p>
                                </div>
                            @endif

                            

                            @if($member->website)
                                <div class="detail-item mb-4">
                                    <h5><i class="fas fa-globe me-2 text-primary"></i>Website:</h5>
                                    <p class="text-muted">
                                        <a href="{{ $member->website }}" target="_blank">{{ $member->website }}</a>
                                    </p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <!-- Primary Contact Information -->
                    <div class="profile-card mb-4">
                        <div class="contact-header mb-4">
                            <h4 class="mb-2"><i class="fas fa-address-book me-2 text-primary"></i>Contact Information</h4>
                            <p class="text-muted small mb-0">Primary contact details for {{ $member->name }}</p>
                        </div>

                        <!-- Email Section -->
                        @if($member->emails && count($member->emails) > 0)
                            <div class="contact-section mb-4">
                                <div class="contact-section-header d-flex align-items-center mb-3">
                                    <div class="contact-icon me-3">
                                        <i class="fas fa-envelope"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-0 fw-bold">Email{{ count($member->emails) > 1 ? ' Addresses' : ' Address' }}</h6>
                                        <small class="text-muted">{{ count($member->emails) }} email{{ count($member->emails) > 1 ? 's' : '' }} available</small>
                                    </div>
                                </div>
                                <div class="contact-list">
                                    @foreach($member->emails as $index => $email)
                                        <div class="contact-item d-flex align-items-center mb-2">
                                            <span class="contact-badge me-2">{{ $index + 1 }}</span>
                                            <a href="mailto:{{ $email }}" class="contact-link flex-grow-1">{{ $email }}</a>
                                            <i class="fas fa-external-link-alt text-muted ms-2"></i>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endif

                        <!-- Phone Section -->
                        @if($member->phones && count($member->phones) > 0)
                            <div class="contact-section mb-4">
                                <div class="contact-section-header d-flex align-items-center mb-3">
                                    <div class="contact-icon me-3">
                                        <i class="fas fa-phone"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-0 fw-bold">Phone{{ count($member->phones) > 1 ? ' Numbers' : ' Number' }}</h6>
                                        <small class="text-muted">{{ count($member->phones) }} number{{ count($member->phones) > 1 ? 's' : '' }} available</small>
                                    </div>
                                </div>
                                <div class="contact-list">
                                    @foreach($member->phones as $index => $phone)
                                        <div class="contact-item d-flex align-items-center mb-2">
                                            <span class="contact-badge me-2">{{ $index + 1 }}</span>
                                            <a href="tel:{{ $phone }}" class="contact-link flex-grow-1">{{ $phone }}</a>
                                            <i class="fas fa-phone-alt text-muted ms-2"></i>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endif

                      
                    </div>

                    @if($member->contact_persons && count($member->contact_persons) > 0)
                        <div class="profile-card mb-4">
                            <h4 class="mb-3"><i class="fas fa-user-friends me-2 text-primary"></i>Contact Persons</h4>
                            <div class="contact-persons">
                                @foreach($member->contact_persons as $contact)
                                    <div class="contact-person mb-3">
                                        <h6 class="fw-bold">{{ $contact['name'] ?? 'N/A' }}</h6>
                                            @if(!empty($contact['phone']))
                                            <p class="text-muted mb-0">
                                                <i class="fas fa-phone me-2"></i>{{ $contact['phone'] }}
                                            </p>
                                            @endif
                                        
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </section>
@endsection