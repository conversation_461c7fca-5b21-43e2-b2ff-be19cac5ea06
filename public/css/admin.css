/* Admin Panel CSS for UIA */

:root {
    --admin-primary: #db231a;
    --admin-secondary: #2c3e50;
    --admin-success: #27ae60;
    --admin-warning: #f39c12;
    --admin-danger: #e74c3c;
    --admin-info: #3498db;
    --admin-light: #ecf0f1;
    --admin-dark: #2c3e50;
    --admin-white: #ffffff;
    --admin-gray-100: #f8f9fa;
    --admin-gray-200: #e9ecef;
    --admin-gray-300: #dee2e6;
    --admin-gray-400: #ced4da;
    --admin-gray-500: #adb5bd;
    --admin-gray-600: #6c757d;
    --admin-gray-700: #495057;
    --admin-gray-800: #343a40;
    --admin-gray-900: #212529;
    --admin-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --admin-shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

/* Admin Login Page Styles */
.admin-login-wrapper {
    min-height: 100vh;
    background: linear-gradient(135deg, var(--admin-primary) 0%, var(--admin-secondary) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.admin-login-container {
    background: var(--admin-white);
    border-radius: 15px;
    box-shadow: var(--admin-shadow-lg);
    overflow: hidden;
    max-width: 900px;
    width: 100%;
    min-height: 500px;
}

.admin-login-left {
    background: linear-gradient(135deg, var(--admin-primary) 0%, #c41e3a 100%);
    color: var(--admin-white);
    padding: 60px 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.admin-login-left::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    animation: float 20s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.admin-logo {
    width: 80px;
    height: 80px;
    margin-bottom: 20px;
    border-radius: 50%;
    background: var(--admin-white);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: var(--admin-primary);
    font-weight: bold;
    position: relative;
    z-index: 2;
}

.admin-login-right {
    padding: 60px 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.admin-form-title {
    color: var(--admin-dark);
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 10px;
    text-align: center;
}

.admin-form-subtitle {
    color: var(--admin-gray-600);
    text-align: center;
    margin-bottom: 40px;
}

.admin-form-group {
    margin-bottom: 25px;
    position: relative;
}

.admin-form-label {
    display: block;
    margin-bottom: 8px;
    color: var(--admin-gray-700);
    font-weight: 500;
    font-size: 0.9rem;
}

.admin-form-control {
    width: 100%;
    padding: 15px 20px;
    border: 2px solid var(--admin-gray-300);
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: var(--admin-white);
}

.admin-form-control:focus {
    outline: none;
    border-color: var(--admin-primary);
    box-shadow: 0 0 0 3px rgba(219, 35, 26, 0.1);
}

.admin-form-control.is-invalid {
    border-color: var(--admin-danger);
}

.admin-invalid-feedback {
    color: var(--admin-danger);
    font-size: 0.875rem;
    margin-top: 5px;
    display: block;
}

.admin-form-check {
    display: flex;
    align-items: center;
    margin-bottom: 25px;
}

.admin-form-check-input {
    margin-right: 10px;
    transform: scale(1.2);
}

.admin-form-check-label {
    color: var(--admin-gray-600);
    font-size: 0.9rem;
}

.admin-btn-primary {
    background: linear-gradient(135deg, var(--admin-primary) 0%, #c41e3a 100%);
    border: none;
    color: var(--admin-white);
    padding: 15px 30px;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 600;
    width: 100%;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.admin-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(219, 35, 26, 0.3);
}

.admin-btn-primary:active {
    transform: translateY(0);
}

.admin-alert {
    padding: 15px 20px;
    border-radius: 10px;
    margin-bottom: 25px;
    border: none;
}

.admin-alert-success {
    background: rgba(39, 174, 96, 0.1);
    color: var(--admin-success);
    border-left: 4px solid var(--admin-success);
}

.admin-alert-danger {
    background: rgba(231, 76, 60, 0.1);
    color: var(--admin-danger);
    border-left: 4px solid var(--admin-danger);
}

/* Responsive Design */
@media (max-width: 768px) {
    .admin-login-container {
        margin: 10px;
        border-radius: 10px;
    }
    
    .admin-login-left,
    .admin-login-right {
        padding: 40px 30px;
    }
    
    .admin-form-title {
        font-size: 1.5rem;
    }
    
    .admin-logo {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
}

@media (max-width: 576px) {
    .admin-login-left,
    .admin-login-right {
        padding: 30px 20px;
    }
    
    .admin-form-control {
        padding: 12px 15px;
    }
    
    .admin-btn-primary {
        padding: 12px 25px;
    }
}

/* Admin Login Body */
.admin-login-body {
    background: linear-gradient(135deg, var(--admin-primary) 0%, var(--admin-secondary) 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.admin-login-wrapper {
    background: var(--admin-white);
    border-radius: 15px;
    box-shadow: var(--admin-shadow-lg);
    overflow: hidden;
    max-width: 900px;
    width: 100%;
    min-height: 500px;
}

/* Admin Dashboard Styles */
.admin-sidebar {
    background: linear-gradient(180deg, var(--admin-dark) 0%, #1a2530 100%);
    min-height: 100vh;
    width: 260px;
    position: fixed;
    left: 0;
    top: 0;
    z-index: 1000;
    transition: all 0.3s ease;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.2);
    padding: 20px 0;
}

.admin-sidebar-header {
    padding: 0 20px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    margin-bottom: 20px;
}

.admin-logo {
    width: 45px;
    height: 45px;
    border-radius: 12px;
    background: var(--admin-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: var(--admin-white);
    font-weight: bold;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.admin-sidebar-content {
    padding: 0 15px;
}

.admin-sidebar .nav-link {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    margin-bottom: 5px;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-weight: 500;
    color: var(--admin-gray-400);
    text-decoration: none;
}

.admin-sidebar .nav-link:hover {
    background: rgba(255, 255, 255, 0.05);
    color: var(--admin-white);
}

.admin-sidebar .nav-link.active {
    background: var(--admin-primary);
    color: var(--admin-white);
    box-shadow: 0 2px 5px rgba(219, 35, 26, 0.3);
}

.admin-sidebar .nav-link i {
    width: 24px;
    text-align: center;
    font-size: 1.1rem;
}

.sidebar-divider {
    border-color: rgba(255, 255, 255, 0.1);
    margin: 20px 0;
}

.admin-main-content {
    margin-left: 260px;
    min-height: 100vh;
    background: var(--admin-gray-100);
    transition: all 0.3s ease;
}

.admin-topbar {
    background: var(--admin-white);
    padding: 15px 30px;
    box-shadow: var(--admin-shadow);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 999;
}

.page-title {
    color: var(--admin-dark);
    font-weight: 600;
    font-size: 1.4rem;
}

.user-dropdown .dropdown-toggle {
    display: flex;
    align-items: center;
    color: var(--admin-dark);
    text-decoration: none;
    padding: 8px 12px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.user-dropdown .dropdown-toggle:hover {
    background: var(--admin-gray-200);
}

.user-dropdown .dropdown-toggle:focus {
    box-shadow: none;
}

.user-name {
    font-weight: 500;
}

.admin-content {
    padding: 30px;
}

.admin-card {
    background: var(--admin-white);
    border-radius: 12px;
    box-shadow: var(--admin-shadow);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border: none;
    transition: all 0.3s ease;
    overflow: hidden;
}

.admin-card:hover {
    box-shadow: var(--admin-shadow-lg);
    transform: translateY(-2px);
}

.admin-card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid var(--admin-gray-200);
    padding: 1.5rem;
    margin: 0;
}

.admin-card-body {
    padding: 1.5rem;
}

.admin-card-title {
    color: var(--admin-dark);
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0;
    display: flex;
    align-items: center;
}

.admin-card-title i {
    margin-right: 0.5rem;
    color: var(--admin-primary);
}

/* Mobile Responsive for Dashboard */
@media (max-width: 768px) {
    .admin-sidebar {
        transform: translateX(-100%);
        width: 240px;
    }

    .admin-sidebar.show {
        transform: translateX(0);
    }

    .admin-main-content {
        margin-left: 0;
    }

    .admin-content {
        padding: 20px 15px;
    }

    .admin-topbar {
        padding: 15px 20px;
    }

    .page-title {
        font-size: 1.2rem;
    }
}
/* Enhanced mobile experience */
@media (max-width: 768px) {
    body.sidebar-open {
        overflow: hidden;
    }

    body.sidebar-open::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 999;
    }
}

/* Dashboard Enhancements */
.admin-card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.admin-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.counter {
    font-weight: 700;
    color: #2c3e50;
}

.text-purple {
    color: #6f42c1 !important;
}

/* Chart containers */
.admin-card canvas {
    max-height: 300px;
}

/* Fixed height chart containers */
.chart-container {
    position: relative !important;
    width: 100% !important;
    overflow: hidden;
}

.chart-container canvas {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    max-height: none !important;
}

/* Dashboard chart specific styling */
#monthlyGrowthChart,
#contentDistributionChart,
#activityTimelineChart {
    width: 100% !important;
    height: 100% !important;
}

/* Chart responsiveness */
@media (max-width: 768px) {
    .chart-container {
        height: 300px !important;
    }
}

@media (max-width: 576px) {
    .chart-container {
        height: 250px !important;
    }
}

/* Stats card improvements */
.admin-card .fa-2x {
    opacity: 0.8;
}

.admin-card small {
    font-size: 0.75rem;
    font-weight: 500;
}

/* Badge improvements */
.badge {
    font-size: 0.7rem;
    font-weight: 600;
}

/* Quick actions improvements */
.btn {
    transition: all 0.2s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

/* Loading animation for counters */
@keyframes countUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.counter {
    animation: countUp 0.6s ease-out;
}

/* Dashboard Loading Overlay */
.dashboard-loader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.3s ease;
}

.dashboard-loader.hidden {
    opacity: 0;
    pointer-events: none;
}

/* Enhanced responsive design */
@media (max-width: 1200px) {
    .admin-content {
        padding: 20px 15px;
    }

    .admin-card {
        margin-bottom: 1rem;
    }
}

@media (max-width: 992px) {
    .col-xl-3 {
        margin-bottom: 1rem;
    }

    .admin-card .fa-2x {
        font-size: 1.5rem;
    }

    .admin-card h3 {
        font-size: 1.5rem;
    }
}

@media (max-width: 768px) {
    .admin-card canvas {
        max-height: 250px;
    }

    .admin-card {
        padding: 1.25rem !important;
        margin-bottom: 1.5rem !important;
    }

    .admin-card-header {
        padding: 1.25rem !important;
    }

    .admin-card-body {
        padding: 1.25rem !important;
    }

    .admin-card-title {
        font-size: 1.1rem;
        margin-bottom: 1rem;
    }

    .counter {
        font-size: 1.25rem;
    }

    .btn {
        font-size: 0.875rem;
        padding: 0.375rem 0.75rem;
    }

    .table-responsive {
        font-size: 0.875rem;
    }

    .badge {
        font-size: 0.65rem;
    }
}

@media (max-width: 576px) {
    .admin-content {
        padding: 15px 10px;
    }

    .admin-card {
        padding: 1rem !important;
        margin-bottom: 1rem !important;
    }

    .admin-card-header {
        padding: 1rem !important;
    }

    .admin-card-body {
        padding: 1rem !important;
    }

    .admin-card h3 {
        font-size: 1.25rem;
    }

    .admin-card .fa-2x {
        font-size: 1.25rem;
    }

    .admin-card-title {
        font-size: 1rem;
        margin-bottom: 0.75rem;
    }

    .btn {
        font-size: 0.8rem;
        padding: 0.25rem 0.5rem;
    }

    .d-grid .btn {
        padding: 0.5rem;
        margin-bottom: 0.5rem;
    }
}

/* Loading states for cards */
.admin-card.loading {
    position: relative;
    overflow: hidden;
}

.admin-card.loading::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* Error states */
.admin-card.error {
    border-left: 4px solid #dc3545;
}

.admin-card.error .card-body {
    background-color: rgba(220, 53, 69, 0.05);
}

/* Success states */
.admin-card.success {
    border-left: 4px solid #28a745;
}

.admin-card.success .card-body {
    background-color: rgba(40, 167, 69, 0.05);
}

/* Enhanced Table Styles */
.admin-table-responsive {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 1rem;
}

.admin-table {
    width: 100%;
    margin-bottom: 0;
    background-color: #fff;
    border-collapse: separate;
    border-spacing: 0;
}

.admin-table thead {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.admin-table thead th {
    padding: 1rem 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.5px;
    border: none;
    position: relative;
    white-space: nowrap;
}

.admin-table thead th:first-child {
    border-top-left-radius: 8px;
}

.admin-table thead th:last-child {
    border-top-right-radius: 8px;
}

.admin-table tbody tr {
    transition: all 0.2s ease;
    border-bottom: 1px solid #e9ecef;
}

.admin-table tbody tr:hover {
    background-color: #f8f9fa;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.admin-table tbody tr:last-child {
    border-bottom: none;
}

.admin-table tbody td {
    padding: 1rem 0.75rem;
    vertical-align: middle;
    border: none;
    font-size: 0.875rem;
}

.admin-table tbody td:first-child {
    font-weight: 500;
}

/* Table Action Buttons */
.admin-table .btn-group {
    display: flex;
    gap: 0.25rem;
}

.admin-table .btn-sm {
    padding: 0.375rem 0.5rem;
    font-size: 0.75rem;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.admin-table .btn-sm:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Status Badges */
.admin-table .badge {
    font-size: 0.7rem;
    padding: 0.35rem 0.65rem;
    font-weight: 500;
    border-radius: 12px;
}

/* Image Thumbnails */
.admin-table .img-thumbnail {
    border-radius: 6px;
    border: 2px solid #e9ecef;
    transition: all 0.2s ease;
}

.admin-table .img-thumbnail:hover {
    border-color: #007bff;
    transform: scale(1.05);
}

/* Sortable Headers */
.admin-table thead th.sortable {
    cursor: pointer;
    user-select: none;
}

.admin-table thead th.sortable:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.admin-table thead th.sortable::after {
    content: '\f0dc';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    margin-left: 0.5rem;
    opacity: 0.5;
    font-size: 0.7rem;
}

.admin-table thead th.sortable.asc::after {
    content: '\f0de';
    opacity: 1;
}

.admin-table thead th.sortable.desc::after {
    content: '\f0dd';
    opacity: 1;
}

/* Table Filters */
.table-filters {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    border: 1px solid #e9ecef;
}

.table-filters .form-control,
.table-filters .form-select {
    border-radius: 6px;
    border: 1px solid #ced4da;
    font-size: 0.875rem;
}

.table-filters .btn {
    border-radius: 6px;
    font-size: 0.875rem;
}

/* Bulk Actions */
.bulk-actions {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    padding: 0.75rem 1rem;
    margin-bottom: 1rem;
    display: none;
}

.bulk-actions.show {
    display: block;
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Table Stats */
.table-stats {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.table-stat {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 0.75rem 1rem;
    text-align: center;
    min-width: 120px;
}

.table-stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #495057;
}

.table-stat-label {
    font-size: 0.75rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Empty State */
.table-empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

.table-empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.table-empty-state h5 {
    margin-bottom: 0.5rem;
    color: #495057;
}

.table-empty-state p {
    margin-bottom: 1.5rem;
}

/* Loading State */
.table-loading {
    position: relative;
    overflow: hidden;
}

.table-loading::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: shimmer 1.5s infinite;
    z-index: 1;
}

/* Responsive Design */
@media (max-width: 768px) {
    .admin-table-responsive {
        border-radius: 0;
        margin: 0 -15px 1rem -15px;
    }

    .admin-table thead th,
    .admin-table tbody td {
        padding: 0.75rem 0.5rem;
        font-size: 0.8rem;
    }

    .admin-table .btn-group {
        flex-direction: column;
        gap: 0.125rem;
    }

    .admin-table .btn-sm {
        padding: 0.25rem 0.375rem;
        font-size: 0.7rem;
    }

    .table-filters {
        margin: 0 -15px 1rem -15px;
        border-radius: 0;
    }

    .table-stats {
        margin: 0 -15px 1rem -15px;
        padding: 0 15px;
    }

    .table-stat {
        flex: 1;
        min-width: auto;
    }
}

@media (max-width: 576px) {
    .admin-table {
        font-size: 0.75rem;
    }

    .admin-table thead th {
        padding: 0.5rem 0.25rem;
        font-size: 0.7rem;
    }

    .admin-table tbody td {
        padding: 0.5rem 0.25rem;
    }

    .admin-table .img-thumbnail {
        width: 40px !important;
        height: 30px !important;
    }
}

/* Enhanced Page Header Styles */
.admin-page-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
    border: 1px solid var(--admin-gray-200);
}

.page-header-content {
    flex: 1;
}

.page-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--admin-primary) 0%, #c41e3a 100%);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    box-shadow: 0 4px 15px rgba(219, 35, 26, 0.3);
}

.page-actions {
    display: flex;
    align-items: flex-start;
}

.page-breadcrumb .breadcrumb {
    background: none;
    padding: 0;
    margin: 0;
}

.page-breadcrumb .breadcrumb-item a {
    color: var(--admin-primary);
    text-decoration: none;
}

.page-breadcrumb .breadcrumb-item a:hover {
    text-decoration: underline;
}

/* Enhanced Stats Cards */
.stats-card {
    background: var(--admin-white);
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: var(--admin-shadow);
    border: 1px solid var(--admin-gray-200);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--admin-shadow-lg);
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--admin-primary), #c41e3a);
}

.stats-card-primary::before {
    background: linear-gradient(90deg, var(--admin-primary), #c41e3a);
}

.stats-card-success::before {
    background: linear-gradient(90deg, var(--admin-success), #2ecc71);
}

.stats-card-warning::before {
    background: linear-gradient(90deg, var(--admin-warning), #e67e22);
}

.stats-card-info::before {
    background: linear-gradient(90deg, var(--admin-info), #5dade2);
}

.stats-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: white;
    margin-bottom: 1rem;
}

.stats-card-primary .stats-icon {
    background: linear-gradient(135deg, var(--admin-primary), #c41e3a);
}

.stats-card-success .stats-icon {
    background: linear-gradient(135deg, var(--admin-success), #2ecc71);
}

.stats-card-warning .stats-icon {
    background: linear-gradient(135deg, var(--admin-warning), #e67e22);
}

.stats-card-info .stats-icon {
    background: linear-gradient(135deg, var(--admin-info), #5dade2);
}

.stats-content h3 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--admin-dark);
}

.stats-content p {
    font-size: 0.9rem;
    color: var(--admin-gray-600);
    margin-bottom: 0.5rem;
}

.stats-change {
    font-size: 0.8rem;
    color: var(--admin-gray-500);
}

.stats-change.text-success {
    color: var(--admin-success) !important;
}

/* Enhanced Admin Statistics Cards */
.admin-stat-card {
    background: linear-gradient(135deg, var(--admin-white) 0%, #f8f9fa 100%);
    border-radius: 20px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    padding: 2rem;
    margin-bottom: 1.5rem;
    border: none;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    text-align: center;
    height: 100%;
    position: relative;
    overflow: hidden;
    cursor: pointer;
}

.admin-stat-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.admin-stat-card-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.admin-stat-card:hover .admin-stat-card-overlay {
    transform: translateX(100%);
}

.admin-stat-card-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    margin: 0 auto 1.5rem;
    position: relative;
    z-index: 2;
    transition: all 0.3s ease;
}

.admin-stat-card:hover .admin-stat-card-icon {
    transform: scale(1.1) rotate(5deg);
}

.admin-stat-card-content {
    position: relative;
    z-index: 2;
}

.admin-stat-card-content h3 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--admin-dark);
    transition: all 0.3s ease;
}

.admin-stat-card:hover .admin-stat-card-content h3 {
    transform: scale(1.05);
}

.admin-stat-card-title {
    font-size: 1rem;
    color: var(--admin-gray-600);
    margin-bottom: 1rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.admin-stat-card-footer {
    margin-top: 1rem;
}

.admin-stat-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.85rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.admin-stat-badge-success {
    background: rgba(39, 174, 96, 0.1);
    color: var(--admin-success);
    border: 1px solid rgba(39, 174, 96, 0.2);
}

.admin-stat-badge-info {
    background: rgba(52, 152, 219, 0.1);
    color: var(--admin-info);
    border: 1px solid rgba(52, 152, 219, 0.2);
}

.admin-stat-badge-warning {
    background: rgba(243, 156, 18, 0.1);
    color: var(--admin-warning);
    border: 1px solid rgba(243, 156, 18, 0.2);
}

.admin-stat-badge-muted {
    background: rgba(108, 117, 125, 0.1);
    color: var(--admin-gray-600);
    border: 1px solid rgba(108, 117, 125, 0.2);
}

/* Color Variants */
.admin-stat-card-primary .admin-stat-card-icon {
    background: linear-gradient(135deg, var(--admin-primary), #c41e3a);
    box-shadow: 0 8px 20px rgba(219, 35, 26, 0.3);
}

.admin-stat-card-success .admin-stat-card-icon {
    background: linear-gradient(135deg, var(--admin-success), #2ecc71);
    box-shadow: 0 8px 20px rgba(39, 174, 96, 0.3);
}

.admin-stat-card-warning .admin-stat-card-icon {
    background: linear-gradient(135deg, var(--admin-warning), #e67e22);
    box-shadow: 0 8px 20px rgba(243, 156, 18, 0.3);
}

.admin-stat-card-info .admin-stat-card-icon {
    background: linear-gradient(135deg, var(--admin-info), #5dade2);
    box-shadow: 0 8px 20px rgba(52, 152, 219, 0.3);
}

.admin-stat-card-purple .admin-stat-card-icon {
    background: linear-gradient(135deg, #6f42c1, #8e44ad);
    box-shadow: 0 8px 20px rgba(111, 66, 193, 0.3);
}

.admin-stat-card-secondary .admin-stat-card-icon {
    background: linear-gradient(135deg, var(--admin-gray-600), var(--admin-gray-700));
    box-shadow: 0 8px 20px rgba(108, 117, 125, 0.3);
}

/* Responsive Design for Enhanced Stat Cards */
@media (max-width: 1200px) {
    .admin-stat-card {
        padding: 1.5rem;
    }

    .admin-stat-card-icon {
        width: 70px;
        height: 70px;
        font-size: 1.75rem;
    }

    .admin-stat-card-content h3 {
        font-size: 2.25rem;
    }
}

@media (max-width: 768px) {
    .admin-stat-card {
        padding: 1.25rem;
        margin-bottom: 1rem;
    }

    .admin-stat-card-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
        margin-bottom: 1rem;
    }

    .admin-stat-card-content h3 {
        font-size: 2rem;
    }

    .admin-stat-card-title {
        font-size: 0.9rem;
    }

    .admin-stat-badge {
        padding: 0.4rem 0.8rem;
        font-size: 0.8rem;
    }
}

@media (max-width: 576px) {
    .admin-stat-card {
        padding: 1rem;
    }

    .admin-stat-card-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }

    .admin-stat-card-content h3 {
        font-size: 1.75rem;
    }
}

.admin-stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--admin-primary), #c41e3a);
}

.admin-stat-card.bg-primary::before {
    background: linear-gradient(90deg, var(--admin-primary), #c41e3a);
}

.admin-stat-card.bg-success::before {
    background: linear-gradient(90deg, var(--admin-success), #2ecc71);
}

.admin-stat-card.bg-warning::before {
    background: linear-gradient(90deg, var(--admin-warning), #f1c40f);
}

.admin-stat-card.bg-info::before {
    background: linear-gradient(90deg, var(--admin-info), #5dade2);
}

.admin-stat-card.bg-secondary::before {
    background: linear-gradient(90deg, var(--admin-secondary), #7f8c8d);
}

.admin-stat-card.bg-dark::before {
    background: linear-gradient(90deg, var(--admin-dark), #34495e);
}

.admin-stat-card:hover {
    box-shadow: var(--admin-shadow-lg);
    transform: translateY(-5px);
}

.admin-stat-icon {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: var(--admin-white);
    width: 70px;
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    margin: 0 auto 1rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.admin-stat-card:hover .admin-stat-icon {
    transform: scale(1.1);
}

.admin-stat-content h3 {
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--admin-dark);
    transition: all 0.3s ease;
}

.admin-stat-content p {
    font-size: 0.95rem;
    color: var(--admin-gray-600);
    margin-bottom: 0;
    font-weight: 500;
}

/* Ads Filter Section */
.ads-filter-card {
    background: linear-gradient(135deg, var(--admin-white) 0%, #f8f9fa 100%);
    border-radius: 15px;
    box-shadow: var(--admin-shadow);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border: none;
    transition: all 0.3s ease;
}

.ads-filter-card:hover {
    box-shadow: var(--admin-shadow-lg);
}

.ads-filter-title {
    color: var(--admin-dark);
    font-weight: 600;
    margin-bottom: 1.5rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid var(--admin-gray-200);
    font-size: 1.25rem;
}

.ads-filter-form .form-label {
    font-weight: 500;
    color: var(--admin-gray-700);
    margin-bottom: 0.5rem;
}

.ads-filter-form .form-control,
.ads-filter-form .form-select {
    border: 2px solid var(--admin-gray-200);
    border-radius: 8px;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
    background: var(--admin-white);
}

.ads-filter-form .form-control:focus,
.ads-filter-form .form-select:focus {
    border-color: var(--admin-primary);
    box-shadow: 0 0 0 0.2rem rgba(219, 35, 26, 0.25);
    outline: none;
}

.ads-filter-form .btn {
    padding: 0.75rem 1.25rem;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.ads-filter-form .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.ads-filter-form .btn:active {
    transform: translateY(0);
}

/* Ads Table */
.ads-table-card {
    background: linear-gradient(135deg, var(--admin-white) 0%, #f8f9fa 100%);
    border-radius: 15px;
    box-shadow: var(--admin-shadow);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border: none;
    transition: all 0.3s ease;
}

.ads-table-card:hover {
    box-shadow: var(--admin-shadow-lg);
}

.ads-table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid var(--admin-gray-200);
}

.ads-table-title {
    color: var(--admin-dark);
    font-weight: 600;
    font-size: 1.25rem;
    margin-bottom: 0;
}

.ads-table {
    width: 100%;
    margin-bottom: 0;
    background-color: var(--admin-white);
    border-collapse: separate;
    border-spacing: 0;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--admin-shadow);
}

.ads-table thead th {
    background: linear-gradient(135deg, var(--admin-dark) 0%, #1a2530 100%);
    color: var(--admin-white);
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
    padding: 1.25rem 1rem;
    border: none;
}

.ads-table thead th:first-child {
    border-top-left-radius: 12px;
}

.ads-table thead th:last-child {
    border-top-right-radius: 12px;
}

.ads-table tbody tr {
    transition: all 0.3s ease;
    border-bottom: 1px solid var(--admin-gray-200);
}

.ads-table tbody tr:hover {
    background-color: rgba(219, 35, 26, 0.05);
    transform: scale(1.01);
}

.ads-table tbody td {
    padding: 1.25rem 1rem;
    vertical-align: middle;
    border: none;
    font-size: 0.9rem;
}

.ads-table tbody tr:last-child {
    border-bottom: none;
}

.ads-table .badge {
    font-size: 0.75rem;
    padding: 0.5em 0.8em;
    font-weight: 600;
    border-radius: 20px;
}

.ads-table .img-thumbnail {
    border-radius: 8px;
    border: 2px solid var(--admin-gray-200);
    transition: all 0.3s ease;
    width: 60px;
    height: 45px;
    object-fit: cover;
}

.ads-table .img-thumbnail:hover {
    border-color: var(--admin-primary);
    transform: scale(1.05);
}

.ads-table-responsive {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--admin-shadow);
    margin-bottom: 1.5rem;
}

/* Enhanced Card Styles */
.enhanced-card {
    border: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border-radius: 15px;
    overflow: hidden;
}

.card-header-enhanced {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid var(--admin-gray-200);
    padding: 1.5rem;
}

.card-title-section h5 {
    color: var(--admin-dark);
    font-weight: 600;
    margin-bottom: 0;
}

.card-subtitle {
    color: var(--admin-gray-600);
    font-size: 0.9rem;
    margin-bottom: 0;
}

.card-actions .btn-group .btn {
    border-radius: 8px;
}

.card-actions .btn.active {
    background-color: var(--admin-primary);
    border-color: var(--admin-primary);
    color: white;
}

/* Enhanced Button Styles */
.btn-enhanced {
    background: linear-gradient(135deg, var(--admin-primary) 0%, #c41e3a 100%);
    border: none;
    border-radius: 10px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    color: white;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(219, 35, 26, 0.3);
}

.btn-enhanced:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(219, 35, 26, 0.4);
    color: white;
}

/* Enhanced Table Styles */
.admin-table {
    width: 100%;
    margin-bottom: 1rem;
    color: var(--admin-dark);
    background-color: var(--admin-white);
    border-collapse: separate;
    border-spacing: 0;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--admin-shadow);
}

.admin-table thead th {
    background: linear-gradient(180deg, var(--admin-dark) 0%, #1a2530 100%);
    color: var(--admin-white);
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
    padding: 1.25rem 1.5rem;
    border: none;
}

.admin-table tbody tr {
    transition: all 0.3s ease;
}

.admin-table tbody tr:hover {
    background-color: rgba(219, 35, 26, 0.05);
}

.admin-table tbody td {
    padding: 1.25rem 1.5rem;
    border-top: 1px solid var(--admin-gray-200);
    vertical-align: middle;
}

.admin-table tbody tr:first-child td {
    border-top: none;
}

.admin-table .badge {
    font-size: 0.75rem;
    padding: 0.5em 0.8em;
    font-weight: 600;
    border-radius: 20px;
}

.admin-table .btn-group .btn {
    padding: 0.4rem 0.8rem;
    font-size: 0.875rem;
    border-radius: 6px;
    margin-right: 0.25rem;
    transition: all 0.2s ease;
}

.admin-table .btn-group .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.admin-table .img-thumbnail {
    border-radius: 8px;
    border: 2px solid var(--admin-gray-200);
    transition: all 0.3s ease;
    width: 50px;
    height: 50px;
    object-fit: cover;
}

.admin-table .img-thumbnail:hover {
    border-color: var(--admin-primary);
    transform: scale(1.05);
}

.admin-table-responsive {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--admin-shadow);
    margin-bottom: 1.5rem;
}

/* Enhanced Form Styles */
.admin-form-control {
    width: 100%;
    padding: 15px 20px;
    border: 2px solid var(--admin-gray-300);
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: var(--admin-white);
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

.admin-form-control:focus {
    outline: none;
    border-color: var(--admin-primary);
    box-shadow: 0 0 0 3px rgba(219, 35, 26, 0.1);
}

.admin-form-control.is-invalid {
    border-color: var(--admin-danger);
}

.admin-form-label {
    display: block;
    margin-bottom: 8px;
    color: var(--admin-gray-700);
    font-weight: 500;
    font-size: 0.95rem;
}

.admin-form-group {
    margin-bottom: 1.5rem;
}

/* Duplicate admin-card styles removed - using the main definition above */

/* Enhanced Table Row Styles */
.table-row-enhanced {
    transition: all 0.3s ease;
    border-bottom: 1px solid var(--admin-gray-200);
}

.table-row-enhanced:hover {
    background-color: rgba(219, 35, 26, 0.05);
    transform: scale(1.01);
}

/* Image Preview Styles */
.image-preview-container,
.profile-preview-container {
    position: relative;
    display: inline-block;
    cursor: pointer;
}

.slider-preview-img,
.president-profile-img {
    width: 80px;
    height: 50px;
    object-fit: cover;
    border-radius: 8px;
    transition: all 0.3s ease;
    border: 2px solid var(--admin-gray-200);
}

.president-profile-img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
}

.image-overlay,
.profile-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
    border-radius: 8px;
    color: white;
}

.profile-overlay {
    border-radius: 50%;
}

.image-preview-container:hover .image-overlay,
.profile-preview-container:hover .profile-overlay {
    opacity: 1;
}

/* Content Preview Styles */
.content-preview,
.message-preview {
    max-width: 300px;
}

.content-title {
    font-weight: 600;
    color: var(--admin-dark);
    margin-bottom: 0.25rem;
    font-size: 0.95rem;
}

.content-subtitle {
    color: var(--admin-gray-600);
    font-size: 0.85rem;
    margin-bottom: 0.5rem;
}

.content-badge,
.slider-badge {
    background: linear-gradient(135deg, var(--admin-primary), #c41e3a);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    display: inline-block;
    margin-bottom: 0.5rem;
}

.content-meta {
    margin-top: 0.5rem;
}

.message-excerpt {
    font-size: 0.9rem;
    color: var(--admin-gray-700);
    line-height: 1.4;
    margin-bottom: 0.5rem;
}

.message-meta {
    font-size: 0.8rem;
    color: var(--admin-gray-500);
}

/* President Info Styles */
.president-info {
    min-width: 150px;
}

.president-name {
    font-weight: 600;
    color: var(--admin-dark);
    margin-bottom: 0.25rem;
    font-size: 0.95rem;
}

.president-position {
    color: var(--admin-gray-600);
    font-size: 0.85rem;
    margin-bottom: 0.25rem;
}

.president-tenure {
    color: var(--admin-gray-500);
    font-size: 0.75rem;
}

/* Order Badge Styles */
.order-badge-container {
    text-align: center;
}

.order-badge {
    background: linear-gradient(135deg, var(--admin-gray-600), var(--admin-gray-700));
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.85rem;
    min-width: 40px;
    display: inline-block;
}

/* Status Badge Styles */
.status-container {
    text-align: center;
}

.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.85rem;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.status-active {
    background: linear-gradient(135deg, var(--admin-success), #2ecc71);
    color: white;
}

.status-inactive {
    background: linear-gradient(135deg, var(--admin-warning), #e67e22);
    color: white;
}

/* Date Container Styles */
.date-container {
    text-align: center;
}

.date-primary {
    font-weight: 600;
    color: var(--admin-dark);
    display: block;
    font-size: 0.9rem;
}

.date-secondary {
    color: var(--admin-gray-500);
    font-size: 0.75rem;
}

/* Action Buttons Styles */
.action-buttons {
    display: flex;
    gap: 0.25rem;
    justify-content: center;
}

.btn-action {
    width: 35px;
    height: 35px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

.btn-view {
    background: linear-gradient(135deg, var(--admin-info), #5dade2);
    color: white;
}

.btn-view:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(61, 173, 226, 0.4);
    color: white;
}

.btn-edit {
    background: linear-gradient(135deg, var(--admin-primary), #c41e3a);
    color: white;
}

.btn-edit:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(219, 35, 26, 0.4);
    color: white;
}

.btn-delete {
    background: linear-gradient(135deg, var(--admin-danger), #c0392b);
    color: white;
}

.btn-delete:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(231, 76, 60, 0.4);
    color: white;
}

/* Enhanced Button Styles */
.btn {
    padding: 10px 20px;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn:active {
    transform: translateY(0);
}

/* Grid View Styles */
.view-container {
    transition: all 0.3s ease;
}

.slider-card,
.president-message-card {
    background: var(--admin-white);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: var(--admin-shadow);
    transition: all 0.3s ease;
    border: 1px solid var(--admin-gray-200);
}

.slider-card:hover,
.president-message-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--admin-shadow-lg);
}

.slider-card-image {
    position: relative;
    overflow: hidden;
}

.card-img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: all 0.3s ease;
}

.slider-card:hover .card-img {
    transform: scale(1.05);
}

.slider-card-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
}

.slider-card:hover .slider-card-overlay {
    opacity: 1;
}

.overlay-content {
    text-align: center;
    color: white;
}

.overlay-status {
    display: block;
    margin-bottom: 1rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
}

.overlay-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

.slider-order-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
}

.slider-card-body {
    padding: 1.5rem;
}

.slider-card-title {
    font-weight: 600;
    color: var(--admin-dark);
    margin-bottom: 0.5rem;
}

.slider-card-subtitle {
    color: var(--admin-gray-600);
    font-size: 0.9rem;
    margin-bottom: 0.75rem;
}

.slider-card-meta {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--admin-gray-200);
}

/* President Message Card Styles */
.card-header-section {
    padding: 1.5rem;
    border-bottom: 1px solid var(--admin-gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.president-profile {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.profile-image {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--admin-gray-200);
}

.president-details h6 {
    margin-bottom: 0.25rem;
    font-weight: 600;
    color: var(--admin-dark);
}

.president-details p {
    margin-bottom: 0;
    color: var(--admin-gray-600);
    font-size: 0.9rem;
}

.card-body-section {
    padding: 1.5rem;
}

.card-footer-section {
    padding: 1rem 1.5rem;
    background: var(--admin-gray-100);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.card-actions {
    display: flex;
    gap: 0.25rem;
}

/* Empty State Styles */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    background: var(--admin-white);
    border-radius: 15px;
    border: 2px dashed var(--admin-gray-300);
}

.empty-state-content {
    max-width: 500px;
    margin: 0 auto;
}

.empty-state-icon {
    width: 100px;
    height: 100px;
    margin: 0 auto 2rem;
    background: linear-gradient(135deg, var(--admin-gray-300), var(--admin-gray-400));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5rem;
    color: white;
}

.empty-state-title {
    color: var(--admin-dark);
    font-weight: 600;
    margin-bottom: 1rem;
}

.empty-state-description {
    color: var(--admin-gray-600);
    margin-bottom: 2rem;
    line-height: 1.6;
}

.empty-state-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Enhanced Filters Styles */
.enhanced-filters {
    margin-bottom: 2rem;
}

/* Enhanced Action Buttons */
.btn-action {
    width: 36px;
    height: 36px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    margin: 0 2px;
}

.btn-action:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.btn-action:active {
    transform: translateY(0);
}

.btn-view {
    background: linear-gradient(135deg, var(--admin-info) 0%, #5dade2 100%);
    color: white;
}

.btn-view:hover {
    background: linear-gradient(135deg, #5dade2 0%, var(--admin-info) 100%);
    color: white;
}

.btn-edit {
    background: linear-gradient(135deg, var(--admin-primary) 0%, #c41e3a 100%);
    color: white;
}

.btn-edit:hover {
    background: linear-gradient(135deg, #c41e3a 0%, var(--admin-primary) 100%);
    color: white;
}

.btn-delete {
    background: linear-gradient(135deg, var(--admin-danger) 0%, #c0392b 100%);
    color: white;
}

.btn-delete:hover {
    background: linear-gradient(135deg, #c0392b 0%, var(--admin-danger) 100%);
    color: white;
}

.btn-bulk {
    background: linear-gradient(135deg, var(--admin-warning) 0%, #f39c12 100%);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-bulk:hover {
    background: linear-gradient(135deg, #f39c12 0%, var(--admin-warning) 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    color: white;
}

.btn-bulk:active {
    transform: translateY(0);
}

/* Ads Management Responsive Design */
@media (max-width: 768px) {
    .ads-filter-card {
        padding: 1rem;
    }
    
    .ads-filter-form .col-md-3,
    .ads-filter-form .col-md-2 {
        margin-bottom: 1rem;
    }
    
    .ads-table-card {
        padding: 1rem;
    }
    
    .ads-table-responsive {
        font-size: 0.8rem;
    }
    
    .ads-table thead th {
        padding: 1rem;
    }
    
    .ads-table tbody td {
        padding: 1rem;
    }
    
    .btn-action {
        width: 30px;
        height: 30px;
        font-size: 0.8rem;
    }
    
    .ads-stat-card {
        margin-bottom: 1rem;
    }
    
    .ads-stat-content h3 {
        font-size: 1.25rem;
    }
    
    .ads-stat-content p {
        font-size: 0.85rem;
    }
}

@media (max-width: 576px) {
    .ads-filter-form .col-md-3,
    .ads-filter-form .col-md-2 {
        margin-bottom: 0.75rem;
    }
    
    .ads-filter-form .btn {
        padding: 0.5rem 1rem;
        font-size: 0.85rem;
    }
    
    .ads-table thead th {
        padding: 0.75rem 0.5rem;
        font-size: 0.75rem;
    }
    
    .ads-table tbody td {
        padding: 0.75rem 0.5rem;
        font-size: 0.8rem;
    }
    
    .ads-table .btn-group {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
    }
    
    .btn-action {
        width: 100%;
        height: 35px;
        font-size: 0.85rem;
        margin-bottom: 0.25rem;
    }
    
    .ads-stat-card {
        margin-bottom: 0.75rem;
    }
    
    .ads-stat-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }
    
    .ads-stat-content h3 {
        font-size: 1.1rem;
    }
    
    .ads-stat-content p {
        font-size: 0.8rem;
    }
}

.filter-content {
    padding: 1.5rem;
}

.filter-group {
    margin-bottom: 0;
}

.filter-group label {
    font-weight: 600;
    color: var(--admin-dark);
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
}

.enhanced-select,
.enhanced-input {
    border: 2px solid var(--admin-gray-200);
    border-radius: 8px;
    padding: 0.75rem;
    transition: all 0.3s ease;
    background: var(--admin-white);
}

.enhanced-select:focus,
.enhanced-input:focus {
    border-color: var(--admin-primary);
    box-shadow: 0 0 0 0.2rem rgba(219, 35, 26, 0.25);
    outline: none;
}

/* Preview Cards */
.preview-message-card {
    background: var(--admin-white);
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid var(--admin-gray-200);
    box-shadow: var(--admin-shadow);
}

/* Responsive Enhancements */
@media (max-width: 768px) {
    .admin-page-header {
        padding: 1.5rem;
    }

    .page-header-content .d-flex {
        flex-direction: column;
        align-items: flex-start !important;
    }

    .page-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }

    .stats-card {
        margin-bottom: 1rem;
    }

    .page-actions {
        width: 100%;
        margin-top: 1rem;
    }

    .page-actions .d-flex {
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .empty-state {
        padding: 2rem 1rem;
    }

    .empty-state-icon {
        width: 80px;
        height: 80px;
        font-size: 2rem;
    }

    .empty-state-actions {
        flex-direction: column;
        align-items: center;
    }

    .empty-state-actions .btn {
        width: 100%;
        max-width: 250px;
    }
}

.btn-primary {
    background: linear-gradient(135deg, var(--admin-primary) 0%, #c41e3a 100%);
    color: var(--admin-white);
    border: none;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #c41e3a 0%, var(--admin-primary) 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(219, 35, 26, 0.3);
}

/* Enhanced Form Styling */
.form-control {
    border: 2px solid var(--admin-gray-300);
    border-radius: 8px;
    padding: 0.75rem 1rem;
    font-size: 0.95rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--admin-primary);
    box-shadow: 0 0 0 0.2rem rgba(219, 35, 26, 0.15);
}

.form-select {
    border: 2px solid var(--admin-gray-300);
    border-radius: 8px;
    padding: 0.75rem 1rem;
    font-size: 0.95rem;
    transition: all 0.3s ease;
}

.form-select:focus {
    border-color: var(--admin-primary);
    box-shadow: 0 0 0 0.2rem rgba(219, 35, 26, 0.15);
}

.form-label {
    font-weight: 600;
    color: var(--admin-dark);
    margin-bottom: 0.5rem;
}

.form-text {
    color: var(--admin-gray-600);
    font-size: 0.875rem;
}

/* File Upload Styling */
.form-control[type="file"] {
    padding: 0.5rem;
}

.form-control[type="file"]::-webkit-file-upload-button {
    background: var(--admin-primary);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    margin-right: 1rem;
    cursor: pointer;
    font-weight: 500;
}

.form-control[type="file"]::-webkit-file-upload-button:hover {
    background: #c41e3a;
}

/* Preview Area Styling */
#preview-area {
    background: var(--admin-gray-100);
    border: 2px dashed var(--admin-gray-300);
    border-radius: 8px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
}

#preview-area.has-content {
    border-color: var(--admin-success);
    background: rgba(39, 174, 96, 0.05);
}

/* Enhanced Button Styling */
.btn-outline-secondary {
    border: 2px solid var(--admin-gray-400);
    color: var(--admin-gray-700);
    font-weight: 600;
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
    transition: all 0.3s ease;
}

.btn-outline-secondary:hover {
    background: var(--admin-gray-700);
    border-color: var(--admin-gray-700);
    color: white;
    transform: translateY(-1px);
}

.btn-secondary {
    background: var(--admin-gray-600);
    color: var(--admin-white);
}

.btn-success {
    background: var(--admin-success);
    color: var(--admin-white);
}

.btn-danger {
    background: var(--admin-danger);
    color: var(--admin-white);
}

.btn-info {
    background: var(--admin-info);
    color: var(--admin-white);
}

.btn-outline-primary {
    border: 1px solid var(--admin-primary);
    color: var(--admin-primary);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--admin-primary);
    color: var(--admin-white);
}

.btn-outline-secondary {
    border: 1px solid var(--admin-gray-600);
    color: var(--admin-gray-600);
    background: transparent;
}

.btn-outline-success {
    border: 1px solid var(--admin-success);
    color: var(--admin-success);
    background: transparent;
}

.btn-outline-danger {
    border: 1px solid var(--admin-danger);
    color: var(--admin-danger);
    background: transparent;
}

.btn-outline-info {
    border: 1px solid var(--admin-info);
    color: var(--admin-info);
    background: transparent;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 0.875rem;
}

/* Enhanced Responsive Design */
@media (max-width: 768px) {
    .admin-table-responsive {
        font-size: 0.9rem;
    }

    .admin-table thead th {
        padding: 0.75rem;
        font-size: 0.875rem;
    }

    .admin-table tbody td {
        padding: 0.75rem;
        font-size: 0.875rem;
    }

    /* Remove conflicting admin-card styles - handled above */
}

@media (max-width: 576px) {
    .admin-table thead th {
        padding: 0.75rem 0.5rem;
        font-size: 0.75rem;
    }
    
    .admin-table tbody td {
        padding: 0.75rem 0.5rem;
        font-size: 0.85rem;
    }
    
    .admin-table .btn-group .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
    
    .admin-form-control {
        padding: 12px 15px;
    }
    
    .admin-form-label {
        font-size: 0.9rem;
    }
}

/* Improved transitions */
.admin-sidebar {
    transition: transform 0.3s ease;
}

.admin-main-content {
    transition: margin-left 0.3s ease;
}
}
