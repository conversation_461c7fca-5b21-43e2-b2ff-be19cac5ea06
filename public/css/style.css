/* Custom CSS for UIA Website */

:root {
    --primary-color: #db231a;
    --secondary-color: #f8f9fa;
    --accent-color: #ff6b35;
    --text-dark: #333;
    --text-light: #666;
    --vh: 1vh;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    padding-top: 0;
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Prevent horizontal scroll */
/* .container-fluid,
.container {
    overflow-x: hidden;
} */

/* Ensure images are responsive */
img {
    max-width: 100%;
    height: auto;
}

/* Responsive tables */
.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

/* Mobile-first responsive utilities */
.w-100 { width: 100% !important; }
.h-100 { height: 100% !important; }

/* Responsive text alignment */
@media (max-width: 767px) {
    .text-center-mobile { text-align: center !important; }
    .text-left-mobile { text-align: left !important; }
}

/* Responsive spacing utilities */
@media (max-width: 767px) {
    .p-mobile-2 { padding: 0.5rem !important; }
    .p-mobile-3 { padding: 1rem !important; }
    .m-mobile-2 { margin: 0.5rem !important; }
    .m-mobile-3 { margin: 1rem !important; }
    .mb-mobile-3 { margin-bottom: 1rem !important; }
    .mt-mobile-3 { margin-top: 1rem !important; }
}

/* Responsive display utilities */
@media (max-width: 767px) {
    .d-mobile-block { display: block !important; }
    .d-mobile-none { display: none !important; }
    .d-mobile-flex { display: flex !important; }
}

/* Quotation Page Styles */
.product-check {
    transition: all 0.3s ease;
    padding: 0.75rem;
    border-radius: 0.5rem;
    border: 2px solid transparent;
    background: #f8f9fa;
    margin-bottom: 0.5rem;
}

.product-check:hover {
    background: #e9ecef;
    border-color: var(--primary-color);
}

.product-check.selected {
    background: rgba(219, 35, 26, 0.1);
    border-color: var(--primary-color);
}

/* Office Bearers & Executive Committee Page Styles */
.bearer-year-card,
.committee-year-card {
    transition: all 0.3s ease;
}

.bearer-year-card:hover,
.committee-year-card:hover {
    transform: translateY(-5px);
}

.bearer-year-card .card-header,
.committee-year-card .card-header {
    position: relative;
    overflow: hidden;
}

.bearer-year-card .card-header::before,
.committee-year-card .card-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: left 0.5s ease;
}

.bearer-year-card:hover .card-header::before,
.committee-year-card:hover .card-header::before {
    left: 100%;
}

.download-btn {
    transition: all 0.3s ease;
    border-radius: 25px;
    padding: 8px 20px;
    font-weight: 500;
}

.download-btn:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.download-btn:active {
    transform: scale(0.98);
}

/* Badge styles for committee pages */
.badge.bg-primary-subtle {
    background-color: rgba(13, 110, 253, 0.1) !important;
    color: #0d6efd !important;
}

.badge.bg-success-subtle {
    background-color: rgba(25, 135, 84, 0.1) !important;
    color: #198754 !important;
}

.badge.bg-info-subtle {
    background-color: rgba(13, 202, 240, 0.1) !important;
    color: #0dcaf0 !important;
}

.badge.bg-warning-subtle {
    background-color: rgba(255, 193, 7, 0.1) !important;
    color: #ffc107 !important;
}

.badge.bg-danger-subtle {
    background-color: rgba(220, 53, 69, 0.1) !important;
    color: #dc3545 !important;
}

/* Committee Member Cards */
.committee-member-card {
    transition: all 0.3s ease;
}

.committee-member-card:hover {
    transform: translateY(-5px);
}

.committee-member-card .card {
    border-radius: 15px;
    overflow: hidden;
}

.committee-member-card .card:hover {
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15) !important;
}

.member-image-wrapper {
    position: relative;
}

.member-image {
    width: 100px;
    height: 100px;
    object-fit: cover;
    border: 3px solid #f8f9fa;
    transition: all 0.3s ease;
}

.committee-member-card:hover .member-image {
    border-color: var(--primary-color);
    transform: scale(1.05);
}

.member-placeholder {
    width: 100px;
    height: 100px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border: 3px solid #f8f9fa;
    font-size: 2rem;
}

.committee-member-card:hover .member-placeholder {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, var(--primary-color), #ff6b35);
}

.committee-member-card:hover .member-placeholder i {
    color: white !important;
}

/* Responsive adjustments for office bearers and committee pages */
@media (max-width: 768px) {
    .bearer-year-card .card-header .row,
    .committee-year-card .card-header .row {
        text-align: center;
    }

    .bearer-year-card .card-header .col-md-4,
    .committee-year-card .card-header .col-md-4 {
        margin-top: 1rem;
    }

    .download-btn {
        width: 100%;
        margin-top: 0.5rem;
    }

    .member-image,
    .member-placeholder {
        width: 80px;
        height: 80px;
    }

    .member-placeholder {
        font-size: 1.5rem;
    }
}

/* News Page Styles */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, #ff6b35 100%);
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="1" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="1" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.min-vh-50 {
    min-height: 50vh;
}

.floating-card {
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.news-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.news-card:hover {
    transform: translateY(-5px);
}

.news-card:hover .card {
    box-shadow: 0 10px 30px rgba(0,0,0,0.15) !important;
}

.news-image-wrapper {
    position: relative;
    overflow: hidden;
}

.news-image-wrapper img {
    transition: transform 0.3s ease;
    height: 200px;
    object-fit: cover;
    width: 100%;
}

.news-card:hover .news-image-wrapper img {
    transform: scale(1.05);
}

.news-category-badge {
    position: absolute;
    top: 15px;
    left: 15px;
    z-index: 2;
}

.featured-news-card .card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.featured-news-card:hover .card {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15) !important;
}

.object-cover {
    object-fit: cover;
}

.news-highlights .highlight-item {
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.news-highlights .highlight-item:hover {
    background: white !important;
    border-left-color: var(--primary-color);
    transform: translateX(5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.newsletter-card {
    position: relative;
    overflow: hidden;
}

.newsletter-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(219, 35, 26, 0.05), transparent);
    transform: rotate(45deg);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.newsletter-icon {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.archive-item {
    transition: transform 0.3s ease;
}

.archive-item:hover {
    transform: translateY(-3px);
}

.archive-item:hover .card {
    box-shadow: 0 10px 25px rgba(0,0,0,0.15) !important;
}

.date-box {
    min-width: 70px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.date-box .day {
    font-size: 1.5rem;
    line-height: 1;
}

.date-box .month {
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.footer-logo {
    max-height: 60px;
    width: auto;
}

.social-links .btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.social-links .btn:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

/* Responsive adjustments for news page */
@media (max-width: 768px) {
    .hero-section {
        padding: 3rem 0;
    }

    .hero-section .display-4 {
        font-size: 2.5rem;
    }

    .featured-news-card .row {
        flex-direction: column-reverse;
    }

    .news-highlights {
        margin-top: 2rem;
    }

    .archive-item .d-flex {
        flex-direction: column;
        text-align: center;
    }

    .archive-date {
        margin-bottom: 1rem;
        margin-right: 0 !important;
    }
}

/* News Detail Page Styles */
.breadcrumb-section {
    border-bottom: 1px solid #e9ecef;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    position: relative;
}

.breadcrumb-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="1" fill="rgba(255,107,53,0.1)"/><circle cx="80" cy="80" r="1" fill="rgba(255,107,53,0.1)"/></svg>');
    opacity: 0.5;
}

.breadcrumb-section .container {
    position: relative;
    z-index: 1;
}

.breadcrumb-item a {
    color: var(--primary-color);
    transition: all 0.3s ease;
    font-weight: 500;
}

.breadcrumb-item a:hover {
    color: #ff6b35;
    text-decoration: none;
}

.breadcrumb-item.active {
    color: #6c757d;
    font-weight: 600;
}

.news-detail-section {
    background: #fafafa;
}

.news-article {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.08);
}

.article-header {
    border-bottom: 2px solid #f8f9fa;
    padding-bottom: 2rem;
    position: relative;
}

.article-header::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 60px;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), #ff6b35);
    border-radius: 2px;
}

.article-meta {
    margin-bottom: 1.5rem;
}

.article-meta .badge {
    font-size: 0.75rem;
    padding: 0.5rem 0.75rem;
    border-radius: 20px;
    font-weight: 500;
}

.article-meta .text-muted {
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.article-title {
    color: var(--text-dark);
    line-height: 1.2;
    margin-bottom: 1rem;
    font-weight: 700;
}

.article-subtitle {
    font-size: 1.1rem;
    color: #6c757d;
    line-height: 1.6;
    font-style: italic;
}

.article-image {
    position: relative;
}

.article-image img {
    border-radius: 10px;
    transition: transform 0.3s ease;
}

.article-image:hover img {
    transform: scale(1.02);
}

/* Article Content Styling */
.article-content .content-wrapper {
    line-height: 1.8;
    font-size: 1.1rem;
    color: #333;
}

.article-content .content-wrapper p {
    margin-bottom: 1.5rem;
    text-align: justify;
}

.article-content .content-wrapper h1,
.article-content .content-wrapper h2,
.article-content .content-wrapper h3,
.article-content .content-wrapper h4,
.article-content .content-wrapper h5,
.article-content .content-wrapper h6 {
    color: var(--text-dark);
    margin-top: 2rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

.article-content .content-wrapper h2 {
    border-left: 4px solid var(--primary-color);
    padding-left: 1rem;
    margin-left: -1rem;
}

.article-content .content-wrapper ul,
.article-content .content-wrapper ol {
    margin-bottom: 1.5rem;
    padding-left: 2rem;
}

.article-content .content-wrapper li {
    margin-bottom: 0.5rem;
}

.article-content .content-wrapper blockquote {
    background: #f8f9fa;
    border-left: 4px solid var(--primary-color);
    padding: 1.5rem;
    margin: 2rem 0;
    font-style: italic;
    border-radius: 0 8px 8px 0;
}

.article-content .content-wrapper img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    margin: 1.5rem 0;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.article-content .content-wrapper table {
    width: 100%;
    border-collapse: collapse;
    margin: 1.5rem 0;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.article-content .content-wrapper th,
.article-content .content-wrapper td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #e9ecef;
}

.article-content .content-wrapper th {
    background: var(--primary-color);
    color: white;
    font-weight: 600;
}

.article-content .content-wrapper tr:hover {
    background: #f8f9fa;
}

.section-heading {
    color: var(--text-dark);
    border-left: 4px solid var(--primary-color);
    padding-left: 1rem;
    margin-left: -1rem;
}

.speaker-card {
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.speaker-card:hover {
    background: white !important;
    border-left-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.outcome-item {
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.outcome-item:hover {
    background: white !important;
    border-left-color: #28a745;
    transform: translateX(5px);
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
}

.blockquote-custom {
    position: relative;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-left: 5px solid var(--primary-color);
}

.blockquote-custom::before {
    content: '"';
    position: absolute;
    top: -10px;
    left: 20px;
    font-size: 4rem;
    color: var(--primary-color);
    opacity: 0.3;
    font-family: Georgia, serif;
}

.quote-pattern {
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(219,35,26,0.1)"/><circle cx="80" cy="80" r="2" fill="rgba(219,35,26,0.1)"/><circle cx="50" cy="10" r="1" fill="rgba(219,35,26,0.1)"/></svg>');
    opacity: 0.5;
}

.article-footer {
    background: #f8f9fa;
    margin: 0 -2rem -2rem -2rem;
    padding: 1.5rem 2rem;
    border-radius: 0 0 15px 15px;
}

.badge.bg-outline-primary {
    background: transparent !important;
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
}

.share-buttons .btn {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.share-buttons .btn:hover {
    transform: translateY(-2px);
}

/* Sidebar Styles */
.news-sidebar {
    position: sticky;
    top: 100px;
}

.sidebar-widget {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 3px 15px rgba(0,0,0,0.08);
    transition: transform 0.3s ease;
}

.sidebar-widget:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.12);
}

.widget-title {
    color: var(--text-dark);
    border-bottom: 2px solid #f8f9fa;
    padding-bottom: 0.5rem;
}

.related-item {
    transition: all 0.3s ease;
    padding: 0.5rem;
    border-radius: 8px;
}

.related-item:hover {
    background: #f8f9fa;
    transform: translateX(5px);
}

.related-item img {
    border-radius: 5px;
    transition: transform 0.3s ease;
}

.related-item:hover img {
    transform: scale(1.05);
}

.newsletter-widget {
    background: linear-gradient(135deg, var(--primary-color) 0%, #ff6b35 100%);
    position: relative;
    overflow: hidden;
}

.newsletter-widget::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="25" cy="25" r="2" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="2" fill="white" opacity="0.1"/></svg>');
    animation: float 4s ease-in-out infinite;
}

.quick-links a {
    transition: all 0.3s ease;
    padding: 0.25rem 0;
    border-radius: 5px;
}

.quick-links a:hover {
    color: var(--primary-color) !important;
    padding-left: 0.5rem;
}

.contact-widget {
    border-left: 4px solid var(--primary-color);
}

/* Article Navigation */
.article-navigation {
    border-top: 1px solid #e9ecef;
    border-bottom: 1px solid #e9ecef;
}

.nav-link-prev,
.nav-link-next {
    transition: all 0.3s ease;
    padding: 1rem;
    border-radius: 10px;
}

.nav-link-prev:hover,
.nav-link-next:hover {
    background: white;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.nav-icon {
    transition: transform 0.3s ease;
}

.nav-link-prev:hover .nav-icon,
.nav-link-next:hover .nav-icon {
    transform: scale(1.1);
}

/* Comments Section */
.comment-form {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 10px;
    border-left: 4px solid var(--primary-color);
}

.comment-item {
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.comment-item:hover {
    background: white !important;
    border-left-color: var(--primary-color);
    transform: translateX(5px);
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
}

.comment-avatar {
    transition: transform 0.3s ease;
}

.comment-item:hover .comment-avatar {
    transform: scale(1.1);
}

/* Responsive adjustments for news detail page */
@media (max-width: 768px) {
    .news-article {
        padding: 1.5rem;
    }

    .article-footer {
        margin: 0 -1.5rem -1.5rem -1.5rem;
        padding: 1rem 1.5rem;
    }

    .article-title {
        font-size: 2rem;
    }

    .news-sidebar {
        position: static;
        margin-top: 2rem;
    }

    .sidebar-widget {
        margin-bottom: 1.5rem;
    }

    .nav-link-prev .d-flex,
    .nav-link-next .d-flex {
        flex-direction: column;
        text-align: center;
    }

    .nav-icon {
        margin: 0 0 0.5rem 0 !important;
    }

    .comment-form {
        padding: 1.5rem;
    }
}

.product-check .form-check-input {
    margin-top: 0.25rem;
}

.product-check .form-check-label {
    cursor: pointer;
    font-weight: 500;
    color: var(--text-dark);
    transition: color 0.3s ease;
}

.product-check.selected .form-check-label {
    color: var(--primary-color);
}

.process-step {
    padding: 2rem 1rem;
    transition: transform 0.3s ease;
}

.process-step:hover {
    transform: translateY(-5px);
}

.step-number {
    width: 50px;
    height: 50px;
    font-size: 1.25rem;
    position: relative;
    z-index: 2;
}

.step-number::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 100%;
    width: 100px;
    height: 2px;
    background: linear-gradient(to right, var(--primary-color), transparent);
    transform: translateY(-50%);
    z-index: -1;
}

.process-step:last-child .step-number::before {
    display: none;
}

.product-card {
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1) !important;
}

.form-floating.focused {
    transform: scale(1.02);
    transition: transform 0.2s ease;
}

.animate-step {
    animation: slideInUp 0.6s ease forwards;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.success-icon {
    animation: bounceIn 0.6s ease;
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* Responsive adjustments for quotation page */
@media (max-width: 768px) {
    .process-step {
        padding: 1rem 0.5rem;
        margin-bottom: 2rem;
    }

    .step-number::before {
        display: none;
    }

    .product-check {
        padding: 0.5rem;
    }

    .contact-form-wrapper {
        padding: 2rem 1rem !important;
    }
}

/* Enhanced Navigation Styles */
.navbar {
    transition: all 0.3s ease;
    /* backdrop-filter: blur(10px); */
    min-height: 70px;
    padding: 0.5rem 0;
    z-index: 1050;
    width: 100%;
    background: rgba(255, 255, 255, 0.1) !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

/* Navbar scrolled state - solid background instead of transparent */
.navbar.scrolled {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(15px);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.navbar.scrolled .navbar-brand,
.navbar.scrolled .nav-link {
    color: var(--text-dark) !important;
}

.navbar.scrolled .navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%2833, 37, 41, 0.75%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

.navbar .container-fluid {
    max-width: 100%;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
}

@media (min-width: 992px) {
    .navbar .container-fluid {
        padding-left: 3rem;
        padding-right: 3rem;
    }
}

/* Add proper spacing for navbar content */
.navbar-brand {
    margin-right: 2rem;
}

.navbar-nav .nav-link {
    padding: 0.75rem 1rem;
    margin: 0 0.25rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: bold;
    display: flex;
    align-items: center;
    text-decoration: none;
    color: white !important;
    transition: all 0.3s ease;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.navbar-logo {
    transition: all 0.3s ease;
    max-height: 50px;
    width: auto;
    height: auto;
}

/* Enhanced navbar toggler */
.navbar-toggler {
    border: 2px solid var(--primary-color);
    border-radius: 8px;
    padding: 0.5rem 0.75rem;
    transition: all 0.3s ease;
    background: transparent;
}

.navbar-toggler:hover {
    background: var(--primary-color);
    transform: scale(1.05);
}

.navbar-toggler:focus {
    box-shadow: 0 0 0 0.2rem rgba(219, 35, 26, 0.25);
}

.navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%2833, 37, 41, 0.75%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
    transition: all 0.3s ease;
}

.navbar-toggler:hover .navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 1%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

.brand-text {
    font-weight: 700;
    color: white;
    margin-left: 0.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.navbar-actions {
    order: 2;
}

.search-btn {
    border-radius: 25px;
    padding: 0.5rem 0.75rem;
    transition: all 0.3s ease;
    border: 2px solid rgba(255, 255, 255, 0.8);
    color: white;
    background: rgba(255, 255, 255, 0.1);
    min-width: 44px;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.search-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(219, 35, 26, 0.1);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
    z-index: 0;
}

.search-btn:hover::before {
    width: 100px;
    height: 100px;
}

.search-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: scale(1.05);
}

.search-btn:active {
    transform: scale(0.95);
}

.search-btn i,
.search-btn span {
    position: relative;
    z-index: 1;
}

.navbar-nav .nav-link {
    font-weight: 500;
    margin: 0 10px;
    transition: color 0.3s ease;
    position: relative;
    padding: 0.5rem 1rem;
    color: white !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.navbar-nav .nav-link:hover {
    color: rgba(255, 255, 255, 0.8) !important;
}

.navbar-nav .nav-link.active {
    color: #db231a !important;
    font-weight: 600;
}

.navbar-nav .nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: white;
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.navbar-nav .nav-link:hover::after,
.navbar-nav .nav-link.active::after {
    width: 80%;
}

.navbar-toggler {
    border: 2px solid rgba(255, 255, 255, 0.8);
    padding: 0.5rem;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    border-radius: 8px;
    min-width: 44px;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.navbar-toggler:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: white;
}

.navbar-toggler:hover .navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 1%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

.navbar-toggler:focus {
    box-shadow: 0 0 0 0.2rem rgba(219, 35, 26, 0.25);
    outline: none;
}

.navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 1%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
    width: 1.5em;
    height: 1.5em;
    transition: all 0.3s ease;
}

/* Dropdown improvements */
.dropdown-menu {
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    border-radius: 10px;
    padding: 0.5rem 0;
    margin-top: 0.5rem;
}

.dropdown-item {
    padding: 0.75rem 1.5rem;
    transition: all 0.3s ease;
    font-weight: 500;
}

.dropdown-item:hover {
    background: var(--primary-color);
    color: white;
}

/* Search button improvements */
.btn-outline-primary {
    border-radius: 50px;
    padding: 0.5rem 0.75rem;
    transition: all 0.3s ease;
}

.btn-outline-primary:hover {
    transform: scale(1.05);
}

/* Enhanced Hero Slider Section */
.hero-slider {
    position: relative;
    overflow: hidden;
    margin-top: 0;
    padding-top: 0;
}

.hero-slide {
    position: relative;
    overflow: hidden;
    min-height: 100vh;
    min-height: calc(var(--vh, 1vh) * 100);
    display: flex;
    align-items: center;
    background-attachment: fixed;
    background-size: cover;
    background-position: center;
    width: 100%;
}

.hero-slide .container,
.hero-slide .container-fluid {
    width: 100%;
    max-width: 100%;
    padding-left: 2rem;
    padding-right: 2rem;
    padding-top: 2rem;
    padding-bottom: 2rem;
}

@media (min-width: 576px) {
    .hero-slide .container,
    .hero-slide .container-fluid {
        padding-left: 2.5rem;
        padding-right: 2.5rem;
        padding-top: 3rem;
        padding-bottom: 3rem;
    }
}

@media (min-width: 992px) {
    .hero-slide .container,
    .hero-slide .container-fluid {
        padding-left: 4rem;
        padding-right: 4rem;
        padding-top: 4rem;
        padding-bottom: 4rem;
    }
}

/* Add margin to hero content elements */
.hero-slide h1 {
    margin-bottom: 2rem;
}

.hero-slide .lead {
    margin-bottom: 2.5rem;
}

.hero-buttons {
    margin-top: 2rem;
}

.hero-buttons .btn {
    margin: 0.5rem 0.75rem 0.5rem 0;
}

/* Disable parallax on mobile for better performance */
@media (max-width: 768px) {
    .hero-slide {
        background-attachment: scroll !important;
        min-height: 100vh !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }

    .hero-slider {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    .carousel-inner {
        display: block !important;
    }

    .carousel-item {
        display: block !important;
    }

    .carousel-item.active {
        display: block !important;
    }
}

.carousel-item {
    transition: transform 0.8s ease-in-out;
}

/* Ensure hero slider is always visible */
.hero-slider,
.carousel,
.carousel-inner,
.carousel-item {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.carousel-item.active {
    display: block !important;
}

/* Mobile-specific hero slider fixes */
@media (max-width: 991px) {
    .hero-slide {
        background-attachment: scroll !important;
        background-size: cover !important;
        background-position: center !important;
        background-repeat: no-repeat !important;
    }

    .hero-overlay {
        background: rgba(0, 0, 0, 0.4) !important;
    }

    .hero-slide .text-white {
        color: #ffffff !important;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5) !important;
    }

    /* Ensure carousel controls are visible on mobile */
    .carousel-control-prev,
    .carousel-control-next {
        width: 8% !important;
        opacity: 0.8 !important;
    }

    .carousel-control-prev-icon,
    .carousel-control-next-icon {
        background-size: 100% 100% !important;
        width: 2rem !important;
        height: 2rem !important;
    }

    .carousel-indicators {
        bottom: 1rem !important;
    }

    .carousel-indicators [data-bs-target] {
        width: 12px !important;
        height: 12px !important;
        border-radius: 50% !important;
        margin: 0 4px !important;
    }
}

/* Responsive carousel indicators */
.carousel-indicators {
    bottom: 30px;
    margin-bottom: 0;
    z-index: 3;
}

.carousel-indicators button {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin: 0 5px;
    background-color: rgba(255, 255, 255, 0.5);
    border: 2px solid rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;
}

.carousel-indicators button.active {
    background-color: #fff;
    transform: scale(1.2);
}

.carousel-indicators button:hover {
    background-color: rgba(255, 255, 255, 0.8);
    transform: scale(1.1);
}

/* Enhanced carousel controls */
.carousel-control-prev,
.carousel-control-next {
    width: 5%;
    opacity: 0.8;
    transition: all 0.3s ease;
    z-index: 3;
}

.carousel-control-prev:hover,
.carousel-control-next:hover {
    opacity: 1;
}

.carousel-control-prev-icon,
.carousel-control-next-icon {
    width: 2rem;
    height: 2rem;
    background-size: 100% 100%;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

/* Enhanced carousel controls */
.carousel {
    width: 100%;
    overflow: hidden;
}

.carousel-inner {
    width: 100%;
}

.carousel-item {
    width: 100%;
}

.carousel-control-prev,
.carousel-control-next {
    width: 10%;
    opacity: 0.8;
    transition: all 0.3s ease;
    /* background: rgba(0, 0, 0, 0.1); */
    border-radius: 0 15px 15px 0;
    z-index: 10;
}

.carousel-control-next {
    border-radius: 15px 0 0 15px;
}

.carousel-control-prev:hover,
.carousel-control-next:hover {
    opacity: 1;
    background: rgba(0, 0, 0, 0.2);
}

.carousel-control-prev-icon,
.carousel-control-next-icon {
    width: 2rem;
    height: 2rem;
    background-size: 100%;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

/* Mobile-specific carousel controls */
@media (max-width: 768px) {
    .carousel-control-prev,
    .carousel-control-next {
        width: 12%;
        opacity: 0.7;
        background: rgba(0, 0, 0, 0.15);
    }

    .carousel-control-prev-icon,
    .carousel-control-next-icon {
        width: 1.8rem;
        height: 1.8rem;
    }

    .carousel-indicators {
        bottom: 20px;
        z-index: 15;
    }

    .carousel-indicators button {
        width: 12px;
        height: 12px;
        margin: 0 4px;
        border-radius: 50%;
        background-color: rgba(255, 255, 255, 0.6);
        border: 2px solid rgba(255, 255, 255, 0.9);
        transition: all 0.3s ease;
    }

    .carousel-indicators button.active {
        background-color: var(--primary-color);
        border-color: white;
        transform: scale(1.2);
    }

    /* Touch-friendly carousel */
    .carousel-inner {
        touch-action: pan-y pinch-zoom;
    }
}

/* Hero content improvements */
.hero-slide .container {
    position: relative;
    z-index: 2;
}

.hero-slide .row {
    align-items: center;
    min-height: 100vh;
}

.hero-slide h1 {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    animation: fadeInUp 1s ease-out;
}

.hero-slide .lead {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    animation: fadeInUp 1s ease-out 0.2s both;
}

.hero-buttons {
    animation: fadeInUp 1s ease-out 0.4s both;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Hero Section (Legacy support) */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, #1e3c72 100%);
    position: relative;
    overflow: hidden;
    min-height: 100vh;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><polygon fill="rgba(255,255,255,0.1)" points="0,1000 1000,0 1000,1000"/></svg>');
    background-size: cover;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
}

.hero-particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="1" fill="rgba(255,255,255,0.3)"><animate attributeName="opacity" values="0;1;0" dur="3s" repeatCount="indefinite"/></circle><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.3)"><animate attributeName="opacity" values="0;1;0" dur="4s" repeatCount="indefinite"/></circle><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.3)"><animate attributeName="opacity" values="0;1;0" dur="2s" repeatCount="indefinite"/></circle></svg>');
    animation: float 20s infinite linear;
}

@keyframes float {
    0% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
    100% { transform: translateY(0px); }
}

.hero-section .container {
    position: relative;
    z-index: 2;
}

.hero-badge {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.hero-stats {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 1.5rem;
    backdrop-filter: blur(10px);
}

.hero-stat h3 {
    font-size: 2rem;
    font-weight: 700;
}

.hero-buttons .btn {
    margin: 5px;
    padding: 12px 30px;
    border-radius: 50px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
}

.hero-buttons .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.floating-card {
    animation: floatCard 6s ease-in-out infinite;
}

@keyframes floatCard {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

.hero-scroll-indicator {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 3;
}

.scroll-down {
    color: white;
    font-size: 1.5rem;
    text-decoration: none;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

/* Section Styles */
#welcome {
    padding-top: 5rem;
}

.section-badge {
    display: inline-block;
    background: var(--primary-color);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 1rem;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 1rem;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background: var(--accent-color);
    border-radius: 2px;
}

.text-center .section-title::after {
    left: 50%;
    transform: translateX(-50%);
}

.text-left .section-title::after,
.section-title::after {
    left: 0;
    transform: none;
}

/* About Section */
.about-features {
    margin: 1.5rem 0;
}

.feature-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
    font-weight: 500;
}

.about-buttons .btn {
    margin-bottom: 0.5rem;
}

/* About Stats */
.about-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.stat-item {
    text-align: center;
    padding: 2rem 1.5rem;
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    margin-bottom: 1.5rem;
}

/* Ensure stat-item content is visible */
.stat-item h2,
.stat-item p,
.stat-item .stat-number {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Hero stats specific styling */
.hero-stats .stat-item {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.stat-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
}

.stat-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.stat-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), #1e3c72);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    color: white;
    font-size: 1.5rem;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--text-dark);
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.stat-desc {
    color: var(--text-light);
    font-size: 0.85rem;
}

/* Objectives Cards */
.objective-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    height: 100%;
    position: relative;
    transition: transform 0.3s ease;
}

.objective-card:hover {
    transform: translateY(-5px);
}

.objective-number {
    position: absolute;
    top: -15px;
    left: 30px;
    background: var(--primary-color);
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.2rem;
}

/* Service Cards */
.service-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    height: 100%;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
}

.service-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, var(--primary-color), #1e3c72);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
    color: white;
    font-size: 1.8rem;
}

.service-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 1rem;
}

.service-description {
    color: var(--text-light);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.service-features {
    list-style: none;
    padding: 0;
    margin: 0;
}

.service-features li {
    padding: 0.5rem 0;
    color: var(--text-light);
    position: relative;
    padding-left: 1.5rem;
}

.service-features li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--primary-color);
    font-weight: bold;
}

/* Categories Grid */
.category-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    margin-bottom: 1.5rem;
    position: relative;
    overflow: hidden;
}

.category-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.category-card:hover::before {
    transform: scaleX(1);
}

.category-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.category-icon {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.category-card:hover .category-icon {
    transform: scale(1.1);
    color: var(--accent-color);
}

.category-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
}

.category-count {
    color: var(--text-light);
    font-size: 0.9rem;
}

/* Testimonials */
.testimonial-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    height: 100%;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.testimonial-stars {
    color: #ffc107;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.testimonial-text {
    font-style: italic;
    color: var(--text-light);
    margin-bottom: 1.5rem;
    line-height: 1.6;
    position: relative;
}

.testimonial-text::before {
    content: '"';
    font-size: 4rem;
    color: var(--primary-color);
    position: absolute;
    top: -20px;
    left: -10px;
    opacity: 0.3;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.author-avatar {
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}

.author-name {
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.25rem;
}

.author-position {
    color: var(--text-light);
}

/* News Cards */
.news-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 100%;
    position: relative;
}

.news-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.news-date {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: var(--primary-color);
    color: white;
    padding: 0.5rem;
    border-radius: 10px;
    text-align: center;
    min-width: 60px;
    z-index: 2;
}

.date-day {
    display: block;
    font-size: 1.2rem;
    font-weight: 700;
}

.date-month {
    display: block;
    font-size: 0.8rem;
    text-transform: uppercase;
}

.news-content {
    padding: 2rem;
    padding-top: 1rem;
}

.news-category {
    display: inline-block;
    background: var(--accent-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    margin-bottom: 1rem;
}

.news-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 1rem;
    line-height: 1.4;
}

.news-excerpt {
    color: var(--text-light);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.news-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.news-link:hover {
    color: var(--accent-color);
}

/* President Card */
.president-card {
    background: rgba(255, 255, 255, 0.1);
    padding: 2rem;
    border-radius: 15px;
    backdrop-filter: blur(10px);
}

/* Form Styles */
.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 12px 15px;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(44, 90, 160, 0.25);
}

.btn-primary {
    background: var(--primary-color);
    border: none;
    border-radius: 10px;
    padding: 12px 30px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: #1e3c72;
    transform: translateY(-2px);
}

/* Animation Classes */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Enhanced Typography and Spacing */

/* Improved base typography */
body {
    font-size: 16px;
    line-height: 1.6;
}

h1, h2, h3, h4, h5, h6 {
    line-height: 1.3;
    margin-bottom: 1rem;
}

p {
    margin-bottom: 1rem;
    line-height: 1.6;
}

/* Better spacing for sections */
section {
    padding: 4rem 0;
}

@media (max-width: 768px) {
    section {
        padding: 2.5rem 0;
    }
}

@media (max-width: 576px) {
    section {
        padding: 2rem 0;
    }
}

/* Enhanced button typography */
.btn {
    font-weight: 600;
    letter-spacing: 0.5px;
    line-height: 1.4;
}

/* Enhanced Responsive Design */

/* Extra Large Devices (1400px and up) */
@media (min-width: 1400px) {
    .container {
        max-width: 1320px;
    }

    .section-title {
        font-size: 3rem;
    }

    .hero-slide h1 {
        font-size: 4rem;
    }
}

/* Large Devices (1200px and up) */
@media (max-width: 1399px) and (min-width: 1200px) {
    .about-stats {
        grid-template-columns: repeat(3, 1fr);
    }

    .hero-slide h1 {
        font-size: 3.5rem;
    }
}

/* Medium-Large Devices (992px to 1199px) */
@media (max-width: 1199px) and (min-width: 992px) {
    .about-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }

    .hero-slide h1 {
        font-size: 3rem;
    }

    .section-title {
        font-size: 2.25rem;
    }

    .navbar-nav .nav-link {
        margin: 0 5px;
        font-size: 0.95rem;
    }
}

/* Tablet Devices (768px to 991px) */
@media (max-width: 991px) {
    .hero-section,
    .hero-slide {
        text-align: center;
        padding: 100px 0 60px;
    }

    .hero-stats {
        margin-top: 2rem;
    }

    .floating-card {
        margin-top: 2rem;
        max-width: 400px;
        margin-left: auto;
        margin-right: auto;
    }

    .about-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .service-card {
        margin-bottom: 2rem;
    }

    .hero-slide h1 {
        font-size: 2.75rem;
        line-height: 1.2;
    }

    .hero-slide .lead {
        font-size: 1.1rem;
    }

    .hero-buttons {
        margin-top: 2rem;
    }

    .hero-buttons .btn {
        margin: 0.5rem;
        padding: 10px 25px;
        font-size: 0.9rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .welcome-card {
        margin-bottom: 2rem;
    }

    .stat-card {
        margin-bottom: 1rem;
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    /* Ensure mobile stat cards are properly spaced */
    .welcome-stats .stat-card {
        margin-bottom: 1.5rem;
        padding: 1.5rem !important;
    }

    /* Mobile navbar improvements */
    .navbar.scrolled {
        background: rgba(255, 255, 255, 0.98) !important;
    }

    /* Navigation improvements for tablet */
    .navbar-brand img {
        height: 40px;
    }

    .navbar-toggler {
        padding: 0.5rem;
        font-size: 1rem;
    }
}

/* Mobile Devices (up to 767px) */
@media (max-width: 767px) {
    /* Body and general layout */
    body {
        padding-top: 75px;
    }

    /* Navigation for mobile */
    .navbar {
        padding: 0.75rem 0;
        min-height: 75px;
    }

    .navbar .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .navbar-brand {
        font-size: 1.25rem;
        flex: 1;
    }

    .navbar-logo {
        height: 35px;
    }

    .brand-text {
        font-size: 1rem;
    }

    .search-btn {
        padding: 0.5rem;
        min-width: 40px;
        min-height: 40px;
        margin-right: 0.5rem !important;
    }

    .navbar-toggler {
        padding: 0.5rem;
        min-width: 40px;
        min-height: 40px;
        border-width: 1px;
        border: 2px solid var(--primary-color) !important;
    }

    .navbar-actions {
        flex-shrink: 0;
    }

    .navbar-collapse {
        margin-top: 1rem;
        padding: 1rem;
        border-top: 3px solid white;
        background: rgba(0, 0, 0, 0.9);
        border-radius: 0 0 20px 20px;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        backdrop-filter: blur(10px);
        animation: slideDown 0.3s ease-out;
        max-height: 80vh;
        overflow-y: auto;
    }

    @keyframes slideDown {
        from {
            opacity: 0;
            transform: translateY(-20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .navbar-collapse.hiding {
        animation: slideUp 0.3s ease-in forwards;
    }

    @keyframes slideUp {
        from {
            opacity: 1;
            transform: translateY(0);
        }
        to {
            opacity: 0;
            transform: translateY(-20px);
        }
    }

    .navbar-nav {
        padding: 0.5rem 0;
    }

    .navbar-nav .nav-link {
        padding: 1rem 1.5rem;
        margin: 0.25rem 0;
        font-size: 1.1rem;
        text-align: left;
        border-radius: 12px;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        min-height: 52px;
        font-weight: 500;
        position: relative;
        overflow: hidden;
    }

    .navbar-nav .nav-link::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .navbar-nav .nav-link:hover::before {
        left: 100%;
    }

    .navbar-nav .nav-link:hover,
    .navbar-nav .nav-link.active {
        background: linear-gradient(135deg, white, rgba(255, 255, 255, 0.8));
        color: var(--primary-color) !important;
        transform: translateX(5px);
        box-shadow: 0 4px 15px rgba(255, 255, 255, 0.3);
        transform: translateX(5px);
    }

    .navbar-nav .nav-link i {
        width: 20px;
        text-align: center;
        color: white;
    }

    .navbar-nav .nav-link:hover i,
    .navbar-nav .nav-link.active i {
        color: var(--primary-color);
    }

    .dropdown-menu {
        border: none;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        background: #f8f9fa;
        padding: 0.5rem;
        margin: 0.5rem 0;
        border-radius: 10px;
        position: static;
        float: none;
        width: auto;
        margin-top: 0.5rem;
    }

    .dropdown-item {
        padding: 0.75rem 1rem;
        text-align: left;
        color: var(--text-dark);
        border-radius: 8px;
        margin-bottom: 0.25rem;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        min-height: 44px;
        font-weight: 500;
    }

    .dropdown-item:hover {
        background: var(--primary-color);
        color: white;
        transform: translateX(5px);
    }

    .dropdown-item i {
        color: var(--primary-color);
        width: 20px;
        text-align: center;
    }

    .dropdown-item:hover i {
        color: white;
    }

    /* Hero section mobile optimization */
    .hero-section,
    .hero-slide {
        padding: 60px 0 40px;
        min-height: 85vh;
        text-align: center;
    }

    .hero-slide h1 {
        font-size: 2rem;
        line-height: 1.2;
        margin-bottom: 1.5rem;
        padding: 0 1rem;
    }

    .hero-slide .lead {
        font-size: 1rem;
        line-height: 1.5;
        margin-bottom: 2rem;
        padding: 0 1rem;
    }

    .hero-buttons {
        padding: 0 1rem;
    }

    .hero-buttons .btn {
        display: block;
        margin: 0.75rem auto;
        width: 90%;
        max-width: 280px;
        padding: 12px 20px;
        font-size: 0.9rem;
        border-radius: 25px;
    }

    .hero-badge .badge {
        font-size: 0.8rem;
        padding: 0.5rem 1rem;
    }

    .floating-card {
        margin-top: 3rem;
        max-width: 300px;
        margin-left: auto;
        margin-right: auto;
    }

    .floating-card .card-body {
        padding: 2rem 1.5rem;
    }

    .hero-stats {
        margin-top: 3rem;
    }

    .hero-stats .stat-item {
        margin-bottom: 1.5rem;
    }

    .hero-stats h2 {
        font-size: 2rem;
    }

    /* Carousel controls for mobile */
    .carousel-control-prev,
    .carousel-control-next {
        width: 10%;
        opacity: 0.7;
    }

    .carousel-indicators {
        bottom: 20px;
    }

    .carousel-indicators button {
        width: 10px;
        height: 10px;
        margin: 0 3px;
    }

    /* Section titles and content */
    .section-title {
        font-size: 1.75rem;
        line-height: 1.3;
        margin-bottom: 1.5rem;
        padding: 0 1rem;
        text-align: center;
    }

    .section-badge {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
        margin-bottom: 1rem;
    }

    /* Enhanced mobile typography */
    h1, h2, h3, h4, h5, h6 {
        line-height: 1.2;
        margin-bottom: 1rem;
    }

    p {
        line-height: 1.6;
        margin-bottom: 1rem;
    }

    .lead {
        font-size: 1rem;
        line-height: 1.5;
        margin-bottom: 1.5rem;
    }

    /* Grid layouts for mobile */
    .about-stats {
        grid-template-columns: 1fr;
        gap: 1rem;
        margin-top: 1rem;
    }

    .stat-item {
        padding: 1.5rem 1rem;
        margin-bottom: 1rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    /* Cards and content sections */
    .objective-card {
        padding: 1.25rem;
        margin-bottom: 1.5rem;
        border-radius: 12px;
    }

    .service-card {
        padding: 1.25rem;
        margin-bottom: 1.5rem;
        border-radius: 12px;
        text-align: center;
    }

    .service-icon {
        font-size: 2.5rem;
        margin-bottom: 1rem;
    }

    .service-title {
        font-size: 1.1rem;
        margin-bottom: 1rem;
    }

    .service-description {
        font-size: 0.9rem;
        line-height: 1.5;
    }

    .testimonial-card {
        padding: 1.25rem;
        margin-bottom: 1.5rem;
        border-radius: 12px;
    }

    .news-card {
        margin-bottom: 1.5rem;
        border-radius: 12px;
        overflow: hidden;
    }

    .news-content {
        padding: 1.25rem;
    }

    .member-card {
        padding: 1.25rem 1rem;
        margin-bottom: 1.5rem;
        border-radius: 12px;
        text-align: center;
    }

    .category-card {
        margin-bottom: 1.5rem;
        padding: 1.25rem;
        border-radius: 12px;
        text-align: center;
    }

    .category-icon {
        font-size: 2.5rem;
        margin-bottom: 1rem;
    }

    /* Enhanced grid layouts for mobile */
    .row.g-4 > * {
        margin-bottom: 1.5rem;
    }

    /* Better spacing for sections */
    section {
        padding: 3rem 0 !important;
    }

    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    /* Welcome section mobile */
    .welcome-card {
        padding: 1.5rem 1rem !important;
        margin-bottom: 2rem;
        border-radius: 15px;
    }

    .welcome-features .col-md-6 {
        margin-bottom: 1.5rem;
    }

    .feature-item {
        padding: 1rem !important;
        border-radius: 10px;
        margin-bottom: 1rem;
    }

    .welcome-text p {
        font-size: 0.95rem;
        line-height: 1.6;
        margin-bottom: 1rem;
    }

    .quote-icon-wrapper {
        width: 50px;
        height: 50px;
        margin-bottom: 1rem;
    }

    /* Utility elements */
    .back-to-top {
        bottom: 20px;
        right: 20px;
        width: 45px;
        height: 45px;
        font-size: 1rem;
    }

    .notification {
        right: 15px;
        left: 15px;
        transform: translateY(-100px);
        padding: 0.75rem 1rem;
    }

    .notification.show {
        transform: translateY(0);
    }
}

/* Small Mobile Devices (up to 575px) */
@media (max-width: 575px) {
    /* Extra small mobile adjustments */
    body {
        padding-top: 0;
    }

    .navbar {
        padding: 0.5rem 0;
        min-height: 70px;
    }

    .navbar-brand img {
        height: 32px;
    }

    .navbar-brand {
        font-size: 1.2rem;
    }

    .brand-text {
        font-size: 0.9rem;
    }

    .search-btn {
        padding: 0.4rem;
        min-width: 36px;
        min-height: 36px;
        font-size: 0.9rem;
    }

    .navbar-toggler {
        padding: 0.4rem;
        min-width: 36px;
        min-height: 36px;
        font-size: 0.9rem;
    }

    .navbar-collapse {
        margin-top: 0.75rem;
        padding: 0.75rem;
        border-radius: 0 0 15px 15px;
    }

    .navbar-nav .nav-link {
        padding: 0.75rem 1rem;
        font-size: 1rem;
        min-height: 44px;
    }

    /* Hero section for very small screens */
    .hero-slide {
        padding: 60px 0 40px;
        min-height: 85vh;
        display: flex;
        align-items: center;
    }

    .hero-slide .container {
        width: 100%;
    }

    .hero-slide h1 {
        font-size: 2rem;
        line-height: 1.3;
        margin-bottom: 1.5rem;
        padding: 0 1rem;
        text-align: center;
        animation: fadeInUp 0.8s ease-out;
    }

    .hero-slide .lead {
        font-size: 1rem;
        line-height: 1.5;
        margin-bottom: 2rem;
        padding: 0 1rem;
        text-align: center;
        animation: fadeInUp 0.8s ease-out 0.2s both;
    }

    .hero-badge {
        text-align: center;
        animation: fadeInDown 0.8s ease-out;
    }

    .hero-stats {
        margin: 2rem 0;
        text-align: center;
    }

    .hero-stats .col-4 {
        margin-bottom: 1.5rem;
    }

    .hero-stats h2 {
        font-size: 1.8rem;
        margin-bottom: 0.5rem;
        animation: countUp 1s ease-out 0.5s both;
    }

    .hero-stats p {
        font-size: 0.9rem;
        opacity: 0.9;
    }

    .hero-buttons {
        padding: 0 1rem;
        text-align: center;
        animation: fadeInUp 0.8s ease-out 0.4s both;
    }

    .hero-buttons .btn {
        width: 100%;
        max-width: 280px;
        margin: 0.75rem auto;
        padding: 14px 20px;
        font-size: 0.95rem;
        border-radius: 25px;
        font-weight: 600;
        display: block;
        transition: all 0.3s ease;
    }

    .hero-buttons .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    }

    /* Floating card improvements */
    .floating-card {
        margin-top: 2rem;
        animation: float 3s ease-in-out infinite;
    }

    .floating-card .card {
        border-radius: 20px;
        transform: scale(0.9);
    }

    @keyframes countUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes float {
        0%, 100% {
            transform: translateY(0px);
        }
        50% {
            transform: translateY(-10px);
        }
    }

    /* Section adjustments */
    .section-title {
        font-size: 1.4rem;
        line-height: 1.2;
        margin-bottom: 1.5rem;
        padding: 0 0.5rem;
        text-align: center;
    }

    .section-badge {
        font-size: 0.7rem;
        padding: 0.3rem 0.6rem;
        margin-bottom: 1rem;
    }

    /* Enhanced very small mobile typography */
    body {
        font-size: 14px;
        line-height: 1.5;
    }

    h1, h2, h3, h4, h5, h6 {
        line-height: 1.2;
        margin-bottom: 0.75rem;
    }

    p {
        line-height: 1.5;
        margin-bottom: 0.75rem;
        font-size: 0.9rem;
    }

    .lead {
        font-size: 0.95rem;
        line-height: 1.4;
        margin-bottom: 1.25rem;
    }

    /* Button adjustments */
    .about-buttons .btn {
        display: block;
        margin: 0.5rem 0;
        width: 100%;
        padding: 10px 15px;
        font-size: 0.9rem;
    }

    /* Card and content adjustments */
    .stat-item {
        padding: 1rem 0.75rem;
    }

    .stat-number {
        font-size: 1.75rem;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }

    .service-icon {
        width: 55px;
        height: 55px;
        font-size: 1.4rem;
        margin-bottom: 1rem;
    }

    .service-title {
        font-size: 1.1rem;
    }

    .category-icon {
        font-size: 2.25rem;
        margin-bottom: 0.75rem;
    }

    .category-title {
        font-size: 1rem;
    }

    /* News and content cards */
    .news-date {
        position: relative;
        top: auto;
        right: auto;
        margin-bottom: 1rem;
        display: inline-block;
        min-width: 50px;
        padding: 0.4rem;
    }

    .date-day {
        font-size: 1rem;
    }

    .date-month {
        font-size: 0.7rem;
    }

    .news-title {
        font-size: 1.1rem;
        line-height: 1.3;
    }

    .news-content {
        padding: 1.25rem;
    }

    /* Welcome section adjustments */
    .welcome-card {
        padding: 1.25rem 0.75rem !important;
        border-radius: 12px;
    }

    .welcome-text p {
        font-size: 0.85rem;
        line-height: 1.5;
        margin-bottom: 0.75rem;
    }

    .feature-item {
        padding: 0.75rem !important;
        font-size: 0.85rem;
        border-radius: 8px;
        margin-bottom: 0.75rem;
    }

    .feature-item h6 {
        font-size: 0.9rem;
        margin-bottom: 0.5rem;
    }

    /* Enhanced content sections for very small screens */
    .service-card,
    .objective-card,
    .testimonial-card {
        padding: 1rem 0.75rem;
        margin-bottom: 1.25rem;
        border-radius: 10px;
    }

    .category-card {
        padding: 1rem 0.75rem;
        margin-bottom: 1.25rem;
        border-radius: 10px;
    }

    /* Member cards */
    .member-card {
        padding: 1.25rem 0.75rem;
    }

    .member-name {
        font-size: 1rem;
    }

    .member-category {
        font-size: 0.85rem;
    }

    .member-badge {
        font-size: 0.75rem;
        padding: 0.2rem 0.6rem;
    }

    /* Form elements */
    .form-control {
        padding: 10px 12px;
        font-size: 0.9rem;
    }

    .btn {
        padding: 10px 20px;
        font-size: 0.9rem;
    }

    /* Footer adjustments */
    .footer-section h6 {
        font-size: 1rem;
        margin-bottom: 1rem;
    }

    .footer-section ul li {
        margin-bottom: 0.5rem;
    }

    .footer-section a {
        font-size: 0.9rem;
    }

    /* Spacing adjustments */
    .py-5 {
        padding-top: 2.5rem !important;
        padding-bottom: 2.5rem !important;
    }

    .mb-5 {
        margin-bottom: 2rem !important;
    }

    .mt-5 {
        margin-top: 2rem !important;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 50%;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    opacity: 0;
    visibility: hidden;
    z-index: 1000;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.back-to-top.show {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    background: var(--accent-color);
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #1e3c72;
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 100px;
    right: 30px;
    background: white;
    padding: 1rem 1.5rem;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    z-index: 1001;
    transform: translateX(400px);
    transition: all 0.3s ease;
    border-left: 4px solid var(--primary-color);
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    border-left-color: #28a745;
}

.notification.error {
    border-left-color: #dc3545;
}

.notification i {
    margin-right: 0.5rem;
    color: var(--primary-color);
}

/* Page Specific Styles */
.page-header {
    color: white;
    padding: 125px 0 60px;
    text-align: center;
}

.breadcrumb {
    background: transparent;
    padding: 0;
    margin: 0;
}

.breadcrumb-item a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
}

.breadcrumb-item.active {
    color: white;
}

/* Member Cards */
.member-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    margin-bottom: 1.5rem;
}

.member-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.member-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.member-address {
    color: var(--text-light);
    margin-bottom: 0.5rem;
}

.member-categories {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.category-badge {
    background: var(--primary-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
    display: inline-block;
    margin: 0.25rem;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    max-width: 200px;
}

/* Member Profile Specific Styles */
.profile-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
    transition: all 0.3s ease;
}

.profile-card:hover {
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.company-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100px;
    height: 100px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 20px;
    margin: 0 auto 1rem;
    color: white;
    font-size: 2.5rem;
}

.company-title {
    color: var(--primary-color);
    font-weight: 700;
    margin-bottom: 1.5rem;
}

.detail-item {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #f0f0f0;
}

.detail-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.detail-item h5 {
    color: var(--text-dark);
    font-weight: 600;
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
}

.detail-item h5 i {
    width: 20px;
    text-align: center;
}

.contact-person {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 10px;
    border-left: 4px solid var(--primary-color);
    transition: all 0.3s ease;
}

.contact-person:hover {
    background: #e9ecef;
    transform: translateX(5px);
}

.contact-person h6 {
    color: var(--text-dark);
    margin-bottom: 0.5rem;
}

.contact-person a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
}

.contact-person a:hover {
    color: var(--accent-color);
}

.contact-sidebar .profile-card {
    position: sticky;
    top: 100px;
}

.contact-actions .btn {
    border-radius: 10px;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    transition: all 0.3s ease;
}

.contact-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.contact-item h6 {
    color: var(--text-dark);
    font-weight: 600;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
}

.contact-item h6 i {
    width: 20px;
    text-align: center;
}

.contact-item a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
}

.contact-item a:hover {
    color: var(--accent-color);
    text-decoration: underline;
}

/* Member Profile Responsive Styles */
@media (max-width: 991px) {
    .contact-sidebar .profile-card {
        position: static;
        margin-top: 2rem;
    }

    .profile-card {
        padding: 1.5rem;
    }

    .company-logo {
        width: 80px;
        height: 80px;
        font-size: 2rem;
    }
}

@media (max-width: 767px) {
    .profile-card {
        padding: 1rem;
        margin-bottom: 1.5rem;
    }

    .company-logo {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
        margin-bottom: 1rem;
    }

    .company-title {
        font-size: 1.5rem;
        text-align: center;
    }

    .detail-item h5 {
        font-size: 1rem;
    }

    .contact-person {
        margin-bottom: 1rem;
    }

    .contact-actions .btn {
        margin-bottom: 0.5rem;
    }

    .stat-item {
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    .stat-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
}

/* Print Styles */
@media print {
    .navbar,
    .contact-actions,
    .back-to-top,
    .notification {
        display: none !important;
    }

    .profile-card {
        box-shadow: none;
        border: 1px solid #ddd;
        page-break-inside: avoid;
    }

    .page-header {
        background: none !important;
        color: #333 !important;
        padding: 1rem 0;
    }

    body {
        padding-top: 0;
    }
}

/* Contact Form */
.contact-form {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.contact-info {
    background: var(--primary-color);
    color: white;
    padding: 2rem;
    border-radius: 15px;
}

.contact-item {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
}

.contact-icon {
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    font-size: 1.2rem;
}

/* Enhanced Contact Page Styles */
.contact-info-card {
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.contact-info-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15) !important;
}

.contact-icon {
    transition: all 0.3s ease;
}

.contact-info-card:hover .contact-icon {
    transform: scale(1.1);
}

.contact-form-wrapper {
    position: relative;
    overflow: hidden;
}

.form-bg-pattern {
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent 40%, rgba(219, 35, 26, 0.02) 50%, transparent 60%);
    animation: float 20s infinite linear;
}

.contact-bg-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="1" fill="rgba(219,35,26,0.1)"><animate attributeName="opacity" values="0;1;0" dur="3s" repeatCount="indefinite"/></circle><circle cx="80" cy="40" r="1" fill="rgba(219,35,26,0.1)"><animate attributeName="opacity" values="0;1;0" dur="4s" repeatCount="indefinite"/></circle><circle cx="40" cy="80" r="1" fill="rgba(219,35,26,0.1)"><animate attributeName="opacity" values="0;1;0" dur="2s" repeatCount="indefinite"/></circle></svg>');
    opacity: 0.5;
}

.form-floating.focused label {
    color: var(--primary-color);
}

.form-control:focus,
.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(219, 35, 26, 0.25);
}

.form-control.is-valid,
.form-select.is-valid {
    border-color: #28a745;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='m2.3 6.73.94-.94 1.88 1.88 3.75-3.75.94.94-4.69 4.69z'/%3e%3c/svg%3e");
}

.form-control.is-invalid,
.form-select.is-invalid {
    border-color: #dc3545;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.6 2.4 2.4M8.2 4.6l-2.4 2.4'/%3e%3c/svg%3e");
}

.character-counter {
    text-align: right;
    font-size: 0.875rem;
}

.map-container {
    transition: all 0.3s ease;
}

.map-container:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15) !important;
}

.accordion-item {
    border-radius: 10px !important;
    overflow: hidden;
    transition: all 0.3s ease;
}

.accordion-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1) !important;
}

.accordion-button {
    border-radius: 10px !important;
    font-weight: 600;
    padding: 1.25rem 1.5rem;
    background: white;
    border: none;
    box-shadow: none;
}

.accordion-button:not(.collapsed) {
    background: var(--primary-color);
    color: white;
}

.accordion-button:not(.collapsed) i {
    color: white !important;
}

.accordion-button:focus {
    box-shadow: 0 0 0 0.2rem rgba(219, 35, 26, 0.25);
}

.accordion-body {
    padding: 1.5rem;
    background: #f8f9fa;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

/* Contact Page Responsive Styles */
@media (max-width: 991px) {
    .contact-form-wrapper {
        padding: 2rem !important;
    }

    .contact-info-card {
        margin-bottom: 2rem;
    }
}

@media (max-width: 767px) {
    .contact-form-wrapper {
        padding: 1.5rem !important;
    }

    .contact-icon {
        width: 60px;
        height: 60px;
    }

    .contact-icon i {
        font-size: 1.5rem;
    }

    .accordion-button {
        padding: 1rem;
        font-size: 0.9rem;
    }

    .accordion-body {
        padding: 1rem;
    }
}

/* Footer Contact Info */
.contact-info-footer {
    font-size: 0.9rem;
}

.contact-item-footer {
    display: flex;
    align-items: flex-start;
    margin-bottom: 0.75rem;
}

.contact-item-footer i {
    width: 20px;
    margin-top: 2px;
    flex-shrink: 0;
}

.social-links a {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transition: all 0.3s ease;
    text-decoration: none;
}

.social-links a:hover {
    background: var(--primary-color);
    transform: translateY(-2px);
}

/* Welcome Section Styles */
.welcome-content .welcome-card {
    border-left: 4px solid var(--primary-color);
    margin-bottom: 2rem;
}

.welcome-features .feature-item {
    display: flex;
    align-items: center;
    font-weight: 500;
    padding: 1rem;
    margin-bottom: 1rem;
}

/* Add proper spacing to welcome section */
#welcome {
    padding: 4rem 0;
}

#welcome .container {
    padding-left: 2rem;
    padding-right: 2rem;
}

@media (min-width: 992px) {
    #welcome .container {
        padding-left: 3rem;
        padding-right: 3rem;
    }
}

/* About section spacing */
#about {
    padding: 4rem 0;
}

#about .container {
    padding-left: 2rem;
    padding-right: 2rem;
}

@media (min-width: 992px) {
    #about .container {
        padding-left: 3rem;
        padding-right: 3rem;
    }
}

.stat-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative;
    z-index: 1;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* Ensure stat-card content is visible */
.stat-card h2,
.stat-card h6,
.stat-card i {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Fix for stat-card background patterns */
.stat-bg-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.1);
    z-index: -1;
}

/* Ensure welcome stats are visible */
.welcome-stats .stat-card {
    margin-bottom: 1.5rem;
}

.welcome-stats .row {
    margin-bottom: 2rem;
}

/* Fix any potential z-index issues */
.position-relative {
    z-index: 2;
}

/* Ensure counter elements are visible */
[data-counter] {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Add proper spacing to section content */
.section-title {
    margin-bottom: 2rem;
}

.section-badge {
    margin-bottom: 1rem;
    display: inline-block;
}

/* Ensure all text content is visible */
.lead,
.text-muted,
.text-dark {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Member Cards Styles */
.member-card {
    background: white;
    border-radius: 15px;
    padding: 2rem 1.5rem;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    height: 100%;
    border: 1px solid #f0f0f0;
}

.member-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    border-color: var(--primary-color);
}

.member-logo {
    margin-bottom: 1.5rem;
}

.member-logo i {
    transition: transform 0.3s ease;
}

.member-card:hover .member-logo i {
    transform: scale(1.1);
}

.member-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
}

.member-address {
    color: var(--text-light);
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.member-categories {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.member-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    flex-wrap: wrap;
}

.member-actions .btn {
    font-size: 0.85rem;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-weight: 500;
}

.member-category {
    color: var(--text-light);
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.member-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    background: linear-gradient(45deg, var(--primary-color), #ff6b35);
    color: white;
}

/* Member Card List View */
.member-card-list {
    text-align: left;
    padding: 1.5rem;
}

.member-card-list .member-categories {
    justify-content: flex-start;
    margin-bottom: 0;
}

.member-card-list .member-actions {
    justify-content: flex-end;
}

/* Profile Modal Styles */
.member-logo-large {
    padding: 2rem;
    background: linear-gradient(135deg, var(--primary-color), #ff6b35);
    border-radius: 50%;
    display: inline-block;
    margin: 0 auto;
}

.member-logo-large i {
    color: white;
}

/* Updated Objective Cards */
.objective-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    height: 100%;
    border-left: 4px solid var(--primary-color);
}

.objective-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.objective-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, var(--primary-color), #ff6b35);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.objective-icon i {
    font-size: 1.5rem;
    color: white;
}

.objective-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 1rem;
}

/* Color variations for member badges */
.text-purple {
    color: #6f42c1 !important;
}

/* Additional Responsive Enhancements */

/* Landscape Mobile Devices */
@media (max-width: 767px) and (orientation: landscape) {
    .hero-slide {
        min-height: 70vh;
        padding: 60px 0 40px;
    }

    .hero-slide h1 {
        font-size: 2rem;
        margin-bottom: 1rem;
    }

    .hero-slide .lead {
        font-size: 0.95rem;
        margin-bottom: 1.5rem;
    }

    .hero-buttons .btn {
        margin: 0.25rem auto;
        padding: 8px 16px;
    }
}

/* Touch-friendly improvements and mobile optimizations */
@media (max-width: 991px) {
    /* Larger touch targets */
    .btn {
        min-height: 48px;
        min-width: 48px;
        font-size: 1rem;
        padding: 0.75rem 1.5rem;
    }

    .nav-link {
        min-height: 48px;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        font-size: 1.1rem;
    }

    .dropdown-item {
        min-height: 48px;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        font-size: 1rem;
    }

    /* Better spacing for touch */
    .member-card,
    .service-card,
    .category-card {
        margin-bottom: 1.5rem;
        transition: transform 0.2s ease;
    }

    .member-card:active,
    .service-card:active,
    .category-card:active {
        transform: scale(0.98);
    }

    /* Improved readability */
    .card-body {
        padding: 1.5rem;
    }

    .lead {
        font-size: 1.1rem;
        line-height: 1.6;
    }

    /* Smooth scrolling for mobile */
    html {
        scroll-behavior: smooth;
        -webkit-overflow-scrolling: touch;
    }

    /* Prevent zoom on input focus */
    input, select, textarea {
        font-size: 16px;
    }

    /* Better button interactions */
    .btn:active {
        transform: scale(0.95);
    }

    /* Improved carousel performance */
    .carousel-item {
        will-change: transform;
    }

    /* Reduce motion for users who prefer it */
    @media (prefers-reduced-motion: reduce) {
        * {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
        }
    }

    .navbar-collapse {
    max-height: 80vh;
    overflow-y: auto;
  }
  
  .navbar-nav .nav-link {
    padding: 12px 1.5rem;
  }
  
  .dropdown-menu {
    position: static;
    width: 100%;
    margin-top: 0.5rem;
    border: none;
    border-radius: 12px;
    background: rgba(248, 249, 250, 0.95);
    box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(5px);
    padding: 0.5rem;
  }

  .dropdown-item {
    padding: 0.75rem 1.5rem;
    margin: 0.25rem 0;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-weight: 500;
    min-height: 48px;
    display: flex;
    align-items: center;
  }

  .dropdown-item:hover {
    background: var(--primary-color);
    color: white;
    transform: translateX(3px);
  }

  .dropdown-item i {
    width: 20px;
    text-align: center;
  }
}

/* Critical Mobile Fixes */
@media (max-width: 991px) {
    /* Ensure full width on mobile */
    .container,
    .container-fluid {
        width: 100% !important;
        max-width: 100% !important;
        padding-left: 15px !important;
        padding-right: 15px !important;
    }

    /* Fix navbar collapse */
    .navbar-collapse {
        width: 100%;
        margin-left: -15px;
        margin-right: -15px;
        padding-left: 15px;
        padding-right: 15px;
    }

    /* Ensure carousel is full width */
    .carousel,
    .carousel-inner,
    .carousel-item {
        width: 100% !important;
        max-width: 100% !important;
    }

    /* Fix hero content */
    .hero-slide {
        width: 100% !important;
        padding-left: 0 !important;
        padding-right: 0 !important;
    }

    /* Responsive buttons */
    .btn {
        width: auto;
        min-width: 120px;
        padding: 0.75rem 1.5rem;
    }

    /* Fix text overflow */
    h1, h2, h3, h4, h5, h6 {
        word-wrap: break-word;
        overflow-wrap: break-word;
    }

    /* Responsive images */
    img {
        max-width: 100% !important;
        height: auto !important;
    }
}

/* Extra small devices */
@media (max-width: 575px) {
    .container,
    .container-fluid {
        padding-left: 15px !important;
        padding-right: 15px !important;
    }

    .hero-slide {
        min-height: 100vh !important;
        padding: 2rem 0 !important;
        display: flex !important;
        align-items: center !important;
    }

    .hero-slide h1 {
        font-size: 1.75rem !important;
        line-height: 1.2 !important;
        margin-bottom: 1.5rem !important;
    }

    .hero-slide .lead {
        font-size: 1rem !important;
        margin-bottom: 2rem !important;
    }

    .hero-buttons {
        margin-top: 1.5rem !important;
    }

    .hero-buttons .btn {
        display: block !important;
        width: 100% !important;
        margin: 0.5rem 0 !important;
        padding: 0.75rem 1.5rem !important;
        font-size: 1rem !important;
    }

    .btn {
        min-width: 100px;
        padding: 0.6rem 1.2rem;
        font-size: 0.9rem;
    }
}

/* Mobile Portrait Orientation */
@media (max-width: 768px) and (orientation: portrait) {
    .hero-slide {
        min-height: 100vh !important;
        background-attachment: scroll !important;
        display: flex !important;
        align-items: center !important;
    }

    .hero-slide .container {
        width: 100% !important;
        padding: 2rem 1rem !important;
    }

    .carousel-indicators {
        bottom: 20px !important;
    }

    .carousel-control-prev,
    .carousel-control-next {
        width: 5% !important;
    }
}

/* Landscape orientation fixes */
@media (max-height: 500px) and (orientation: landscape) {
    .hero-slide {
        min-height: 100vh !important;
        padding: 2rem 0 !important;
    }

    .navbar {
        min-height: 60px !important;
    }

    body {
        padding-top: 60px !important;
    }
}

/* High DPI / Retina Display Support */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .navbar-brand img,
    .member-logo img {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Print Styles */
@media print {
    .navbar,
    .hero-slider,
    .back-to-top,
    .notification,
    .btn,
    .carousel-control-prev,
    .carousel-control-next,
    .carousel-indicators {
        display: none !important;
    }

    body {
        padding-top: 0;
        font-size: 12pt;
        line-height: 1.4;
        color: #000;
    }

    .section-title {
        font-size: 18pt;
        color: #000;
    }

    .card,
    .service-card,
    .member-card {
        border: 1px solid #ccc;
        box-shadow: none;
        page-break-inside: avoid;
    }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .floating-card {
        animation: none;
    }

    .hero-particles {
        animation: none;
    }
}

/* Dark mode support (if needed in future) */
@media (prefers-color-scheme: dark) {
    /* This can be expanded later for dark mode support */
}

/* President Section Styles */
.president-profile-section {
    padding: 2rem 1rem;
}

.president-image-wrapper {
    position: relative;
    display: inline-block;
}

.president-image-wrapper::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: linear-gradient(45deg, var(--primary-color), #ff6b35);
    border-radius: 50%;
    z-index: -1;
    opacity: 0.1;
}

.president-info h3 {
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.president-info p {
    margin-bottom: 0.25rem;
}

.president-leadership-card {
    padding: 1.5rem;
}

.president-image-container {
    position: relative;
    display: inline-block;
}

.president-image-container::after {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    background: linear-gradient(45deg, rgba(255,255,255,0.3), rgba(255,255,255,0.1));
    border-radius: 50%;
    z-index: -1;
}

.leadership-message h3 {
    position: relative;
}

.leadership-message h3::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 60px;
    height: 3px;
    background: rgba(255,255,255,0.3);
    border-radius: 2px;
}

/* Responsive adjustments for president sections */
@media (max-width: 991px) {
    .president-profile-section {
        margin-bottom: 2rem;
        text-align: center;
    }

    .president-image-wrapper img,
    .president-image-wrapper .default-avatar {
        width: 150px !important;
        height: 150px !important;
    }

    .president-info h3 {
        font-size: 1.5rem;
    }

    .president-leadership-card {
        margin-bottom: 2rem;
    }

    .president-image-container img,
    .president-image-container .default-avatar {
        width: 120px !important;
        height: 120px !important;
    }
}

@media (max-width: 767px) {
    .president-image-wrapper img,
    .president-image-wrapper .default-avatar {
        width: 120px !important;
        height: 120px !important;
    }

    .president-info h3 {
        font-size: 1.25rem;
    }

    .president-image-container img,
    .president-image-container .default-avatar {
        width: 100px !important;
        height: 100px !important;
    }

    .leadership-message h3 {
        font-size: 1.25rem;
    }

    .leadership-message .blockquote p {
        font-size: 1rem !important;
    }
}


.bg-gradient-primary {
  background-color: #4e73df; /* fallback */
  background-image: linear-gradient(180deg, #4e73df 10%, #224abe 100%);
  background-size: cover;
}

.bg-gradient-success {
  background-color: #1cc88a; /* fallback color */
  background-image: linear-gradient(180deg, #1cc88a 10%, #13855c 100%);
  background-size: cover;
}


.bg-gradient-warning {
  background: linear-gradient(45deg, #ffc107, #ffca2c);
  color: #212529;
}

.bg-gradient-info {
  background: linear-gradient(45deg, #17a2b8, #138496);
  color: white;
}
