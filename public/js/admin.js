/**
 * Admin Panel JavaScript for UIA
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // Initialize admin functionality
    initializeAdminLogin();
    initializeAdminDashboard();
    initializeFormValidation();
    initializeAlerts();
    
});

/**
 * Admin Login Page Functionality
 */
function initializeAdminLogin() {
    const loginForm = document.getElementById('adminLoginForm');
    const emailInput = document.getElementById('email');
    const passwordInput = document.getElementById('password');
    const submitBtn = document.querySelector('.admin-btn-primary');
    
    if (!loginForm) return;
    
    // Add loading state to submit button
    loginForm.addEventListener('submit', function(e) {
        if (submitBtn) {
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Signing In...';
            submitBtn.disabled = true;
        }
    });
    
    // Real-time validation
    if (emailInput) {
        emailInput.addEventListener('blur', function() {
            validateEmail(this);
        });
        
        emailInput.addEventListener('input', function() {
            clearFieldError(this);
        });
    }
    
    if (passwordInput) {
        passwordInput.addEventListener('input', function() {
            clearFieldError(this);
            validatePassword(this);
        });
    }
    
    // Show/Hide password functionality
    const passwordToggle = document.getElementById('passwordToggle');
    if (passwordToggle) {
        passwordToggle.addEventListener('click', function() {
            togglePasswordVisibility(passwordInput, this);
        });
    }
}

/**
 * Admin Dashboard Functionality
 */
function initializeAdminDashboard() {
    const sidebarToggle = document.getElementById('sidebarToggle');
    const sidebar = document.querySelector('.admin-sidebar');
    const body = document.body;
    
    if (sidebarToggle && sidebar) {
        sidebarToggle.addEventListener('click', function() {
            sidebar.classList.toggle('show');
            body.classList.toggle('sidebar-open');
        });
        
        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(e) {
            if (window.innerWidth <= 768) {
                if (!sidebar.contains(e.target) && !sidebarToggle.contains(e.target)) {
                    sidebar.classList.remove('show');
                    body.classList.remove('sidebar-open');
                }
            }
        });
    }
    
    // Initialize dashboard widgets
    initializeDashboardWidgets();

    // Initialize dashboard counters
    initializeDashboardCounters();
}

/**
 * Form Validation
 */
function initializeFormValidation() {
    const forms = document.querySelectorAll('.needs-validation');
    
    forms.forEach(function(form) {
        form.addEventListener('submit', function(e) {
            if (!form.checkValidity()) {
                e.preventDefault();
                e.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    });
    
    // Enhanced form validation for admin forms
    const adminForms = document.querySelectorAll('form');
    adminForms.forEach(function(form) {
        form.addEventListener('submit', function(e) {
            const requiredFields = form.querySelectorAll('[required]');
            let isValid = true;
            
            requiredFields.forEach(function(field) {
                if (!field.value.trim()) {
                    isValid = false;
                    field.classList.add('is-invalid');
                    showFieldError(field, 'This field is required');
                } else {
                    field.classList.remove('is-invalid');
                    clearFieldError(field);
                }
            });
            
            if (!isValid) {
                e.preventDefault();
                showNotification('Please fill in all required fields', 'error');
            }
        });
    });
}

/**
 * Alert Management
 */
function initializeAlerts() {
    // Auto-hide alerts after 5 seconds
    const alerts = document.querySelectorAll('.admin-alert');
    alerts.forEach(function(alert) {
        setTimeout(function() {
            fadeOutAlert(alert);
        }, 5000);
    });
    
    // Close button functionality
    const closeButtons = document.querySelectorAll('.alert-close');
    closeButtons.forEach(function(btn) {
        btn.addEventListener('click', function() {
            const alert = this.closest('.admin-alert');
            fadeOutAlert(alert);
        });
    });
}

/**
 * Dashboard Widgets
 */
function initializeDashboardWidgets() {
    // Animate counters
    const counters = document.querySelectorAll('.counter');
    counters.forEach(function(counter) {
        animateCounter(counter);
    });
    
    // Initialize charts if Chart.js is available
    if (typeof Chart !== 'undefined') {
        initializeCharts();
    }
}

/**
 * Utility Functions
 */

function validateEmail(input) {
    const email = input.value.trim();
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    
    if (email === '') {
        showFieldError(input, 'Email is required');
        return false;
    } else if (!emailRegex.test(email)) {
        showFieldError(input, 'Please enter a valid email address');
        return false;
    } else {
        clearFieldError(input);
        return true;
    }
}

function validatePassword(input) {
    const password = input.value;
    
    if (password.length < 6) {
        showFieldError(input, 'Password must be at least 6 characters long');
        return false;
    } else {
        clearFieldError(input);
        return true;
    }
}

function showFieldError(input, message) {
    input.classList.add('is-invalid');
    
    let feedback = input.parentNode.querySelector('.admin-invalid-feedback');
    if (!feedback) {
        feedback = document.createElement('div');
        feedback.className = 'admin-invalid-feedback';
        input.parentNode.appendChild(feedback);
    }
    feedback.textContent = message;
}

function clearFieldError(input) {
    input.classList.remove('is-invalid');
    const feedback = input.parentNode.querySelector('.admin-invalid-feedback');
    if (feedback) {
        feedback.remove();
    }
}

function togglePasswordVisibility(passwordInput, toggleBtn) {
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleBtn.innerHTML = '<i class="fas fa-eye-slash"></i>';
    } else {
        passwordInput.type = 'password';
        toggleBtn.innerHTML = '<i class="fas fa-eye"></i>';
    }
}

function fadeOutAlert(alert) {
    alert.style.transition = 'opacity 0.5s ease';
    alert.style.opacity = '0';
    setTimeout(function() {
        alert.remove();
    }, 500);
}

function animateCounter(counter) {
    const target = parseInt(counter.getAttribute('data-target'));
    const duration = 2000; // 2 seconds
    const step = target / (duration / 16); // 60fps
    let current = 0;
    
    const timer = setInterval(function() {
        current += step;
        if (current >= target) {
            current = target;
            clearInterval(timer);
        }
        counter.textContent = Math.floor(current);
    }, 16);
}

function initializeCharts() {
    // Example chart initialization
    const ctx = document.getElementById('adminChart');
    if (ctx) {
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                datasets: [{
                    label: 'Website Traffic',
                    data: [12, 19, 3, 5, 2, 3],
                    borderColor: 'rgb(219, 35, 26)',
                    backgroundColor: 'rgba(219, 35, 26, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'top',
                    }
                }
            }
        });
    }
}

/**
 * AJAX Helper Functions
 */
function makeAjaxRequest(url, method = 'GET', data = null) {
    return fetch(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: data ? JSON.stringify(data) : null
    })
    .then(response => response.json())
    .catch(error => {
        console.error('Ajax request failed:', error);
        showNotification('An error occurred. Please try again.', 'error');
    });
}

function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `admin-alert admin-alert-${type} notification`;
    notification.innerHTML = `
        <span>${message}</span>
        <button type="button" class="alert-close" aria-label="Close">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    document.body.appendChild(notification);
    
    // Position the notification
    notification.style.position = 'fixed';
    notification.style.top = '20px';
    notification.style.right = '20px';
    notification.style.zIndex = '9999';
    notification.style.minWidth = '300px';
    
    // Auto-remove after 5 seconds
    setTimeout(function() {
        fadeOutAlert(notification);
    }, 5000);
    
    // Close button functionality
    const closeBtn = notification.querySelector('.alert-close');
    closeBtn.addEventListener('click', function() {
        fadeOutAlert(notification);
    });
}

/**
 * Initialize dashboard widgets
 */
function initializeDashboardWidgets() {
    // Add hover effects to admin cards
    const adminCards = document.querySelectorAll('.admin-card');
    adminCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.boxShadow = '0 4px 15px rgba(0, 0, 0, 0.1)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '';
        });
    });
}

/**
 * Initialize dashboard counters with animation
 */
function initializeDashboardCounters() {
    const counters = document.querySelectorAll('.counter');

    const animateCounter = (counter) => {
        const target = parseInt(counter.getAttribute('data-target'));
        const duration = 2000; // 2 seconds
        const increment = target / (duration / 16); // 60fps
        let current = 0;

        const updateCounter = () => {
            if (current < target) {
                current += increment;
                counter.textContent = Math.floor(current);
                requestAnimationFrame(updateCounter);
            } else {
                counter.textContent = target;
            }
        };

        updateCounter();
    };

    // Use Intersection Observer to trigger animation when counters come into view
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateCounter(entry.target);
                observer.unobserve(entry.target);
            }
        });
    }, { threshold: 0.5 });

    counters.forEach(counter => {
        observer.observe(counter);
    });
}

/**
 * Initialize DataTables
 */
function initializeDataTables() {
    const tables = document.querySelectorAll('.data-table');
    tables.forEach(function(table) {
        if ($.fn.DataTable && $.fn.DataTable.isDataTable(table)) {
            $(table).DataTable().destroy();
        }

        const tableConfig = {
            responsive: true,
            pageLength: 25,
            lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "All"]],
            order: [[0, 'desc']],
            dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
                 '<"row"<"col-sm-12 col-md-2"B><"col-sm-12 col-md-10"<"table-info-custom">>>' +
                 '<"row"<"col-sm-12"tr>>' +
                 '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
            buttons: [
                {
                    extend: 'excel',
                    text: '<i class="fas fa-file-excel me-1"></i>Excel',
                    className: 'btn btn-success btn-sm',
                    exportOptions: {
                        columns: ':not(.no-export)'
                    }
                },
                {
                    extend: 'pdf',
                    text: '<i class="fas fa-file-pdf me-1"></i>PDF',
                    className: 'btn btn-danger btn-sm',
                    exportOptions: {
                        columns: ':not(.no-export)'
                    }
                },
                {
                    extend: 'print',
                    text: '<i class="fas fa-print me-1"></i>Print',
                    className: 'btn btn-info btn-sm',
                    exportOptions: {
                        columns: ':not(.no-export)'
                    }
                }
            ],
            language: {
                search: "",
                searchPlaceholder: "Search records...",
                lengthMenu: "Show _MENU_ entries",
                info: "Showing _START_ to _END_ of _TOTAL_ entries",
                infoEmpty: "Showing 0 to 0 of 0 entries",
                infoFiltered: "(filtered from _MAX_ total entries)",
                zeroRecords: "No matching records found",
                emptyTable: "No data available in table",
                paginate: {
                    first: '<i class="fas fa-angle-double-left"></i>',
                    last: '<i class="fas fa-angle-double-right"></i>',
                    next: '<i class="fas fa-angle-right"></i>',
                    previous: '<i class="fas fa-angle-left"></i>'
                }
            },
            drawCallback: function(settings) {
                // Initialize tooltips for action buttons
                if (typeof bootstrap !== 'undefined') {
                    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
                    tooltipTriggerList.map(function (tooltipTriggerEl) {
                        return new bootstrap.Tooltip(tooltipTriggerEl);
                    });
                }

                // Add custom styling to pagination
                $('.dataTables_paginate .pagination').addClass('pagination-sm');
            },
            initComplete: function() {
                // Add custom search styling
                $('.dataTables_filter input').addClass('form-control form-control-sm');
                $('.dataTables_length select').addClass('form-select form-select-sm');
            }
        };

        // Check if jQuery and DataTables are available
        if (typeof $ !== 'undefined' && $.fn.DataTable) {
            $(table).DataTable(tableConfig);
        }
    });
}

// Initialize everything when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeAdminDashboard();
    initializeFormValidation();
    initializeAlerts();
    initializeImagePreview();
    initializeDataTables();
    initializeTooltips();
    initializeConfirmDialogs();
});
