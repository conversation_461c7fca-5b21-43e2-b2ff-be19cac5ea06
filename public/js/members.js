// UIA Members Page JavaScript

$(document).ready(function() {
    // Initialize the page
    function initializePage() {
        setupEventListeners();
        AOS.refresh();
    }

    // Setup event listeners
    function setupEventListeners() {
        // Search functionality
        $('#memberSearch').on('input', debounce(filterMembers, 300));
        $('#locationFilter').on('change', filterMembers);

        // View toggle
        $('#gridView').on('click', function() {
            $(this).addClass('active').siblings().removeClass('active');
            showGridView();
        });

        $('#listView').on('click', function() {
            $(this).addClass('active').siblings().removeClass('active');
            showListView();
        });

        // Modal search
        $('#modalSearchInput').on('input', function() {
            const searchTerm = $(this).val();
            $('#memberSearch').val(searchTerm);
            filterMembers();
        });
    }

    // Debounce function for search
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Filter members based on search and filters
    function filterMembers() {
        const searchTerm = $('#memberSearch').val().toLowerCase();
        const locationFilter = $('#locationFilter').val();

        // Hide all member cards initially
        $('.member-card').closest('.col-lg-4, .col-12').hide();

        // Show matching member cards
        $('.member-card').each(function() {
            const memberName = $(this).find('.member-name').text().toLowerCase();
            const memberAddress = $(this).find('.member-address').text().toLowerCase();
            const memberLocation = $(this).data('location') || '';

            const matchesSearch = !searchTerm ||
                memberName.includes(searchTerm) ||
                memberAddress.includes(searchTerm);

            const matchesLocation = !locationFilter ||
                memberLocation.toLowerCase().includes(locationFilter.toLowerCase());

            if (matchesSearch && matchesLocation) {
                $(this).closest('.col-lg-4, .col-12').show();
            }
        });

        // Update member count
        updateMemberCount();
    }

    // Show grid view
    function showGridView() {
        $('.member-card').removeClass('member-card-list');
        $('.member-card').each(function() {
            const col = $(this).closest('.col-lg-4, .col-12');
            col.removeClass('col-12').addClass('col-lg-4 col-md-6');
            $(this).find('.member-logo i').removeClass('fa-lg').addClass('fa-3x');
        });
    }

    // Show list view
    function showListView() {
        $('.member-card').addClass('member-card-list');
        $('.member-card').each(function() {
            const col = $(this).closest('.col-lg-4, .col-12');
            col.removeClass('col-lg-4 col-md-6').addClass('col-12');
            $(this).find('.member-logo i').removeClass('fa-3x').addClass('fa-lg');
        });
    }

    // Update member count
    function updateMemberCount() {
        const totalMembers = $('.member-card').length;
        const visibleMembers = $('.member-card:visible').length;
        $('#memberCount').text(`Showing ${visibleMembers} of ${totalMembers} members`);
    }

    // Global functions
    window.clearFilters = function() {
        $('#memberSearch').val('');
        $('#locationFilter').val('');
        $('.member-card').closest('.col-lg-4, .col-12').show();
        updateMemberCount();
    };

    window.performSearch = function() {
        const searchTerm = $('#modalSearchInput').val();
        $('#memberSearch').val(searchTerm);
        $('#searchModal').modal('hide');
        filterMembers();
    };

    // View Member Profile Function
    window.viewMemberProfile = function(memberName) {
        // Redirect to member details page with member name as query parameter
        window.location.href = `/member-details?name=${encodeURIComponent(memberName)}`;
    };

    // Open Contact Form Function
    window.openContactForm = function(memberName) {
        // Create and show contact modal
        const modalHtml = `
            <div class="modal fade" id="contactModal" tabindex="-1" aria-labelledby="contactModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="contactModalLabel">
                                <i class="fas fa-envelope me-2"></i>Contact ${memberName}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <form id="contactForm">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="contactName" class="form-label">Your Name *</label>
                                        <input type="text" class="form-control" id="contactName" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="contactEmail" class="form-label">Your Email *</label>
                                        <input type="email" class="form-control" id="contactEmail" required>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="contactPhone" class="form-label">Your Phone</label>
                                        <input type="tel" class="form-control" id="contactPhone">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="contactCompany" class="form-label">Your Company</label>
                                        <input type="text" class="form-control" id="contactCompany">
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="contactSubject" class="form-label">Subject *</label>
                                    <input type="text" class="form-control" id="contactSubject" required>
                                </div>
                                <div class="mb-3">
                                    <label for="contactMessage" class="form-label">Message *</label>
                                    <textarea class="form-control" id="contactMessage" rows="4" required></textarea>
                                </div>
                                <input type="hidden" id="targetMember" value="${memberName}">
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="button" class="btn btn-primary" onclick="submitContactForm()">
                                <i class="fas fa-paper-plane me-2"></i>Send Message
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if any
        const existingModal = document.getElementById('contactModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Add modal to body
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('contactModal'));
        modal.show();

        // Clean up modal after it's hidden
        document.getElementById('contactModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    };

    // Submit Contact Form Function
    window.submitContactForm = function() {
        const form = document.getElementById('contactForm');

        // Basic validation
        const name = document.getElementById('contactName').value.trim();
        const email = document.getElementById('contactEmail').value.trim();
        const subject = document.getElementById('contactSubject').value.trim();
        const message = document.getElementById('contactMessage').value.trim();

        if (!name || !email || !subject || !message) {
            alert('Please fill in all required fields.');
            return;
        }

        // Here you would typically send the data to your server
        // For now, we'll just show a success message
        alert(`Thank you ${name}! Your message has been sent to ${document.getElementById('targetMember').value}. We will get back to you soon.`);

        // Close modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('contactModal'));
        modal.hide();
    };

    // Initialize the page when DOM is ready
    initializePage();
    
    // Initial filter and count update
    filterMembers();
});
