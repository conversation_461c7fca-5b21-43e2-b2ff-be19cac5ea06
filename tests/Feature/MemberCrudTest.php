<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\Member;
use App\Models\User;

class MemberCrudTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $admin;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create an admin user for testing
        $this->admin = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password')
        ]);
    }

    /** @test */
    public function admin_can_view_members_index()
    {
        $this->actingAs($this->admin);
        
        $response = $this->get(route('admin.members.index'));
        
        $response->assertStatus(200);
        $response->assertViewIs('admin.members.index');
    }

    /** @test */
    public function admin_can_view_create_member_form()
    {
        $this->actingAs($this->admin);
        
        $response = $this->get(route('admin.members.create'));
        
        $response->assertStatus(200);
        $response->assertViewIs('admin.members.create');
    }

    /** @test */
    public function admin_can_create_member_with_single_email_and_phone()
    {
        $this->actingAs($this->admin);
        
        $memberData = [
            'name' => 'Test Company Ltd',
            'address' => 'Test Address',
            'full_address' => 'Full Test Address Line 1\nLine 2',
            'description' => 'Test company description',
            'emails' => ['<EMAIL>'],
            'phones' => ['+91-9876543210'],
            'website' => 'https://testcompany.com',
            'contact_persons' => [
                ['name' => 'John Doe', 'phone' => '+91-9876543211']
            ],
            'is_active' => true,
            'sort_order' => 1
        ];
        
        $response = $this->post(route('admin.members.store'), $memberData);
        
        $response->assertRedirect();
        $response->assertSessionHas('success');
        
        $this->assertDatabaseHas('members', [
            'name' => 'Test Company Ltd',
            'address' => 'Test Address',
            'is_active' => true,
            'sort_order' => 1
        ]);
        
        $member = Member::where('name', 'Test Company Ltd')->first();
        $this->assertEquals(['<EMAIL>'], $member->emails);
        $this->assertEquals(['+91-9876543210'], $member->phones);
    }

    /** @test */
    public function admin_can_create_member_with_multiple_emails_and_phones()
    {
        $this->actingAs($this->admin);
        
        $memberData = [
            'name' => 'Multi Contact Company',
            'address' => 'Multi Address',
            'emails' => [
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>'
            ],
            'phones' => [
                '+91-9876543210',
                '+91-9876543211',
                '0771-2345678'
            ],
            'is_active' => true,
            'sort_order' => 1
        ];
        
        $response = $this->post(route('admin.members.store'), $memberData);
        
        $response->assertRedirect();
        $response->assertSessionHas('success');
        
        $member = Member::where('name', 'Multi Contact Company')->first();
        $this->assertCount(3, $member->emails);
        $this->assertCount(3, $member->phones);
        $this->assertContains('<EMAIL>', $member->emails);
        $this->assertContains('<EMAIL>', $member->emails);
        $this->assertContains('<EMAIL>', $member->emails);
        $this->assertContains('+91-9876543210', $member->phones);
        $this->assertContains('+91-9876543211', $member->phones);
        $this->assertContains('0771-2345678', $member->phones);
    }

    /** @test */
    public function admin_can_view_member()
    {
        $this->actingAs($this->admin);
        
        $member = Member::create([
            'name' => 'View Test Company',
            'address' => 'View Test Address',
            'emails' => ['<EMAIL>'],
            'phones' => ['+91-1234567890'],
            'is_active' => true,
            'sort_order' => 1
        ]);
        
        $response = $this->get(route('admin.members.show', $member));
        
        $response->assertStatus(200);
        $response->assertViewIs('admin.members.show');
        $response->assertViewHas('member', $member);
    }

    /** @test */
    public function admin_can_view_edit_member_form()
    {
        $this->actingAs($this->admin);
        
        $member = Member::create([
            'name' => 'Edit Test Company',
            'address' => 'Edit Test Address',
            'emails' => ['<EMAIL>'],
            'phones' => ['+91-1234567890'],
            'is_active' => true,
            'sort_order' => 1
        ]);
        
        $response = $this->get(route('admin.members.edit', $member));
        
        $response->assertStatus(200);
        $response->assertViewIs('admin.members.edit');
        $response->assertViewHas('member', $member);
    }

    /** @test */
    public function admin_can_update_member_with_multiple_contacts()
    {
        $this->actingAs($this->admin);
        
        $member = Member::create([
            'name' => 'Original Company',
            'address' => 'Original Address',
            'emails' => ['<EMAIL>'],
            'phones' => ['+91-1111111111'],
            'is_active' => true,
            'sort_order' => 1
        ]);
        
        $updateData = [
            'name' => 'Updated Company',
            'address' => 'Updated Address',
            'emails' => [
                '<EMAIL>',
                '<EMAIL>'
            ],
            'phones' => [
                '+91-2222222222',
                '+91-3333333333',
                '0771-4444444'
            ],
            'is_active' => false,
            'sort_order' => 2
        ];
        
        $response = $this->put(route('admin.members.update', $member), $updateData);
        
        $response->assertRedirect();
        $response->assertSessionHas('success');
        
        $member->refresh();
        $this->assertEquals('Updated Company', $member->name);
        $this->assertEquals('Updated Address', $member->address);
        $this->assertCount(2, $member->emails);
        $this->assertCount(3, $member->phones);
        $this->assertContains('<EMAIL>', $member->emails);
        $this->assertContains('<EMAIL>', $member->emails);
        $this->assertFalse($member->is_active);
        $this->assertEquals(2, $member->sort_order);
    }

    /** @test */
    public function admin_can_delete_member()
    {
        $this->actingAs($this->admin);
        
        $member = Member::create([
            'name' => 'Delete Test Company',
            'address' => 'Delete Test Address',
            'emails' => ['<EMAIL>'],
            'phones' => ['+91-1234567890'],
            'is_active' => true,
            'sort_order' => 1
        ]);
        
        $response = $this->delete(route('admin.members.destroy', $member));
        
        $response->assertRedirect(route('admin.members.index'));
        $response->assertSessionHas('success');
        
        $this->assertDatabaseMissing('members', [
            'id' => $member->id
        ]);
    }

    /** @test */
    public function member_creation_requires_name_and_address()
    {
        $this->actingAs($this->admin);
        
        $response = $this->post(route('admin.members.store'), []);
        
        $response->assertSessionHasErrors(['name', 'address']);
    }

    /** @test */
    public function member_emails_must_be_valid_email_addresses()
    {
        $this->actingAs($this->admin);
        
        $memberData = [
            'name' => 'Test Company',
            'address' => 'Test Address',
            'emails' => [
                '<EMAIL>',
                'invalid-email',
                '<EMAIL>'
            ],
            'sort_order' => 1
        ];
        
        $response = $this->post(route('admin.members.store'), $memberData);
        
        $response->assertSessionHasErrors(['emails.1']);
    }

    /** @test */
    public function member_website_must_be_valid_url()
    {
        $this->actingAs($this->admin);
        
        $memberData = [
            'name' => 'Test Company',
            'address' => 'Test Address',
            'website' => 'not-a-valid-url',
            'sort_order' => 1
        ];
        
        $response = $this->post(route('admin.members.store'), $memberData);
        
        $response->assertSessionHasErrors(['website']);
    }

    /** @test */
    public function member_contact_persons_require_name_when_provided()
    {
        $this->actingAs($this->admin);

        $memberData = [
            'name' => 'Test Company',
            'address' => 'Test Address',
            'contact_persons' => [
                ['name' => '', 'phone' => '+91-1234567890']
            ],
            'sort_order' => 1
        ];

        $response = $this->post(route('admin.members.store'), $memberData);

        $response->assertSessionHasErrors(['contact_persons.0.name']);
    }

    /** @test */
    public function member_can_add_email_using_model_method()
    {
        $member = Member::create([
            'name' => 'Test Company',
            'address' => 'Test Address',
            'emails' => ['<EMAIL>'],
            'sort_order' => 1
        ]);

        $member->addEmail('<EMAIL>');
        $member->save();

        $this->assertContains('<EMAIL>', $member->emails);
        $this->assertContains('<EMAIL>', $member->emails);
        $this->assertCount(2, $member->emails);
    }

    /** @test */
    public function member_does_not_add_duplicate_email()
    {
        $member = Member::create([
            'name' => 'Test Company',
            'address' => 'Test Address',
            'emails' => ['<EMAIL>'],
            'sort_order' => 1
        ]);

        $member->addEmail('<EMAIL>');
        $member->save();

        $this->assertCount(1, $member->emails);
        $this->assertEquals(['<EMAIL>'], $member->emails);
    }

    /** @test */
    public function member_can_remove_email_using_model_method()
    {
        $member = Member::create([
            'name' => 'Test Company',
            'address' => 'Test Address',
            'emails' => ['<EMAIL>', '<EMAIL>'],
            'sort_order' => 1
        ]);

        $member->removeEmail('<EMAIL>');
        $member->save();

        $this->assertContains('<EMAIL>', $member->emails);
        $this->assertNotContains('<EMAIL>', $member->emails);
        $this->assertCount(1, $member->emails);
    }

    /** @test */
    public function member_can_add_phone_using_model_method()
    {
        $member = Member::create([
            'name' => 'Test Company',
            'address' => 'Test Address',
            'phones' => ['+91-1111111111'],
            'sort_order' => 1
        ]);

        $member->addPhone('+91-2222222222');
        $member->save();

        $this->assertContains('+91-1111111111', $member->phones);
        $this->assertContains('+91-2222222222', $member->phones);
        $this->assertCount(2, $member->phones);
    }

    /** @test */
    public function member_does_not_add_duplicate_phone()
    {
        $member = Member::create([
            'name' => 'Test Company',
            'address' => 'Test Address',
            'phones' => ['+91-1111111111'],
            'sort_order' => 1
        ]);

        $member->addPhone('+91-1111111111');
        $member->save();

        $this->assertCount(1, $member->phones);
        $this->assertEquals(['+91-1111111111'], $member->phones);
    }

    /** @test */
    public function member_can_remove_phone_using_model_method()
    {
        $member = Member::create([
            'name' => 'Test Company',
            'address' => 'Test Address',
            'phones' => ['+91-1111111111', '+91-2222222222'],
            'sort_order' => 1
        ]);

        $member->removePhone('+91-2222222222');
        $member->save();

        $this->assertContains('+91-1111111111', $member->phones);
        $this->assertNotContains('+91-2222222222', $member->phones);
        $this->assertCount(1, $member->phones);
    }

    /** @test */
    public function member_active_scope_filters_active_members()
    {
        Member::create([
            'name' => 'Active Company',
            'address' => 'Active Address',
            'is_active' => true,
            'sort_order' => 1
        ]);

        Member::create([
            'name' => 'Inactive Company',
            'address' => 'Inactive Address',
            'is_active' => false,
            'sort_order' => 2
        ]);

        $activeMembers = Member::active()->get();

        $this->assertCount(1, $activeMembers);
        $this->assertEquals('Active Company', $activeMembers->first()->name);
    }

    /** @test */
    public function member_ordered_scope_orders_by_sort_order()
    {
        Member::create([
            'name' => 'Third Company',
            'address' => 'Third Address',
            'sort_order' => 3
        ]);

        Member::create([
            'name' => 'First Company',
            'address' => 'First Address',
            'sort_order' => 1
        ]);

        Member::create([
            'name' => 'Second Company',
            'address' => 'Second Address',
            'sort_order' => 2
        ]);

        $orderedMembers = Member::ordered()->get();

        $this->assertEquals('First Company', $orderedMembers->get(0)->name);
        $this->assertEquals('Second Company', $orderedMembers->get(1)->name);
        $this->assertEquals('Third Company', $orderedMembers->get(2)->name);
    }
}
