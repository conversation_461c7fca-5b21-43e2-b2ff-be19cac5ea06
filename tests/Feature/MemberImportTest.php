<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;
use App\Models\Member;
use App\Models\User;
use App\Imports\MembersImport;
use Maatwebsite\Excel\Facades\Excel;

class MemberImportTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $admin;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create an admin user for testing
        $this->admin = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password')
        ]);
    }

    /** @test */
    public function admin_can_view_import_form()
    {
        $this->actingAs($this->admin);
        
        $response = $this->get(route('admin.members.index'));
        
        $response->assertStatus(200);
        $response->assertSee('Import Members');
    }

    /** @test */
    public function admin_can_import_members_with_single_contacts()
    {
        $this->actingAs($this->admin);
        Storage::fake('local');

        // Create a CSV content with single email and phone
        $csvContent = "name,address,full_address,description,categories,emails,phones,website,contact_persons,is_active,sort_order\n";
        $csvContent .= "Test Company 1,Test Address 1,Full Address 1,Description 1,Manufacturing,<EMAIL>,+91-1111111111,https://test1.com,John Doe:+91-1111111112,1,1\n";
        $csvContent .= "Test Company 2,Test Address 2,Full Address 2,Description 2,Export,<EMAIL>,+91-2222222222,https://test2.com,Jane Doe:+91-2222222223,1,2";

        $file = UploadedFile::fake()->createWithContent('members.csv', $csvContent);

        Excel::fake();

        $response = $this->post(route('admin.members.import'), [
            'file' => $file
        ]);

        Excel::assertImported('members.csv', function (MembersImport $import) {
            return true;
        });

        $response->assertRedirect();
        $response->assertSessionHas('success');
    }

    /** @test */
    public function admin_can_import_members_with_multiple_contacts()
    {
        $this->actingAs($this->admin);
        Storage::fake('local');

        // Create a CSV content with multiple emails and phones
        $csvContent = "name,address,full_address,description,categories,emails,phones,website,contact_persons,is_active,sort_order\n";
        $csvContent .= "Multi Contact Company,Multi Address,Full Multi Address,Multi Description,Manufacturing;Export,<EMAIL>;<EMAIL>,+91-1111111111;+91-2222222222,https://multi.com,John Doe:+91-1111111113;Jane Doe:+91-2222222224,1,1";

        $file = UploadedFile::fake()->createWithContent('members.csv', $csvContent);

        Excel::fake();

        $response = $this->post(route('admin.members.import'), [
            'file' => $file
        ]);

        Excel::assertImported('members.csv', function (MembersImport $import) {
            return true;
        });

        $response->assertRedirect();
        $response->assertSessionHas('success');
    }

    /** @test */
    public function import_requires_csv_file()
    {
        $this->actingAs($this->admin);

        $response = $this->post(route('admin.members.import'), []);

        $response->assertSessionHasErrors(['file']);
    }

    /** @test */
    public function import_file_must_be_csv()
    {
        $this->actingAs($this->admin);
        Storage::fake('local');

        $file = UploadedFile::fake()->create('members.txt', 100);

        $response = $this->post(route('admin.members.import'), [
            'file' => $file
        ]);

        $response->assertSessionHasErrors(['file']);
    }

    /** @test */
    public function members_import_class_processes_single_contacts_correctly()
    {
        $rowData = [
            'name' => 'Test Company',
            'address' => 'Test Address',
            'full_address' => 'Full Test Address',
            'description' => 'Test Description',
            'categories' => 'Manufacturing,Export',
            'emails' => '<EMAIL>',
            'phones' => '+91-1111111111',
            'website' => 'https://test.com',
            'contact_persons' => 'John Doe,+91-1111111112',
            'is_active' => '1',
            'sort_order' => '1'
        ];

        $import = new MembersImport();
        $member = $import->model($rowData);

        $this->assertEquals('Test Company', $member->name);
        $this->assertEquals('Test Address', $member->address);
        $this->assertEquals(['<EMAIL>'], $member->emails);
        $this->assertEquals(['+91-1111111111'], $member->phones);
        $this->assertEquals('https://test.com', $member->website);
        $this->assertEquals([['name' => 'John Doe', 'phone' => '+91-1111111112']], $member->contact_persons);
        $this->assertTrue($member->is_active);
        $this->assertEquals(1, $member->sort_order);
    }

    /** @test */
    public function members_import_class_processes_multiple_contacts_correctly()
    {
        $rowData = [
            'name' => 'Multi Contact Company',
            'address' => 'Multi Address',
            'full_address' => 'Full Multi Address',
            'description' => 'Multi Description',
            'categories' => 'Manufacturing,Export,Trading',
            'emails' => '<EMAIL>,<EMAIL>,<EMAIL>',
            'phones' => '+91-1111111111,+91-2222222222,0771-3333333',
            'website' => 'https://multi.com',
            'contact_persons' => 'John Doe,+91-1111111113;Jane Doe,+91-2222222224;Bob Smith,+91-3333333335',
            'is_active' => '1',
            'sort_order' => '2'
        ];

        $import = new MembersImport();
        $member = $import->model($rowData);

        $this->assertEquals('Multi Contact Company', $member->name);
        $this->assertEquals(['Manufacturing', 'Export', 'Trading'], $member->categories);
        $this->assertEquals(['<EMAIL>', '<EMAIL>', '<EMAIL>'], $member->emails);
        $this->assertEquals(['+91-1111111111', '+91-2222222222', '0771-3333333'], $member->phones);
        $this->assertCount(3, $member->contact_persons);
        $this->assertEquals('John Doe', $member->contact_persons[0]['name']);
        $this->assertEquals('+91-1111111113', $member->contact_persons[0]['phone']);
        $this->assertEquals('Jane Doe', $member->contact_persons[1]['name']);
        $this->assertEquals('+91-2222222224', $member->contact_persons[1]['phone']);
        $this->assertEquals('Bob Smith', $member->contact_persons[2]['name']);
        $this->assertEquals('+91-3333333335', $member->contact_persons[2]['phone']);
    }

    /** @test */
    public function members_import_handles_empty_optional_fields()
    {
        $rowData = [
            'name' => 'Minimal Company',
            'address' => 'Minimal Address',
            'full_address' => '',
            'description' => '',
            'categories' => '',
            'emails' => '',
            'phones' => '',
            'website' => '',
            'contact_persons' => '',
            'is_active' => '',
            'sort_order' => ''
        ];

        $import = new MembersImport();
        $member = $import->model($rowData);

        $this->assertEquals('Minimal Company', $member->name);
        $this->assertEquals('Minimal Address', $member->address);
        $this->assertEquals('', $member->full_address); // Empty string, not null
        $this->assertEquals('', $member->description); // Empty string, not null
        $this->assertNull($member->emails); // Null when empty
        $this->assertNull($member->phones); // Null when empty
        $this->assertNull($member->website);
        $this->assertNull($member->contact_persons); // Null when empty
        $this->assertFalse($member->is_active); // Empty string becomes false
        $this->assertEquals(0, $member->sort_order); // Default value
    }

    /** @test */
    public function members_import_handles_mixed_separators_gracefully()
    {
        $rowData = [
            'name' => 'Mixed Separators Company',
            'address' => 'Mixed Address',
            'categories' => 'Manufacturing, Export , Trading', // Mixed spaces with commas
            'emails' => '<EMAIL> , <EMAIL>, <EMAIL>', // Mixed spaces with commas
            'phones' => '+91-1111111111,+91-2222222222 , +91-3333333333', // Mixed spaces with commas
            'contact_persons' => 'John Doe,+91-1111111113 ; Jane Doe,+91-2222222224', // Mixed spaces with semicolon for contacts
            'is_active' => '1',
            'sort_order' => '1'
        ];

        $import = new MembersImport();
        $member = $import->model($rowData);

        $this->assertEquals(['Manufacturing', 'Export', 'Trading'], $member->categories);
        $this->assertEquals(['<EMAIL>', '<EMAIL>', '<EMAIL>'], $member->emails);
        $this->assertEquals(['+91-1111111111', '+91-2222222222', '+91-3333333333'], $member->phones);
        $this->assertCount(2, $member->contact_persons);
        $this->assertEquals('John Doe', $member->contact_persons[0]['name']);
        $this->assertEquals('Jane Doe', $member->contact_persons[1]['name']);
    }

    /** @test */
    public function members_import_filters_empty_values()
    {
        $rowData = [
            'name' => 'Filter Test Company',
            'address' => 'Filter Address',
            'categories' => 'Manufacturing,,Export,', // Empty values with commas
            'emails' => '<EMAIL>,,<EMAIL>,', // Empty values with commas
            'phones' => '+91-1111111111,,+91-2222222222,', // Empty values with commas
            'contact_persons' => 'John Doe,+91-1111111113;;Jane Doe,+91-2222222224;', // Empty values with semicolons for contacts
            'is_active' => '1',
            'sort_order' => '1'
        ];

        $import = new MembersImport();
        $member = $import->model($rowData);

        $this->assertEquals(['Manufacturing', 'Export'], $member->categories);
        $this->assertEquals(['<EMAIL>', '<EMAIL>'], $member->emails);
        $this->assertEquals(['+91-1111111111', '+91-2222222222'], $member->phones);
        $this->assertCount(2, $member->contact_persons);
    }
}
