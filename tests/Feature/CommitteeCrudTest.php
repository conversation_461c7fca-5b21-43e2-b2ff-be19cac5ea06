<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;
use App\Models\Committee;
use App\Models\User;

class CommitteeCrudTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $admin;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create an admin user for testing
        $this->admin = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password')
        ]);
    }

    /** @test */
    public function admin_can_view_committees_index()
    {
        $this->actingAs($this->admin);
        
        $response = $this->get(route('admin.committees.index'));
        
        $response->assertStatus(200);
        $response->assertViewIs('admin.committees.index');
    }

    /** @test */
    public function admin_can_view_create_committee_form()
    {
        $this->actingAs($this->admin);
        
        $response = $this->get(route('admin.committees.create'));
        
        $response->assertStatus(200);
        $response->assertViewIs('admin.committees.create');
    }

    /** @test */
    public function admin_can_create_committee_member()
    {
        $this->actingAs($this->admin);
        Storage::fake('public');
        
        $image = UploadedFile::fake()->image('committee-member.jpg');
        
        $committeeData = [
            'name' => 'John Doe',
            'position' => 'Chairman',
            'image' => $image,
            'sort_order' => 1,
            'is_active' => true
        ];
        
        $response = $this->post(route('admin.committees.store'), $committeeData);
        
        $response->assertRedirect();
        $response->assertSessionHas('success');
        
        $this->assertDatabaseHas('committees', [
            'name' => 'John Doe',
            'position' => 'Chairman',
            'sort_order' => 1,
            'is_active' => true
        ]);
        
        Storage::disk('public')->assertExists('committees/' . $image->hashName());
    }

    /** @test */
    public function admin_can_view_committee_member()
    {
        $this->actingAs($this->admin);
        
        $committee = Committee::factory()->create();
        
        $response = $this->get(route('admin.committees.show', $committee));
        
        $response->assertStatus(200);
        $response->assertViewIs('admin.committees.show');
        $response->assertViewHas('committee', $committee);
    }

    /** @test */
    public function admin_can_view_edit_committee_form()
    {
        $this->actingAs($this->admin);
        
        $committee = Committee::factory()->create();
        
        $response = $this->get(route('admin.committees.edit', $committee));
        
        $response->assertStatus(200);
        $response->assertViewIs('admin.committees.edit');
        $response->assertViewHas('committee', $committee);
    }

    /** @test */
    public function admin_can_update_committee_member()
    {
        $this->actingAs($this->admin);
        
        $committee = Committee::factory()->create([
            'name' => 'Original Name',
            'position' => 'Original Position'
        ]);
        
        $updateData = [
            'name' => 'Updated Name',
            'position' => 'Updated Position',
            'sort_order' => 2,
            'is_active' => false
        ];
        
        $response = $this->put(route('admin.committees.update', $committee), $updateData);
        
        $response->assertRedirect();
        $response->assertSessionHas('success');
        
        $this->assertDatabaseHas('committees', [
            'id' => $committee->id,
            'name' => 'Updated Name',
            'position' => 'Updated Position',
            'sort_order' => 2,
            'is_active' => false
        ]);
    }

    /** @test */
    public function admin_can_toggle_committee_active_status()
    {
        $this->actingAs($this->admin);
        
        $committee = Committee::factory()->create(['is_active' => true]);
        
        $response = $this->patch(route('admin.committees.toggle-active', $committee));
        
        $response->assertRedirect();
        $response->assertSessionHas('success');
        
        $this->assertDatabaseHas('committees', [
            'id' => $committee->id,
            'is_active' => false
        ]);
    }

    /** @test */
    public function admin_can_delete_committee_member()
    {
        $this->actingAs($this->admin);
        Storage::fake('public');
        
        $committee = Committee::factory()->create([
            'image_path' => 'committees/test-image.jpg'
        ]);
        
        // Create a fake file to test deletion
        Storage::disk('public')->put('committees/test-image.jpg', 'fake content');
        
        $response = $this->delete(route('admin.committees.destroy', $committee));
        
        $response->assertRedirect(route('admin.committees.index'));
        $response->assertSessionHas('success');
        
        $this->assertDatabaseMissing('committees', [
            'id' => $committee->id
        ]);
        
        Storage::disk('public')->assertMissing('committees/test-image.jpg');
    }

    /** @test */
    public function committee_creation_requires_name_and_position()
    {
        $this->actingAs($this->admin);
        
        $response = $this->post(route('admin.committees.store'), []);
        
        $response->assertSessionHasErrors(['name', 'position']);
    }

    /** @test */
    public function committee_image_must_be_valid_image_file()
    {
        $this->actingAs($this->admin);
        Storage::fake('public');
        
        $invalidFile = UploadedFile::fake()->create('document.pdf', 1000);
        
        $response = $this->post(route('admin.committees.store'), [
            'name' => 'John Doe',
            'position' => 'Chairman',
            'image' => $invalidFile
        ]);
        
        $response->assertSessionHasErrors(['image']);
    }
}
